<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlogCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'cover_image',
        'banner_image',
        'alt_text_cover_image',
        'alt_text_banner_image',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'page_h1_heading',
        'slug',
    ];

    public function blogs()
    {
        return $this->hasMany(Blog::class);
    }
}