<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BlogComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'status',
        'message',
        'blog_id',
        'user_id',
    ];

    public function blog()
    {
        return $this->belongsTo(Blog::class, 'blog_id');
    }

}
