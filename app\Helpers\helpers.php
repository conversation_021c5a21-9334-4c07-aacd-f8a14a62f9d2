<?php

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

// function media($file, $path)
// {
//     $imageName = time() . '.' . $file->extension();
//     $file->move(public_path($path), $imageName);
//     return $imageName;
// }


function media($file, $path)
{
    $extension = strtolower($file->getClientOriginalExtension());

    // Get original name without extension
    $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);

    // Slugify the original name (e.g., "Gold Necklace Set" => "gold-necklace-set")
    $slugifiedName = Str::slug($originalName);
    $imageName = $slugifiedName . '.webp';
    $fullPath = public_path($path) . '/' . $imageName;

    // Ensure filename is unique (append short suffix only if file exists)
    if (file_exists($fullPath)) {
        $imageName = $slugifiedName . '-' . Str::random(3) . '.webp';
        $fullPath = public_path($path) . '/' . $imageName;
    }

    // Handle image creation based on extension
    switch ($extension) {
        case 'jpeg':
        case 'jpg':
            $image = imagecreatefromjpeg($file->getPathname());
            break;
        case 'png':
            $image = imagecreatefrompng($file->getPathname());
            // Preserve transparency
            imagepalettetotruecolor($image);
            imagealphablending($image, true);
            imagesavealpha($image, true);
            break;
        default:
            // Unsupported format: save original without WebP conversion
            $fallbackName = $slugifiedName . '.' . $extension;
            $fallbackPath = public_path($path) . '/' . $fallbackName;
            if (file_exists($fallbackPath)) {
                $fallbackName = $slugifiedName . '-' . Str::random(3) . '.' . $extension;
            }
            $file->move(public_path($path), $fallbackName);
            return $fallbackName;
    }

    // Save image as WebP with quality 80
    imagewebp($image, $fullPath, 80);
    imagedestroy($image);

    return $imageName;
}


function customDate($date, $format, $to_date = null)
{
    return Carbon::parse($date)->format($format);
}
function customTime($time, $format)
{
    return Carbon::parse($time)->format($format);
}

function getRangeDate($date = null, $type = null, $days = 15)
{
    if (!$date && $type == 'stats') {
        $end = Carbon::now()->addDay();
        $start = Carbon::now()->subDays($days);
        return [$start, $end];
    }

    $date = explode(' to ', $date);

    if ($type == 'stats' && isset($date[0]) && !isset($date[1])) {
        $start = Carbon::parse($date[0])->subdays($days);
        return [$start, $date[0]];
    }
    return [
        isset($date[0]) && $date[0] != '' ? $date[0] : null,
        isset($date[1]) ? $date[1] : null,
    ];
}

function getTags($tag, $eli)
{
    $tag = str_replace(', ', $eli, $tag);
    return explode($eli, $tag);
}
function sendMail($data)
{
     

    return $mail = Mail::send($data['view'], ['data' => $data['data']], function ($message) use ($data) {
        $message->to($data['to'])
            ->from(config('helpers.mail.from_address'), config('helpers.mail.from_name'))
            ->subject($data['subject']);
    });


}

function getAdmin()
{
    return User::where('role', 'admin')->first();
}
