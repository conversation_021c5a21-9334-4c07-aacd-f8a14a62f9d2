<?php

namespace App\Http\Controllers\website\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthenticatedSessionController extends Controller
{

    /**
     * Handle an incoming authentication request.
     */

    // public function store(LoginRequest $request)
    // {

    //     $request->authenticate();
    //     $request->session()->regenerate();

    //     if($request->ajax())
    //     {
    //         return response()->json(['success' => 'Successfully Login', 'route' => route('home')]);
    //     }

    //     return redirect()->intended(RouteServiceProvider::HOME);
    // }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
