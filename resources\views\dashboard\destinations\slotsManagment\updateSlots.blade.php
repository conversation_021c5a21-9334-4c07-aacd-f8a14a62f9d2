<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasStoreSlots" aria-labelledby="offcanvasStoreSlotsLabel">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 id="offcanvasStoreSlotsLabel" class="offcanvas-title">
            Update Slots
        </h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="store_slots">
            @csrf
            <input type="hidden" value="{{ $destination->id }}" name="destination_id">
            <div class="d-grid gap-3 w-100">
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="locations">Destination Date</label>
                    <select class="custom_form_field" name="date" id="date">
                        <option selected disabled>Select date</option>
                        @if(isset($slotsData))
                        @foreach ($slotsData as $date => $slots)
                        @php
                        $formattedDate = customDate($date, 'F d, Y');
                        $isExpired = now()->greaterThan(Carbon\Carbon::parse($date));
                        @endphp
                        @if(!($isExpired))
                        <option value="{{$formattedDate}}">{{$formattedDate}}</option>
                        @endif
                        @endforeach
                        @else{
                        <p>No slot data available</p>
                        <option>No slot data available</option>
                        }
                        @endif

                    </select>
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="slots">Slots</label>
                    <input class="custom_form_field" type="number" min="1" value="1" name="slots" id="slots" />
                </div>
                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button type="submit" class="custom_btn_2">Add
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>
        </form>
    </div>
</div>
