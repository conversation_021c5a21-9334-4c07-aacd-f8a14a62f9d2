document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_contact_category")) {
        const contactId = event.target.getAttribute("data-id");
        console.log(contactId);
        document
            .querySelector("#contact_confirmDeleteBtn")
            .setAttribute("data-id", contactId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#contact_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/contact/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Contact record deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_contact_category")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".category_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $(".reply_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                message: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/contact/reply/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasReply" + id)
                        );
                        offcanvas.hide();
                        showAlert("Message sent successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);

                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    },
                });
            },
        });
    });
});
