function showAlert(message, type) {
    var alertElement = $("#alertMessage");
    var alertContent = $("#alertContent");
    alertContent.text(message);

    if (type === "success") {
        alertElement.addClass("alert-success show");
        setTimeout(function () {
            alertElement.removeClass("alert-success show");
        }, 3000);
    } else if (type === "error") {
        alertElement.addClass("alert-danger show");
        setTimeout(function () {
            alertElement.removeClass("alert-danger show");
        }, 3000);
    }
}

$(document).ready(function () {
    $(".page_h1_heading").on("input", function () {
        $(".h1_headingCharCount").text($(this).val().length);
    });
    $(".meta_title").on("input", function () {
        $(".meta_titleCharCount").text($(this).val().length);
    });
    $(".meta_description").on("input", function () {
        $(".meta_descriptionCharCount").text($(this).val().length);
    });
});

$(document).ready(function () {
    $(".toggle-button").click(function () {
        $(".actions").not($(this).siblings(".actions")).hide();
        var actions = $(this).siblings(".actions");
        actions.toggle(); // Toggle the visibility of the actions div
    });
});
