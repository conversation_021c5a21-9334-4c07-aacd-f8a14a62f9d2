@extends("dashboard.include.layout")


@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a> / blog / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <a href="{{route('dashboard.blogs.add')}}" class="custom_btn_2 d-flex align-items-center "><span>Add
                    Blog</span></a>
        </div>
        <table id="blog_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">Status</th>
                    <th style="min-width: 100px;">Popular Status</th>
                    <th style="min-width: 160px;">Cover Image</th>
                    <th style="min-width: 330px;">Title</th>
                    <th style="min-width: 100px;">Type</th>
                    <th style="min-width: 120px;">Category</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($blogList as $bloglist)
                <tr class="blog_row">
                    <td></td>
                    <td>
                        <label class="toogle_switch mb-3">
                            <input type="checkbox" class="blog_switch_input" {{ $bloglist->status==1 ? "checked" : ""}}
                                data-id="{{ $bloglist->id }}">
                            <span class="agent_switch switch round" data-id="{{ $bloglist->id }}"></span>
                        </label>
                    </td>
                    <td>
                        <label class="toogle_switch mb-3">
                            <input type="checkbox" class="blog_popular_switch_input"
                                {{ $bloglist->popular==1 ? "checked" : ""}} data-id="{{ $bloglist->id }}">
                            <span class="agent_switch switch round" data-id="{{ $bloglist->id }}"></span>
                        </label>
                    </td>
                    <td>
                        <div style="width: 2.375rem;
                      height: 2.375rem;">
                            <img src="{{$bloglist ? asset('storage/blog/'.$bloglist->cover_image) : ''}}"
                                class="w-100 h-100">
                        </div>
                    </td>

                    <td>{{ $bloglist->title }}</td>
                    <td>{{ $bloglist->type }}</td>
                    <td>{{ $bloglist->category->title ?? '' }}</td>
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $bloglist->id }}" type="button" class="delete_btn delete_blog"><i
                                        class="fas fa-trash-alt"></i>Delete</button>
                                <a href="{{ route('dashboard.blogs.update', $bloglist->id) }}"
                                    class="update_btn edit_btn update_blog"><i class="far fa-edit"></i>Update</a>

                            </div>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</div>

@include("dashboard.blogs.delete")

@endsection