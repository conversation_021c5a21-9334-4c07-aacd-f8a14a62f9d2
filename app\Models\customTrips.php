<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class customTrips extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'phone',
        'country',
        'city',
        'areas',
        'total_participant',
        'total_adults',
        'total_children',
        'total_days',
        'dates',
        'message',
        'total_price',
        'advance_price',
        'destination_details',
        'total_rooms'

    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
