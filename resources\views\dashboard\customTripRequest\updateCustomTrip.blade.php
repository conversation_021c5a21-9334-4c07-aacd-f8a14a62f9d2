@extends("dashboard.include.layout")


@section("wrapper")


<div class="pagination-list d-flex w-100">
    <a> / Custom Trip / Booking / Update</a>
</div>


<form id="update_custom_trip" method="post" enctype="multipart/form-data">
    <input type="hidden" name="id" value="{{ $custom->id }}">
    <div class="d-flex mb-3 justify-content-end">
        <button type="submit" class="custom_btn_1">update</button>
    </div>

    @csrf
    <div class="add_destination_main">


        <div class="add_destination_box_one d-flex flex-column">
            <div class="destination_single_box">

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Booking information</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Status</label>
                            <select name="status" class="custom_form_field">
                                <option value="active">Active</option>
                            </select>

                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Total Days</label>
                            <input type="number" min="0" id="total_days" class="custom_form_field" placeholder="Enter tickets"
                                name="total_days" value={{$custom->total_days}}>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Total Participant</label>
                            <input type="number" min="0" id="total_participant" class="custom_form_field" placeholder="Enter tickets"
                                name="total_participant" value={{$custom->total_participant}}>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Total Adults</label>
                            <input type="number" min="0" id="total_adults" class="custom_form_field" placeholder="Enter total adults"
                                name="total_adults" value={{$custom->total_adults}}>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Total Children</label>
                            <input type="number" min="0" id="total_children" class="custom_form_field" placeholder="Enter total children"
                                name="total_children" value={{$custom->total_children}}>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Total Room</label>
                            <input type="number" min="0" id="total_rooms" class="custom_form_field" placeholder="Enter rooms"
                                name="total_rooms" value={{$custom->total_rooms}}>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Select Country</label>
                            <select class="custom_multi_select_dropdown" data-control="select2"
                                data-placeholder="Select Country" data-allow-clear="false" multiple="multiple"
                                name="country[]" id="country">

                                <?php $uniqueCountries = $destinationCountryLocation->pluck('country')->unique()->toArray(); ?>

                                @foreach ($uniqueCountries as $country)
                                <option value="{{ $country }}">{{ $country }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Select City</label>
                            <select id="city" class="custom_multi_select_dropdown custom_form_field city" data-control="select2"
                                data-placeholder="Select city" data-allow-clear="false" multiple="multiple"
                                name="city[]">
                                <?php $displayedCountries = []; ?>
                                @foreach ($destinationCountryLocation as $location)
                                @if (!in_array($location['country'], $displayedCountries))
                                <?php $displayedCountries[] = $location['country']; ?>
                                <optgroup label="{{ $location['country'] }}">
                                    @endif
                                    <option value="{{$location['city'] }}">
                                        {{ $location['city'] }}
                                    </option>

                                    @if (!in_array($location['country'], $displayedCountries))
                                </optgroup>
                                @endif
                                @endforeach
                            </select>

                        </div>


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Select areas</label>
                            <select class="custom_multi_select_dropdown destination_list" data-control="select2"
                                data-placeholder="Select destination or places" data-allow-clear="false"
                                multiple="multiple" name="destination_list[]" id="destination_list">

                                @foreach ($destinationCountryLocation as $city => $cityLocations)
                                <optgroup label="{{ $cityLocations['country'] }} - {{ $cityLocations['city'] }}">
                                    <?php $locations = json_decode($cityLocations['locations'], true); ?>

                                    @foreach ($locations as $cityareas)
                                    <option
                                        value="{{ $cityLocations['country'] . '-' . $cityLocations['city'] . '-' . $cityareas['value'] }}">
                                        {{ $cityareas['value'] }}
                                    </option>
                                    @endforeach
                                </optgroup>
                                @endforeach
                            </select>

                        </div>


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Dates range</label>
                            <input placeholder="Select the start to end date" id="custom_package_date_range"
                                name="from_to_date">
                        </div>

                    </div>

                </div>
            </div>
        </div>

    </div>
</form>

@endsection