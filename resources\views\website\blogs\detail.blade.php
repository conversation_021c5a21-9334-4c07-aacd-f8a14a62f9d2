@extends('website.include.layout')
@section('title', 'blog | ' . $blog->meta_title ?? 'Default Meta Title')

@section('meta_title', $blog->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $blog->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $blog->meta_description ?? 'Default Meta Description')
@section('h1', $blog->page_h1_heading ?? 'Default H1 Heading')

@push('css')

@endpush
@section('content')



<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('<?php echo url('storage/blog/' . $blog->banner_image); ?>');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">
                <h2>{{$blog->title}}</h2>
                <p>{{$blog->short_description}}</p>
            </div>
        </div>
    </div>
</section>


<div class="single_article_parent d-inline-block w-100">
    <div class="custom_container">
        <div class="single_article_box">

            <div class="single_article_data d-flex flex-column w-100">
                <div class="single_article_bread_crums d-flex w-100 align-items-center flex-wrap">
                    <a href="{{route('home')}}">Home</a><i class="fas fa-angle-right"></i><a
                        href="{{route('website.blogs.index')}}"> Blog</a> <i
                        class="fas fa-angle-right"></i><a>{{$blog->title}}</a>
                </div>
                @php($tags = json_decode($blog->tags, true))

                <div class="single_article_category d-flex w-100 align-items-center flex-wrap">
                    @foreach($tags as $tag)
                    <span>{{$tag['value']}}</span>
                    @endforeach
                </div>
                <div class="single_article_heading d-flex w-100 align-items-center">
                    <h3>{{$blog->title}}</h3>
                </div>
                <div class="single_article_short_detail d-flex align-items-center w-100 justify-content-between">
                    <div class="single_article_short_detial_left d-flex align-items-center">
                        <figure class="d-flex w-100 align-items-center">
                            <img  class="object-fit-cover" src="{{asset('storage/userImages/'.$blog->user->profile_image)}}" alt="author image" width="40" height="40"  loading="lazy" decoding="async"  />


                            <span>By {{$blog->author_name}}</span>
                        </figure>
                        <small>{{customDate($blog->created_at,'F d, Y')}}</small>
                    </div>
                    <div class="single_article_short_detial_right d-flex align-items-center">
                        <i class="fas fa-envelope"></i>
                        <small>{{$blog->comments->count()}}</small>
                    </div>
                </div>
                <div class="article_share_button d-flex align-items-center">
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(url()->current()) }}"
                        target="_blank" class="fb">
                        <i class="fab fa-facebook-f" aria-hidden="true"></i>
                    </a>
                    <a href="https://www.instagram.com" target="_blank" class="insta">
                        <i class="fab fa-instagram" aria-hidden="true"></i>
                    </a>
                    <a href="https://wa.me/?text={{ urlencode(url()->current()) }}" target="_blank" class="whatsapp">
                        <i class="fab fa-whatsapp" aria-hidden="true"></i>
                    </a>
                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(url()->current()) }}" target="_blank"
                        class="twitter">
                        <i class="fab fa-twitter" aria-hidden="true"></i>
                    </a>
                </div>
                <div class="single_article_main_img d-flex w-100">
                    <figure class="d-flex w-100">
                        <img class="w-100 h-100 object-fit-cover"
                            src="{{ $blog->cover_image ? asset('storage/blog/'.$blog->cover_image) : asset('website/images/logo.png') }}"
                            alt="{{$blog->alt_text_cover_image}}" loading="lazy" decoding="async" width="800" height="530" />
                    </figure>
                </div>
                <div class="single_article_main_detail d-inline-block w-100">
                    <p>{!! $blog->description !!}</p>
                </div>
                <div class="single_article_tags d-flex w-100 align-items-center flex-wrap">
                    <div class="single_article_tags_heading">Tags</div>
                    @foreach($tags as $tag)
                    <span>{{$tag['value']}}</span>
                    @endforeach
                </div>
                @if($blog->comments->count() > 0 )
                <div class="single_article_comments_main d-flex w-100">
                    <div class="single_article_comments_list append_blog_list d-flex w-100 flex-column">
                        @foreach($blog->comments as $comment)
                        @if($comment->status == 1)
                        @include('website.blogs.appendComment')
                        @endif
                        @endforeach
                    </div>
                </div>
                @endif
                <div class="article_coment_section d-inline-block w-100">
                    <div class="article_comment_heading d-flex w-100">
                        <h4>LEAVE A REPLY</h4>
                    </div>
                    <section class="blog_section_one d-block w-100">

                        <form method="POST" id='submit_comment'>
                            @csrf
                            <div class="blog_page_form_content">

                                <div class="blog_form_fields">
                                    <span>Author Name</span>
                                    <input type="text" placeholder="Enter your full name" required="" name="name"
                                        value="" />

                                </div>
                                <div class="blog_form_fields">
                                    <span>Email</span>
                                    <input type="email" placeholder="Enter your email address" required="" name="email"
                                        value="" />

                                </div>
                                <div class="blog_form_fields">
                                    <span>Message</span>
                                    <textarea placeholder="Type your message here..." cols="20" rows="4" name="message"
                                        required></textarea>

                                </div>
                                <input type="hidden" name="blog_id" value="{{$blog->id}}" />


                                <div
                                    class="blog_form_submit_btn d-flex w-100 align-items-center justify-content-center">
                                    <button type="submit">
                                        Post Comment
                                    </button>
                                </div>
                            </div>
                        </form>

                    </section>
                </div>
            </div>


            <div class="single_article_sidebar">
                <div class="popular_article_box d-flex flex-column w-100">
                    <div class="popular_article_heading d-flex w-100 align-items-center justify-content-between">
                        <strong>MOST POPULAR</strong>
                    </div>
                    <div class="popular_article_list">
                        @if($popular_posts)
                        @foreach($popular_posts as $post)
                        <a class="popular_single_article" href="{{route('blog.detail',$post->slug)}}">
                            <figure class="w-100">
                                <img class="w-100 h-100 object-fit-cover"
                                    src="{{ $post->cover_image ? asset('storage/blog/'.$post->cover_image) : asset('website/images/logo.png') }}"
                                    alt="{{$post->alt_text_cover_image}}" loading="lazy" decoding="async" width="250" height="210" />

                            </figure>
                            <div class="popular_single_article_details d-flex w-100 flex-column">
                                <h3>{{$post->title}}</h3>
                                <div class="popular_single_article_inner_details d-flex align-items-center">
                                    <span>{{customDate($post->created_at,'F d, Y')}}</span>
                                    <small>{{$post->comments->count()}} Comments</small>
                                </div>
                                <strong>Read Post</strong>
                            </div>
                        </a>
                        @endforeach
                        @endif
                    </div>
                </div>
            </div>



        </div>

    </div>

</div>


<div class="related_blog_box d-inline-block w-100">
    <div class="custom_container">
        <div class="related_blog_header d-flex w-100 align-items-center justify-content-center w-100">
            <h4>Related Articles</h4>
        </div>
        <div class="related_blog_list related_blog_slider">
            @foreach($related as $blog)
            <div class="single_related_blog">
                <a href="{{route('blog.detail',$blog->slug)}}">
                    <div class="single_blog_box_figure">
                        <img class="w-100 h-100 object-fit-cover" src="{{ $blog->cover_image ? asset('storage/blog/'.$blog->cover_image) : asset('website/images/logo.png') }}"
                            alt="{{$blog->alt_text_cover_image}}" loading="lazy" decoding="async" width="250" height="210" />
                    </div>

                    <div class="single_blog_box d-flex flex-column">
                        <div class="single_blog_box_des_tags d-flex align-items-center">
                            <span>{{customDate($blog->created_at,'F d, Y')}}</span>
                            <small>{{$blog->comments->count()}} comments</small>
                        </div>
                        <h3>{{$blog->title}}</h3>
                        <p>{{$blog->short_description}}</p>
                        <strong>Read Post</strong>
                    </div>
                </a>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endsection

@push('js')

@endpush