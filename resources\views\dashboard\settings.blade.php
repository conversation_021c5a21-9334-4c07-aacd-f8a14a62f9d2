@extends('dashboard.include.layout')
@section('title', 'website settings')
@section("wrapper")

<div class="pagination-list d-flex w-100">
    <a> / Dashboard / website / settings </a>
</div>


<form id="update_global_data_form" method="post" enctype="multipart/form-data" novalidate="novalidate">
    <div class="d-flex mb-3 justify-content-end">

        <button type="submit" class="custom_btn_1">update</button>
    </div>
    @csrf
    <input type="hidden" name="id" value="{{$admindata->id}}">
    <div class="add_destination_main">


        <div class="add_destination_box_one d-flex flex-column">
            <div class="destination_single_box">
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Commision Detail</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="commission">Add commision on package (percentage)</label>
                            <input type="number" min='0' id="commission" class="custom_form_field"
                                placeholder="Enter commission in percentage" name="commission"
                                value="{{$admindata->commission_percentage}}">
                        </div>




                    </div>

                </div>
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Form recaptcha keys</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="visible_recaptcha_sitekey">Add visible recaptcha sitekey</label>
                            <input type="text" id="visible_recaptcha_sitekey" class="custom_form_field"
                                placeholder="Enter visible recaptcha sitekey" name="visible_recaptcha_sitekey"
                                value="{{$admindata->visible_recaptcha_sitekey}}">
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="invisible_recaptcha_sitekey">Add invisible recaptcha sitekey</label>
                            <input type="text" id="invisible_recaptcha_sitekey" class="custom_form_field"
                                placeholder="Enter invisible recaptcha sitekey" name="invisible_recaptcha_sitekey"
                                value="{{$admindata->invisible_recaptcha_sitekey}}">
                        </div>




                    </div>

                </div>
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Social Detail</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="facebook">Facebook</label>
                            <input type="text" id="facebook" class="custom_form_field" placeholder="Enter facebook link"
                                name="facebook" value="{{$admindata->facebook}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="instagram">Instagram</label>
                            <input type="text" id="instagram" class="custom_form_field"
                                placeholder="Enter instagram link" name="instagram" value="{{$admindata->instagram}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="twitter">Twitter</label>
                            <input type="text" id="twitter" class="custom_form_field" placeholder="Enter twitter link"
                                name="twitter" value="{{$admindata->twitter}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="youtube">Youtube</label>
                            <input type="text" id="youtube" class="custom_form_field" placeholder="Enter youtube link"
                                name="youtube" value="{{$admindata->youtube}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="tiktok">Tiktok</label>
                            <input type="text" id="tiktok" class="custom_form_field" placeholder="Enter tiktok link"
                                name="tiktok" value="{{$admindata->tiktok}}">
                        </div>




                    </div>

                </div>


            </div>
        </div>


        <div class="add_destination_box_two d-flex flex-column">
            <div class="destination_single_box">

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Email Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="primary_email">Primary Email</label>
                            <input type="email" id="primary_email" class="custom_form_field "
                                placeholder="Enter primary email" name="primary_email"
                                value="{{$admindata->primary_email}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="newsletter_email">Newsletter Email</label>
                            <input type="email" id="newsletter_email" class="custom_form_field "
                                placeholder="Enter primary email" name="newsletter_email"
                                value="{{$admindata->newsletter_email}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="contact_email">Contact Email</label>
                            <input type="email" id="contact_email" class="custom_form_field "
                                placeholder="Enter contact email" name="contact_email"
                                value="{{$admindata->contact_email}}">
                        </div>


                    </div>
                </div>


                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Contact Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="primary_phone_num">Primary phone number</label>
                            <input type="text" id="primary_phone_num" class="custom_form_field"
                                placeholder="Enter primary phone number" name="primary_phone_num"
                                value="{{$admindata->primary_no}}">
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="secondary_phone_num">Secondary phone number</label>
                            <input type="text" id="secondary_phone_num" class="custom_form_field"
                                placeholder="Enter secondary phone number" name="secondary_phone_num"
                                value="{{$admindata->secondary_no}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="address">Address</label>
                            <input type="text" id="address" class="custom_form_field" placeholder="Enter address"
                                name="address" value="{{$admindata->address}}">
                        </div>

                    </div>

                </div>



            </div>
        </div>

    </div>
</form>

@endsection
