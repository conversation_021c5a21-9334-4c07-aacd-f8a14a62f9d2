<?php

namespace App\Http\Controllers\website\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VerifyEmailController extends Controller
{
    /**
     * Mark the user's email address as verified.
     * This method works for both authenticated and unauthenticated users.
     */
    public function verify(Request $request, $id, $hash): RedirectResponse
    {
        // The signed middleware already validates the signature, so we can proceed

        // Find the user by ID from the URL
        $user = User::findOrFail($id);

        // Verify the hash matches the user's email
        if (!hash_equals((string) $hash, sha1($user->getEmailForVerification()))) {
            abort(403, 'Invalid verification link.');
        }

        // Check if email is already verified
        if ($user->hasVerifiedEmail()) {
            // If user is not logged in, log them in
            if (!Auth::check()) {
                Auth::login($user);
            }
            return redirect('/home')->with('status', 'Email already verified!');
        }

        // Mark email as verified
        if ($user->markEmailAsVerified()) {
            $user->update(['is_verified' => '1']);
            event(new Verified($user));
        }

        // Log the user in if they're not already logged in
        if (!Auth::check()) {
            Auth::login($user);
        }

        return redirect('/home')->with('status', 'Email verified successfully!');
    }
}