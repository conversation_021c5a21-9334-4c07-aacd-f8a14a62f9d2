@extends("dashboard.include.layout")


@section("wrapper")


<div class="pagination-list d-flex w-100">
    <a> / Destination / Edit Destination</a>
</div>

@php($user = auth()->user())

<form id="update_destination_form" method="post" enctype="multipart/form-data">

    <input type="hidden" name="id" value="{{ $destination->id }}">
    <div class="d-flex mb-3 justify-content-end">
        <button type="submit" class="custom_btn_1">update</button>
    </div>
    <div class="image_guide_list">
        <ul>
            <li>Upload Image must be less then 1 MB (10kb or 50 kb)</li>
            <li>Upload image of same dimension for all category and banners</li>
        </ul>
    </div>

    @csrf
    <div class="add_destination_main">


        <div class="add_destination_box_one d-flex flex-column">
            <div class="destination_single_box">

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Destination information</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">


                        <!-- <div class="form_field_box d-grid gap-2 w-100">
                            <label for="flag">Private Trips</label>
                            <select name="flag" class="custom_form_field" id="pricing_flag">
                                <option disabled {{ !isset($destination->flag) ? 'selected' : '' }}>Select status</option>
                                <option value="1" {{ (isset($destination->flag) && $destination->flag == 1) ? 'selected' : '' }}>Active</option>
                                <option value="0" {{ (isset($destination->flag) && $destination->flag == 0) ? 'selected' : '' }}>Disable</option>
                            </select>
                        </div>  -->

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Title</label>
                            <input type="text" id="title" class="custom_form_field" placeholder="Enter your Title"
                                name="title" value="{{ $destination->title }}">
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="banner_img">Banner Image (use dimension 2500px by 400px)</label>
                            <div class="d-grid img_preview_parent">
                                <div class="img_preview">
                                    <img src="{{$destination ? asset('storage/destinations/'.$destination->banner_image) : ''}}" />
                                </div>
                                <input id="banner_img" type="file" name="banner_img" class="dropify" data-max-file-size="2M"
                                    data-allowed-file-extensions="jpg jpeg png gif webp"
                                    data-default-file="{{$destination ? asset('storage/destinations/'.$destination->banner_image) : ''}}" />
                            </div>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="alt_text_banner_image">Alt Text of Banner Image</label>
                            <input class="custom_form_field" name="alt_text_banner_image" id="alt_text_banner_image"
                                value="{{ $destination->alt_text_banner_image }}" />
                        </div>


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="cover_img">Cover Image (use dimension 400px by 400px)</label>
                            <div class="d-grid img_preview_parent">
                                <div class="img_preview">
                                    <img src="{{$destination ? asset('storage/destinations/'.$destination->cover_img) : ''}}" />
                                </div>
                                <input id="cover_img" type="file" name="cover_img" class="dropify" data-max-file-size="2M"
                                    data-allowed-file-extensions="jpg jpeg png gif webp"
                                    data-default-file="{{$destination ? asset('storage/destinations/'.$destination->cover_img) : ''}}" />
                            </div>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="alt_text_cover_image">Alt Text of Cover Image</label>
                            <input class="custom_form_field" name="alt_text_cover_image" id="alt_text_cover_image"
                                value="{{ $destination->alt_text_cover_image }}" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="destination_pdf">Destination pdf ()</label>
                            <input id="destination_pdf" type="file" name="destination_pdf" class="dropify"
                                data-max-file-size="2M" data-allowed-file-extensions="jpg jpeg png pdf webp"
                                data-default-file="{{$destination ? asset('storage/destinationPdf/'.$destination->destination_pdf) : ''}}" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="short_description">Short Description</label>
                            <textarea id="destination_short_des" class="w-100" rows="25" required
                                name="short_description">{!!$destination ? $destination->short_description : ''!!}</textarea>
                        </div>


                    </div>

                </div>



                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Image Gallery (use dimension 400px by 400px)</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">

                            <div class="col-sm-10">
                                <div id="update_multi_image_picker" class="row">
                                    <input type="hidden" id="destinationId" data-value='{{ $destination->id }}'>
                                    @if(isset($destination->image_gallery))
                                    @foreach(json_decode($destination->image_gallery) as $index => $image)
                                    <div class="col-md-4 mb-3 position-relative" data-id="{{$index}}">
                                        <img src="{{ asset('storage/destinations/'.$image) }}" class="img-fluid h-100"
                                            alt="Gallery Image">
                                        <a href="javascript:void(0)"
                                            style="right: 3px;top: 3px;background: rgb(237, 60, 32);border-radius: 3px;width: 86px;height: 30px;line-height: 30px;text-align: center;text-decoration: none;color: rgb(255, 255, 255);position: absolute !important;"
                                            class="delete-image" data-image="{{ $image }}"
                                            data-id="{{ $index }}">Delete</a>
                                    </div>
                                    @endforeach
                                    @endif
                                </div>
                            </div>

                        </div>

                    </div>

                </div>



                <input type="hidden" id="update-editor-data" name="editor_data"
                    value="{{ $destination->destination_long_detail }}">
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Destinations</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">

                            <button type="button" class="custom_btn_1" id="add-editor-btn-update">Add Editor</button>

                        </div>

                        <div class="d-grid gap-3 w-100" id="update-editors-container">



                        </div>


                    </div>

                </div>


                <!-- new change -->

                <input type="hidden" id="update_accomodation_editor_data" name="accomodation_editor_data"
                    value="{{ $destination->destination_accomodation_detail }}">
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Accomodation</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">

                            <button type="button" class="custom_btn_1" id="add-accomodation-editor-btn-update">Add Accomodation Editor</button>

                        </div>

                        <div class="d-grid gap-3 w-100" id="update_accomodation_editors_container">



                        </div>


                    </div>

                </div>


                <!-- new change end -->



            </div>
        </div>


        <div class="add_destination_box_two d-flex flex-column">
            <div class="destination_single_box">




                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Category and Type</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="category">Category</label>

                            <select id="category" name="category" class="custom_form_field">
                                @foreach($destinationCategories as $category)
                                <option value="{{ $category->id }}"
                                    {{ $category->id == $destination->category_id ? 'selected' : '' }}>
                                    {{ $category->title }}
                                </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="type">Type</label>
                            <select id="type" name="type" class="custom_form_field">
                                @foreach($destinationTypes as $type)
                                <option value="{{ $type->id }}"
                                    {{ $type->id == $destination->type_id  ? 'selected' : '' }}>
                                    {{ $type->title }}
                                </option>
                                @endforeach
                            </select>
                        </div>

                    </div>
                </div>





                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Pricing</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="currency">Currency</label>
                            <select id="currency" class="custom_form_field" name="currency">
                                <option value="usd" selected>USD</option>
                            </select>
                        </div>
                        {{-- If flag is 1, only price per person fields are shown --}}
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="price">Single Room Price (supplementry single)</label>
                            <input type="number" min="0" id="single_room_price" class="custom_form_field" placeholder="Enter price"
                                name="single_room_price" value="{{$destination->single_room_price}}">
                        </div>

                        @if(!($user->role == 'admin'))
                        @if($destination->flag == 1)
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="price">Price (per person)</label>
                            <input type="number" min="0" id="price" class="custom_form_field" placeholder="Enter price"
                                name="price" value="{{$destination->price}}">
                        </div>
                        @else
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="two_person_price">Price (For two person's)</label>
                            <input type="number" min="0" id="two_person_price" class="custom_form_field" placeholder="Enter price"
                                name="two_person_price" value="{{$destination->two_person_price}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="three_to_four_price">Price (For three to four person's)</label>
                            <input type="number" min="0" id="three_to_four_price" class="custom_form_field" placeholder="Enter price"
                                name="three_to_four_price" value="{{$destination->three_to_four_price}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="five_to_six_price">Price (For five to six person's)</label>
                            <input type="number" min="0" id="five_to_six_price" class="custom_form_field" placeholder="Enter price"
                                name="five_to_six_price" value="{{$destination->five_to_six_price}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="six_plus_price">Price (For six plus person's)</label>
                            <input type="number" min="0" id="six_plus_price" class="custom_form_field" placeholder="Enter price"
                                name="six_plus_price" value="{{$destination->six_plus_price}}">
                        </div>
                        @endif
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="admin_commision">Commission Percentage</label>
                            <input type="text" id="commission" class="custom_form_field"
                                placeholder="commission percentage" name="admin_commision"
                                value="{{$commission->commission}} %" readonly>
                        </div>
                        @if($destination->flag == 1)
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="final_price">Final Price (per person)</label>
                            <input type="number" min="0" id="final_price" class="custom_form_field"
                                placeholder="Enter price" name="final_price" value="{{$destination->final_price}}" readonly>
                        </div>
                        @else
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="two_final_price">Final Price (For two person's)</label>
                            <input type="number" min="0" id="two_final_price" class="custom_form_field"
                                placeholder="Enter price" name="two_final_price" value="{{$destination->two_final_price}}" readonly>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="three_to_four_final_price">Final Price (For three to four person's)</label>
                            <input type="number" min="0" id="three_to_four_final_price" class="custom_form_field"
                                placeholder="Enter price" name="three_to_four_final_price" value="{{$destination->three_to_four_final_price}}" readonly>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="five_to_six_final_price">Final Price (For five to six person's)</label>
                            <input type="number" min="0" id="five_to_six_final_price" class="custom_form_field"
                                placeholder="Enter price" name="five_to_six_final_price" value="{{$destination->five_to_six_final_price}}" readonly>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="six_plus_final_price">Final Price (For six plus person's)</label>
                            <input type="number" min="0" id="six_plus_final_price" class="custom_form_field"
                                placeholder="Enter price" name="six_plus_final_price" value="{{$destination->six_plus_final_price}}" readonly>
                        </div>

                        <script>
                            document.getElementById('price').addEventListener('input', calculateFinalPrice);
                            document.getElementById('two_person_price').addEventListener('input', calculateTwoFinalPrice);
                            document.getElementById('three_to_four_price').addEventListener('input', calculateThreeToFourFinalPrice);
                            document.getElementById('five_to_six_price').addEventListener('input', calculateFiveToSixFinalPrice);
                            document.getElementById('six_plus_price').addEventListener('input', calculateSixPlusFinalPrice);
                            document.getElementById('commission').addEventListener('input', calculateFinalPrice);

                            function calculateFinalPrice() {
                                const price = parseFloat(document.getElementById('price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('final_price').value = finalPrice.toFixed(2);
                            }

                            function calculateTwoFinalPrice() {
                                const price = parseFloat(document.getElementById('two_person_price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('two_final_price').value = finalPrice.toFixed(2);
                            }

                            function calculateThreeToFourFinalPrice() {
                                const price = parseFloat(document.getElementById('three_to_four_price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('three_to_four_final_price').value = finalPrice.toFixed(2);
                            }

                            function calculateFiveToSixFinalPrice() {
                                const price = parseFloat(document.getElementById('five_to_six_price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('five_to_six_final_price').value = finalPrice.toFixed(2);
                            }

                            function calculateSixPlusFinalPrice() {
                                const price = parseFloat(document.getElementById('six_plus_price').value) || 0;
                                const commissionPercentage = parseFloat(document.getElementById('commission').value) || 0;
                                const finalPriceValue = price + (price * (commissionPercentage / 100));
                                const finalPrice = Math.round(finalPriceValue);
                                document.getElementById('six_plus_final_price').value = finalPrice.toFixed(2);
                            }
                        </script>
                        @endif


                        @else
                        @if($destination->flag == 1)
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="final_price">Price (per person)</label>
                            <input type="number" min="0" id="final_price" class="custom_form_field"
                                placeholder="Enter price" name="final_price" value="{{$destination->final_price}}">
                        </div>
                        @else
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="two_final_price"> Price (For two person's)</label>
                            <input type="number" min="0" id="two_final_price" class="custom_form_field"
                                placeholder="Enter price" name="two_final_price" value="{{$destination->two_final_price}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="three_to_four_final_price"> Price (For three to four person's)</label>
                            <input type="number" min="0" id="three_to_four_final_price" class="custom_form_field"
                                placeholder="Enter price" name="three_to_four_final_price" value="{{$destination->three_to_four_final_price}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="five_to_six_final_price"> Price (For five to six person's)</label>
                            <input type="number" min="0" id="five_to_six_final_price" class="custom_form_field"
                                placeholder="Enter price" name="five_to_six_final_price" value="{{$destination->five_to_six_final_price}}">
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="six_plus_final_price"> Price (For six plus person's)</label>
                            <input type="number" min="0" id="six_plus_final_price" class="custom_form_field"
                                placeholder="Enter price" name="six_plus_final_price" value="{{$destination->six_plus_final_price}}">
                        </div>
                        @endif
                        @endif

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="extras">Addons</label>
                            <select id="extras" class="custom_multi_select_dropdown extras" data-control="select2"
                                data-placeholder="Select an option" data-allow-clear="false" multiple="multiple"
                                name="extras[]">
                                <option></option>
                                <?php
                                $array = [];

                                if (!empty($destination->extras)) {
                                    $array = json_decode($destination->extras, true);
                                }

                                // Extract titles safely
                                $titles = array_map(function ($item) {
                                    return explode("-", $item)[0];
                                }, $array);
                                ?>
                                @if($destinationExtras && $destinationExtras->isNotEmpty())
                                @foreach($destinationExtras as $extras)
                                <option value="{{ $extras['title'] }}-{{ $extras['price'] }}"
                                    {{ in_array($extras->title, $titles) ? 'selected' : '' }}>
                                    {{ $extras['title'] }}-{{ $extras['price'] }}
                                </option>
                                @endforeach
                                @endif
                            </select>
                        </div>



                    </div>
                </div>



                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Package Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="days">Total Day's</label>
                            <input type="number" id="days" class="custom_form_field" placeholder="Enter total days"
                                name="days" min="0" value="{{$destination->days}}">
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="nights">Total Night's</label>
                            <input type="number" id="nights" class="custom_form_field" placeholder="Enter total nights"
                                name="nights" min="0" value="{{$destination->nights}}">
                        </div>


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="include_in">Include in package</label>

                            <select class="include_in_package include_in" data-control="select2"
                                data-placeholder="Select an option" data-allow-clear="false" multiple="multiple"
                                name="include_in[]">
                                <option></option>
                                <?php
                                $includearray = [];

                                if (!empty($destination->include_in)) {
                                    $includearray = json_decode($destination->include_in, true); // true gives array
                                }


                                ?> @if($destinationInclude && $destinationInclude->isNotEmpty())
                                @foreach($destinationInclude as $include)
                                <option value="{{ $include['title'] }}"
                                    {{ in_array($include->title, $includearray) ? 'selected' : '' }}>
                                    {{ $include['title'] }}
                                </option>
                                @endforeach
                                @endif
                            </select>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="not_include">Not Include in package</label>

                            <select class="not_include_in_package not_include" data-control="select2"
                                data-placeholder="Select an option" data-allow-clear="false" multiple="multiple"
                                name="not_include[]">
                                <option></option>
                                <?php
                                $notincludearray = [];

                                if (!empty($destination->not_include)) {
                                    $decoded = json_decode($destination->not_include, true);
                                    $notincludearray = is_array($decoded) ? $decoded : [];
                                }
                                ?>
                                @if($destinationNotInclude && $destinationNotInclude->isNotEmpty())
                                @foreach($destinationNotInclude as $notinclude)
                                <option value="{{ $notinclude['title'] }}"
                                    {{ in_array($notinclude->title, $notincludearray) ? 'selected' : '' }}>
                                    {{ $notinclude['title'] }}
                                </option>
                                @endforeach
                                @endif
                            </select>
                        </div>

                    </div>

                </div>

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Departure Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="departure_location">Departure Location</label>
                            <input type="text" id="departure_location" class="custom_form_field"
                                placeholder="Enter departure location" name="departure_location"
                                value="{{$destination->departure_location}}">
                        </div>


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="departure_date_time">Departure Date and Time</label>
                            <input type="text" class="custom_form_field" id="multiple-date-picker"
                                name="departure_date_time" placeholder="Select multiple dates"
                                value="{{ $destination->departure_date_time }}">
                        </div>



                    </div>

                </div>

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Destination Detail</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="destination_country">Destination's Country</label>

                            <select class="custom_form_field" name="destination_country" id="destination_country">
                                <option disabled selected>Select Country</option>
                                <?php
                                $uniqueCountries = $destinationCountryLocation->pluck('country')->unique()->toArray();
                                ?>
                                @foreach ($uniqueCountries as $country)
                                <option value="{{ $country }}"
                                    {{ $country == $destination->destination_country ? 'selected' : '' }}>{{ $country }}
                                </option>
                                @endforeach
                            </select>
                        </div>

                        

                      

                    </div>

                </div>
                @if($user->role == 'admin')
                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>SEO Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="page_h1_heading">Seo H1 heading (55 to 60 charactor) </label>
                            <label>Total character (<span class="h1_headingCharCount"></span>) </label>
                            <textarea class="custom_form_field page_h1_heading" id="page_h1_heading" type="text"
                                name="page_h1_heading" rows="4">{{$destination->page_h1_heading}}</textarea>

                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_title">Meta Title (55 to 60 charactor)</label>
                            <label>Total character (<span class="meta_titleCharCount"></span>) </label>
                            <textarea class="custom_form_field meta_title" id="meta_title" type="text" name="meta_title"
                                rows="4">{{$destination->meta_title}}</textarea>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_description">Meta Description (155 to 160 charactor)</label>
                            <label>Total character (<span class="meta_descriptionCharCount"></span>) </label>
                            <textarea class="custom_form_field meta_description" id="meta_description" type="text"
                                name="meta_description" rows="4"> {{$destination->meta_description}}</textarea>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_keywords">Meta keywords (comma seperated)</label>
                            <textarea class="custom_form_field" id="meta_keywords" type="text" name="meta_keywords"
                                rows="4">{{$destination->meta_keywords}}</textarea>
                        </div>



                    </div>

                </div>
                @endif
            </div>
        </div>

    </div>
</form>

@endsection