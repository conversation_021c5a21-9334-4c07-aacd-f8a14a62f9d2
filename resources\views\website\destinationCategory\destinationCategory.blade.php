@extends('website.include.layout')
@section('title', 'Destination Categories')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')


@section('content')


<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('/website/images/typesBanner.webp');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">

                <h2>Explore Diverse Destination Categories in Africa with Travel Africa</h2>
                <p>Discover a wide range of destination categories across Africa, from stunning natural wonders to
                    vibrant cultural experiences. Plan your next international adventure with Travel Africa and book
                    destination packages online from anywhere in the world.</p>
            </div>
        </div>
    </div>
</section>

<section class="d-inline-block w-100 float-left space">
    <div class="custom_container">
        <div class="gallery_heading d-flex flex-column w-100 align-items-center text-center">
            <small>Explore more</small>
            <h3>Our Destination Categories</h3>
        </div>
        <div class="gallery_list d-flex align-items-center justify-content-center flex-wrap h-100">
            @foreach($destinationCategory as $destinationcat)
            <div class="single_gallery_list h-100">
                <a href="{{ route('website.destinationCategory.destinationCategoryDetail', $destinationcat->slug) }}"
                    class="position-relative w-100 h-100 float-left">
                    <img src="{{$destinationcat ? asset('storage/destinations/'.$destinationcat->cover_image) : asset('website/images/logo.png') }}"
                        class="w-100 h-100 object-fit-cover" alt="{{$destinationcat->alt_text_cover_image}}"
                        loading="lazy" decoding="async" width="270" height="300" />
                    <figcaption
                        class="single_gallery_content position-absolute w-100 bottom-0 left-0 d-flex justify-content-between align-items-end">
                        <span>{{$destinationcat->title}}</span>
                    </figcaption>
                </a>
            </div>
            @endforeach

        </div>

    </div>
</section>


@endsection
