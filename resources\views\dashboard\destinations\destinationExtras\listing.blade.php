@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / destinations / extras / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddExtras"><span>Add
                </span></button>
        </div>
        <table id="destination_extras_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 120px;">Title</th>
                    <th style="min-width: 120px;">Price</th>
                    <th style="min-width: 16px; text-align:right;">Action</th>
                </tr>
            </thead>
            <tbody>
            @if($destinationExtras && $destinationExtras->isNotEmpty())
                @foreach($destinationExtras as $destinationExtrasData)
                <tr class="extras_row">
                    <td></td>
                    <td>{{ $destinationExtrasData->title }}</td>
                    <td>{{ $destinationExtrasData->price }}</td>


                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $destinationExtrasData->id }}" type="button"
                                    class="delete_btn delete_extras"><i class="fas fa-trash-alt"></i>Delete</button>

                                <button class="update_btn edit_extras_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasUpdateExtras{{$destinationExtrasData->id}}"
                                    data-form-id="update_extras_{{$destinationExtrasData->id}}"
                                    data-id="{{$destinationExtrasData->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                            </div>
                        </div>
                        @include("dashboard.destinations.destinationExtras.update")

                    </td>
                </tr>

                @endforeach
                @endif
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.destinations.destinationExtras.add")
@include("dashboard.destinations.destinationExtras.delete")

@endsection
