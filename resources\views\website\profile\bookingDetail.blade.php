@extends('website.include.layout')
@section('title', 'profile')
@push('css')
<link rel="stylesheet" href="{{asset('profile/css/profile_page.css')}}">
@endpush
@section('content')

<section class="page_title_section d-block w-100 text-center float-left position-relative">
    <img src="{{asset('website/images/login_banner.png')}}" class="w-100 float-left h-100 object-fit-cover"
        alt="homebanner1" loading="lazy" />
    <div class="d-flex w-100 h-100 position-absolute top-0 left-0">
        <div class="page_title d-flex align-items-center justify-content-center text-center flex-column">
            <small>Read</small>
            <h2>Booking Details</h2>
        </div>
    </div>
</section>

<section class="profile_page_section">
    <div class="custom_container">
        <div class="pofile_page_main_box">
            @include('website.profile.sidebar')
            <!-- personel info asset start -->

            <div class="profile_tabs_data_section">
                <div class="profile_tabs_data_header">
                    <h2>Booking Details</h2>
                </div>
                <div class="profile_tabs_content_box">
                    <table id="booking_history" class="data_table display" style="width:100%">
                        <thead>
                            <tr>
                                <th style="min-width: 40px;"></th>
                                <th style="min-width: 120px;">Destination Id</th>
                                <th style="min-width: 120px;">Destination</th>
                                <th style="min-width: 120px;">Booked Date</th>
                                <th style="min-width: 80px;">Tickets</th>
                                <th style="min-width: 80px;">Adultes</th>
                                <th style="min-width: 80px;">Children</th>
                                <th style="min-width: 150px;">Extras</th>
                                <th style="min-width: 120px;">Message</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($bookingRecords as $bookingRecords)
                            <tr>
                                <td></td>
                                <td>{{$bookingRecords->destination->id}}</td>
                                <td>{{$bookingRecords->destination->title}}</td>
                                <td>{{$bookingRecords->date}}</td>
                                <td>{{$bookingRecords->tickets}}</td>
                                <td>{{$bookingRecords->adultes}}</td>
                                <td>{{$bookingRecords->children}}</td>
                                <td>
                                    @if($bookingRecords->extras)
                                    @foreach (explode(',', $bookingRecords->extras) as $extras)
                                    <p class=" m-2 p-2" style="background-color:var(--secondary-bg-color);">
                                        {{ $extras }}
                                    </p>
                                    @endforeach
                                    @else
                                    <p class=" m-2 p-2" style="background-color:var(--secondary-bg-color);">
                                        No Adon's Selected</p>
                                    @endif
                                </td>
                                <td>{{$bookingRecords->message}}</td>
                            </tr>
                            @endforeach
                        </tbody>

                    </table>

                </div>
            </div>
        </div>
    </div>
</section>



@endsection

@push('js')
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js" defer></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js" defer></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js" defer></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js" defer></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.68/pdfmake.min.js" defer></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.68/vfs_fonts.js" defer></script>
<script src="{{ asset('profile/js/custom_script.js') }}"></script>
@endpush