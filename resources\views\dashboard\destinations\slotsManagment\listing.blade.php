@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / destination / details</a>
</div>
<div class="content-section-box">
    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center" tabindex="0" type="button" data-bs-toggle="offcanvas"
                data-bs-target="#offcanvasStoreSlots">
                <i class="far fa-edit"></i> Add
            </button>
        </div>
        <table id="country_location_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 120px;">Destination</th>
                    <th style="min-width: 120px;">Date</th>
                    <th style="min-width: 120px;">Date Status</th>
                    <th style="min-width: 120px;">Slots</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>


                @if(isset($slotsData))
                @foreach ($slotsData as $date => $slots)
                @php
                $formattedDate = customDate($date, 'F d, Y');
                $isExpired = now()->greaterThan(Carbon\Carbon::parse($date));
                @endphp
                <tr class="destination_row">
                    <td></td>
                    <td>{{ $destination->title }}</td>
                    <td>{{ $formattedDate }}</td>
                    <td>@if($isExpired) Expired @endif</td>
                    <td>{{$slots['slots'] ?? 0}}</td>
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{$slots['id'] }}" type="button" class="delete_btn delete_slot"><i
                                        class="fas fa-trash-alt"></i>
                                    Delete</button>


                            </div>
                        </div>

                    </td>
                </tr>
                @endforeach
                @else{
                <p>No slot data available</p>
                }
                @endif

            </tbody>
        </table>
    </div>

</div>
@include("dashboard.destinations.slotsManagment.updateSlots")
@include("dashboard.destinations.slotsManagment.deleteSlots")

@endsection
