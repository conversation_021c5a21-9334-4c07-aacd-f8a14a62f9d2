$(document).ready(function () {
    $(".blog_long_description").summernote({
        height: "300",
    });
});

$(document).ready(function () {
    $("#blog-category-form").validate({
        rules: {
            title: {
                required: true,
            },
            cover_img: {
                required: true,
            },
            banner_img: {
                required: true,
            },
            page_h1_heading: {
                required: true,
            },
            meta_title: {
                required: true,
            },
            meta_description: {
                required: true,
            },
            meta_keywords: {
                required: true,
            },
            alt_text_cover_image: {
                required: true,
            },
            alt_text_banner_image: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/blog/category/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasAddCategory")
                    );
                    offcanvas.hide();
                    showAlert("Category added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasAddCategory")
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("Category already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    }
                },
            });
        },
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_blog_category")) {
        const blogCategoryId = event.target.getAttribute("data-id");
        console.log(blogCategoryId);
        document
            .querySelector("#blog_cat_confirmDeleteBtn")
            .setAttribute("data-id", blogCategoryId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#blog_cat_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/blog/category/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Category deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_blog_category")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".category_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $(".edit_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
        console.log(formId);

        $("#" + formId).validate({
            rules: {
                title: {
                    required: true,
                },
                page_h1_heading: {
                    required: true,
                },
                meta_title: {
                    required: true,
                },
                meta_description: {
                    required: true,
                },
                meta_keywords: {
                    required: true,
                },
                alt_text_cover_image: {
                    required: true,
                },
                alt_text_banner_image: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/blog/category/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasUpdateCategory" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("Category updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateCategory" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("Category already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});

// Add event listener to toggle switches
document.addEventListener("change", function (event) {
    if (event.target.classList.contains("blog_switch_input")) {
        const blogId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/blog/${blogId}/status`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { status: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});

// Add event listener to toggle switches
document.addEventListener("change", function (event) {
    if (event.target.classList.contains("blog_popular_switch_input")) {
        const blogId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/blog/popular/${blogId}/status`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { status: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_blog")) {
        const blogId = event.target.getAttribute("data-id");
        console.log(blogId);
        document
            .querySelector("#blog_confirmDeleteBtn")
            .setAttribute("data-id", blogId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector("#blog_confirmDeleteBtn");
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/blog/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("blog deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_blog")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".blog_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert("An error occurred while deleting blog.", "error");
            },
        });
    });
});

$(document).ready(function () {
    $("#add_blog_form").validate({
        rules: {
            title: {
                required: true,
            },
            cover_img: {
                required: true,
            },
            tags: {
                required: true,
            },

            short_description: {
                required: true,
            },
            type: {
                required: true,
            },
            author_name: {
                required: true,
            },
            banner_img: {
                required: true,
            },
            page_h1_heading: {
                required: true,
            },
            meta_title: {
                required: true,
            },
            meta_description: {
                required: true,
            },
            meta_keywords: {
                required: true,
            },
            alt_text_cover_image: {
                required: true,
            },
            alt_text_banner_image: {
                required: true,
            },
            alt_text_author_image: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/blog/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);

            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();

                    $(".form_process_loader").addClass("d-none");
                    showAlert("blog added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("blog already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    $("#update_blog_form").validate({
        rules: {
            title: {
                required: true,
            },

            tags: {
                required: true,
            },

            short_description: {
                required: true,
            },
            type: {
                required: true,
            },
            author_name: {
                required: true,
            },

            page_h1_heading: {
                required: true,
            },
            meta_title: {
                required: true,
            },
            meta_description: {
                required: true,
            },
            meta_keywords: {
                required: true,
            },
            alt_text_cover_image: {
                required: true,
            },
            alt_text_banner_image: {
                required: true,
            },
            alt_text_author_image: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            console.log(form);

            let baseURL = window.location.origin;
            let id = $(form).find('[name="id"]').val();
            let apiEndpoint = "/api/blog/update/" + id;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();

                    $(".form_process_loader").addClass("d-none");
                    showAlert("blog updated successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    showAlert(
                        "There is an error.contact your support team !",
                        "error"
                    );
                },
            });
        },
    });
});

// Add event listener to toggle switches
document.addEventListener("change", function (event) {
    if (event.target.classList.contains("blog_comment_switch_input")) {
        const commentId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/blog/comment/${commentId}/status`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { status: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_blog_comment")) {
        const commentId = event.target.getAttribute("data-id");
        console.log(commentId);
        document
            .querySelector("#blog_comment_confirmDeleteBtn")
            .setAttribute("data-id", commentId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#blog_comment_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/blog/comment/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("comment deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_blog_comment")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".comment_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert("An error occurred while deleting comment.", "error");
            },
        });
    });
});
