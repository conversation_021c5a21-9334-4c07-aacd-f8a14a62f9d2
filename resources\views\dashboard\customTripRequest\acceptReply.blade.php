<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAcceptReply{{$tripList->id}}"
    aria-labelledby="offcanvasAcceptReply{{$tripList->id}}Label">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 id="offcanvasAcceptReply{{$tripList->id}}Label" class="offcanvas-title">Accept Reply</h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="accept_reply_{{$tripList->id}}">
            @csrf
            <input type="hidden" name="id" value="{{$tripList->id}}" />
            <div class="d-grid gap-3 w-100">

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="message">Message</label>
                    <textarea class="custom_form_field" name="message" id="message" rows="10"></textarea>
                </div>

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="total_price">Total price</label>
                    <input type="number" min="0" class="custom_form_field total_price" name="total_price"
                        id="total_price_{{$tripList->id}}" />
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="advance_percentage">Advance in percentage</label>
                    <input type="number" min="0" max="100" class="custom_form_field advance_percentage"
                        name="advance_percentage" id="advance_percentage_{{$tripList->id}}" />
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="advance_price">Advance in price</label>
                    <input type="number" min="0" class="custom_form_field advance_price" name="advance_price"
                        id="advance_price_{{$tripList->id}}" />
                </div>
                <script>
                document.getElementById('total_price_{{$tripList->id}}').addEventListener('input',
                    calculateFinalPrice);
                document.getElementById('advance_percentage_{{$tripList->id}}').addEventListener('input',
                    calculateFinalPrice);

                function calculateFinalPrice() {
                    const price = parseFloat(document.getElementById('total_price_{{$tripList->id}}').value) ||
                        0;
                    const advancePercentage = parseFloat(document.getElementById(
                            'advance_percentage_{{$tripList->id}}').value) ||
                        0;
                    const advancePrice = (price * advancePercentage / 100).toFixed(2);
                    document.getElementById('advance_price_{{$tripList->id}}').value = advancePrice;
                }
                </script>

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="destination_detail">Add pdf</label>

                    <input id="destination_detail" type="file" name="destination_detail" class="dropify"
                        data-max-file-size="2M" data-allowed-file-extensions="jpg jpeg png pdf webp" />
                </div>

                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button class="custom_btn_2">Submit
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>
        </form>
    </div>
</div>