<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('web_banners', function (Blueprint $table) {
            $table->id();
            $table->longText('desktop_image');
            $table->longText('mobile_image');
            $table->longText('alt_text');
             $table->longText('link');
            $table->boolean('status')->default(1);
            $table->timestamps(); // created_at & updated_at
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('web_banners');
    }
};
