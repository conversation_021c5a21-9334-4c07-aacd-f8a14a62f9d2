@extends('website.include.layout')
@section('title', 'comming soon')

@section('content')
@push('css')

<style>
body {
    overflow: hidden;
}

.desk_header {
    display: none !important;
}

.mobile_header {
    display: none !important;
}

.newsletter_two_custom_section {
    display: none !important;
}

.footer-section {
    display: none !important;
}

.coming_soon_section {
    width: 100%;
    padding-top: 110px;
    background: #000000;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    height: 100dvh;
    overflow-y: auto;
}

.section_top_layer {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
}

.section_top_layer img {
    display: flex;
    width: 100%;
    object-fit: cover;
}

.inner_section {
    display: flex;
    flex-direction: column;
    max-width: 540px;
    padding: 0px 15px;
}

.inner_section_head {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    border-bottom: 6px dotted var(--primary-color);
    padding-bottom: 22px;
}

.inner_section .inner_section_head>h3 {
    color: #FFF;
    font-size: 95px;
    font-style: normal;
    font-weight: 700;
    line-height: 118px;
    text-transform: uppercase;
    text-shadow: 3px 3px 0px var(--primary-color);
    font-family: 'Work Sans', sans-serif !important;

}

.inner_section .inner_section_head>span {
    color: #FFF;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;
    text-transform: capitalize;
    text-shadow: 1px 2px 1px var(--primary-color);
    font-family: 'Work Sans', sans-serif !important;
    padding-bottom: 20px;
}

.inner_section .inner_section_head>a {
    color: #FFF;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 30px;

    text-shadow: 1px 2px 1px var(--primary-color);
    font-family: 'Work Sans', sans-serif !important;
    text-decoration: none;
}

.inner_section_head_2 {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    position: relative;
    padding: 50px 0px;
    text-align: center;
}

.inner_section_head_2 .ab_1 {
    position: absolute;
    left: -10px;
    top: 20px;

}

.inner_section_head_2 .ab_2 {
    position: absolute;
    right: -10px;
    top: 20px;
}

.inner_section_head_2 img {
    width: 100px;
}


.inner_section_head_2 h3 {
    color: #FFF;
    font-size: 141px;
    font-style: normal;
    font-weight: 700;
    line-height: 132.8px;
    text-transform: uppercase;
    font-family: 'Work Sans', sans-serif !important;
    text-shadow: 3px 3px 0px #E87524;
}

.inner_section_head_2 h3 span {
    color: #FFF;
    font-size: 94px;
    font-style: normal;
    font-weight: 700;
    line-height: 130.565px;
    text-transform: uppercase;
    font-family: 'Work Sans', sans-serif !important;
}

@media(max-width: 768px) {}

@media(max-width: 540px) {
    .inner_section_head_2 h3 {
        font-size: 90px;
        line-height: 89.8px;
    }

    .inner_section_head_2 h3 span {
        font-size: 70px;
        line-height: 96.565px;
    }

    .inner_section_head_2 img {
        width: 70px;
    }

    .inner_section .inner_section_head>span {
        font-size: 16px;
        line-height: 26px;
    }

    .inner_section .inner_section_head>a {
        font-size: 16px;
        line-height: 26px;
    }
}
</style>


<section class="coming_soon_section">
    <div class="section_top_layer"><img src="website/images/comingsoon.webp"></div>
    <div class="inner_section">
        <div class="inner_section_head">
            <h3>Travel Africas</h3>
            <span>(Travel Africas)</span>
            <a href="mailto:<EMAIL>">Contact Us at : <EMAIL></a>
        </div>
        <div class="inner_section_head_2">
            <img class="ab_1" src="website/images/soonleft.webp">
            <h3><span>Coming</span> Soon</h3>
            <img class="ab_2" src="website/images/soonright.webp">
        </div>

    </div>
</section>

@endsection

@push('js')

@endpush
