@extends('website.include.layout')
@section('title', 'payment')

@section('Default Meta Title')
@section('Default Meta Keywords')
@section('Default Meta Description')
@section('Default H1 Heading')


@section('content')
<style>
#card-element {
    background-color: #fff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

#card-element input {
    font-size: 16px;
    padding: 10px;
    border: none;
    outline: none;
    width: 100%;
    margin-bottom: 10px;
    box-sizing: border-box;
}

#card-element .StripeElement--invalid {
    border-color: #dc3545;
}

#card-errors {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
}
.login_section_box_heading h4 {
    font-size: 20px;
    font-weight: 600;
    
}
</style>

<section class="login_section display-block float-left w-100 mt-5">

    <div style="max-width: 540px;margin:0 auto;">
        <div class="login_section_box d-grid w-100 align-items-center">
            <div class="login_tabs_main  d-flex w-100 align-items-center justify-content-center w-100 flex-column">
                <div class="login_section_box_heading d-grid w-100">
                    <h3>Payment Detail's</h3>
                    <p>you have two option for the payment . you can transfer through your credit / debit card or direct
                        transfer into account provided in direct transfer . click on direct tranfser to use this option
                    </p>
                </div>


                <div class="nav login_tabs" role="tablist">
                    <a class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#loginTab"
                        type="button" role="tab" aria-controls="loginTab" aria-selected="true">Credit/Debit Card</a>
                    <a class="nav-link" id="signup-tab" data-bs-toggle="tab" data-bs-target="#signupTab" type="button"
                        role="tab" aria-controls="signupTab" aria-selected="false">Direct Transfer</a>
                </div>

                <div class="tab-content login_tab_content">
                    <div class="tab-pane fade show active" id="loginTab" role="tabpanel" aria-labelledby="login-tab">
                       <div class="login_section_box_heading">
                            <h4>Price To be Paid</h4>
                            <p>{{ $price }} USD</p>
                        </div>
                        <form id="strip_payment_submit">
                            @csrf
                            <!-- Your hidden input fields -->
                            <input type="hidden" name="id" value="{{ $id }}">
                            <input type="hidden" name="trip_type" value="{{ $tripType }}">
                            <input type="hidden" name="user_id" value="{{ $userId }}">
                            <input type="hidden" name="payment_type" value="{{ $paymentType }}">
                            <input type="hidden" id="price" name="price" value="{{ $price }}">

                            <div style="padding-bottom: 20px;">
                                <span style="padding-bottom: 10px;">Card Number</span>
                                <div id="card-element"></div>
                            </div>
                            <div class="login_form_content_box">
                                <div class="login_form_signin_btn">
                                    <button id="submit-payment">Pay Now</button>
                                </div>
                            </div>
                        </form>


                    </div>
                    <div class="tab-pane fade" id="signupTab" role="tabpanel" aria-labelledby="signup-tab">

                        <div class="login_section_box_heading">
                            <h4>Account Number</h4>
                            <p>*****************</p>
                             <h4> Routing (ABA)</h4>
                            <p>*********</p>
                            <h4>SWIFT code</h4>
                            <p>CITIUS33</p>
                              <h4>Account type</h4>
                            <p>CHECKING</p>
                            <h4>Beneficiary name</h4>
                            <p>Monpays Tours</p>
                        </div>
                         <div class="login_section_box_heading">
                            <h4>Price To be Paid</h4>
                            <p>{{ $price }} USD</p>
                        </div>


                        <form method="POST" id="custom_trip_direct_payment" enctype="multipart/form-data">
                            @csrf

                            <input type="hidden" name="id" value="{{ $id }}">
                            <input type="hidden" name="trip_type" value="{{ $tripType }}">
                            <input type="hidden" name="user_id" value="{{ $userId }}">
                            <input type="hidden" name="payment_type" value="{{ $paymentType }}">
                            <input type="hidden" name="price" value="{{ $price }}">

                            <div class="login_form_content_box">

                                <div for="payment_slip" class="drag_and_drop_box" data-for="payment_slip">
                                    <small>Attach payment slip</small>
                                    <input id="payment_slip" type="file" accept="image/*" name="payment_slip"
                                        class="dropify" data-max-file-size="2M"
                                        data-allowed-file-extensions="jpg jpeg png webp" />
                                    <label class="error" generated="true" for="payment_slip"></label>
                                </div>
                                <div class="login_form_signin_btn">
                                    <button>Submit</button>
                                </div>
                            </div>
                        </form>



                    </div>
                </div>
            </div>

        </div>

    </div>
</section>

@push('scripts')
<!-- Include your JavaScript files here -->



@endpush
@endsection
