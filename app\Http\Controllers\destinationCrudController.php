<?php

namespace App\Http\Controllers;

use App\Models\countryAndLocations;
use App\Models\DestinationCategory;
use App\Models\Destinations;
use App\Models\destinationSlots;
use App\Models\DestinationType;
use App\Models\destination_extras;
use App\Models\destination_includes;
use App\Models\destination_not_includes;
use App\Models\DestinationBooking;
use App\Models\bookingPayments;
use App\Models\Commissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class destinationCrudController extends Controller
{
    public function index(request $request)
    {
        $status = $request->query('status');

        if ($status) {
            $destinations = Destinations::where('status', $status)->get();
        } elseif ($request->id) {
            $destinations = Destinations::where('user_id', $request->id)->get();
        } elseif (Auth::check()) {
            $user = Auth::user();

            $destinations = $user->destinations;
        } else {
            // Handle the case when no user is authenticated
            $destinations = [];
        }

        return view('dashboard.destinations.index', ['destinations' => $destinations]);
    }

    public function addDestinationView()
    {
        $user = auth()->user();

        $commission = Commissions::where('country', $user->subscriber->country)->first();


        $destinationCategories = DestinationCategory::all();
        $destinationTypes = DestinationType::all();
        $destinationCountryLocation = countryAndLocations::all();

        $destinationInclude = Auth::user()->destinationIncludes;
        $destinationNotInclude = Auth::user()->destinationNotIncludes;
        $destinationExtras = Auth::user()->destinationExtras;



        return view('dashboard.destinations.addDestination', ['destinationCategories' => $destinationCategories, 'destinationTypes' => $destinationTypes, 'destinationCountryLocation' => $destinationCountryLocation, 'destinationInclude' => $destinationInclude, 'destinationNotInclude' => $destinationNotInclude, 'destinationExtras' => $destinationExtras, 'commission' => $commission]);
    }

    public function store(Request $request)
    {

        $destination = new Destinations();

        $destination->user_id = $request->user_id;
        $destination->category_id = $request->category;
        $destination->type_id = $request->type;
        $destination->title = $request->title;
        $destination->slug = Str::slug($request->title);
        $destination->short_description = $request->short_description;
        $destination->departure_location = $request->departure_location;

        $destination->page_h1_heading = $request->page_h1_heading;
        $destination->meta_title = $request->meta_title;
        $destination->meta_description = $request->meta_description;
        $destination->meta_keywords = $request->meta_keywords;

        $destination->alt_text_cover_image = $request->alt_text_cover_image;
        $destination->alt_text_banner_image = $request->alt_text_banner_image;


        $destination->single_room_price = $request->single_room_price;

        $destination->days = $request->days;
        $destination->nights = $request->nights;

        $departureDateTime = json_encode($request->departure_date_time);
        $destination->departure_date_time = $departureDateTime;

        $destination->currency = $request->currency;
        $destination->destination_country = $request->destination_country;



        $destination->status = 0;
        $destination->popular = 0;
        $destination->trending = 0;



        $destination->include_in = $request->has('include_in')
            ? json_encode($request->input('include_in'), JSON_UNESCAPED_UNICODE)
            : json_encode([], JSON_UNESCAPED_UNICODE);

        $destination->not_include = $request->has('not_include')
            ? json_encode($request->input('not_include'), JSON_UNESCAPED_UNICODE)
            : json_encode([], JSON_UNESCAPED_UNICODE);

        $destination->extras = $request->has('extras')
            ? json_encode($request->input('extras'), JSON_UNESCAPED_UNICODE)
            : json_encode([], JSON_UNESCAPED_UNICODE);

        // if (isset($request->city)) {
        //     $destinationCities = implode(',', $request->input('city'));
        //     $destination->city = $destinationCities;
        // }

        // if (isset($request->destination_list)) {
        //     $destinationDetail = implode(',', $request->input('destination_list'));
        //     $destination->destination_detail = $destinationDetail;
        // }

        // Handle image upload
        if ($request->hasFile('destination_pdf')) {
            $file = $request->file('destination_pdf');
            $pdfImagePath = media($file, 'storage/destinationPdf');
            $pdfImagePath = basename($pdfImagePath);
            $destination->destination_pdf = $pdfImagePath;
        }

        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $logoImagePath = media($file, 'storage/destinations');
            $logoImagePath = basename($logoImagePath);
            $destination->cover_img = $logoImagePath;
        }

        // Handle image upload
        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $BannerImagePath = media($file, 'storage/destinations');
            $BannerImagePath = basename($BannerImagePath);
            $destination->banner_image = $BannerImagePath;
        }

        $imageGalleryPaths = [];
        // if ($request->hasFile('image_gallery')) {
        //     foreach ($request->file('image_gallery') as $image) {
        //         $imageName = uniqid() . '.' . $image->extension();
        //         $image->move(public_path('storage/destinations'), $imageName);
        //         $imageName = basename($imageName);
        //         $imageGalleryPaths[] = $imageName;
        //     }
        // }

        if ($request->hasFile('image_gallery')) {
            foreach ($request->file('image_gallery') as $image) {
                $convertedImageName = media($image, 'storage/destinations');
                $imageGalleryPaths[] = $convertedImageName;
            }
        }

        $destination->image_gallery = json_encode($imageGalleryPaths);

        $editorData = [];
        // if ($request->has('section')) {
        if ($request->has('editor_data')) {
            //new change
            $editors = json_decode($request->input('editor_data'), true);

            foreach ($editors as $key => $editor) {

                $editor_main_image_data = null;
                $fileIndex = $key + 1;
                if ($request->hasFile("main_image.$fileIndex")) {
                    $file = $request->file("main_image")[$fileIndex];
                    $editor_main_image = media($file, 'storage/destinations');
                    $editor_main_image_data = basename($editor_main_image);
                }

                $content = $editor['content'];
                $content = str_replace('<o:p>', '<p>', $content);
                $content = str_replace('</o:p>', '</p>', $content);

                // Additional cleanup steps
                $content = str_replace('&nbsp;', ' ', $content); // Replace non-breaking spaces with regular spaces
                $content = preg_replace('/<span[^>]*>/', '', $content); // Remove <span> tags and their attributes
                $content = str_replace('</span>', '', $content); // Remove closing </span> tags
                $content = preg_replace('/<p[^>]*>/', '<p>', $content); // Remove <p> tags and their attributes
                libxml_use_internal_errors(true);
                $dom = new \DomDocument();
                $dom->loadHtml($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
                // $images = $dom->getElementsByTagName('img');
                // foreach ($images as $item => $image) {
                //     $src = $image->getAttribute('src');
                //     if (strpos($src, 'data:image') === 0) {
                //         list(, $data) = explode(',', $src);
                //         $imageData = base64_decode($data);
                //         $imageName = uniqid() . '.webp'; // Generate a unique image name
                //         $path = public_path('storage/destinationEditor') . '/' . $imageName;
                //         file_put_contents($path, $imageData);
                //         $image->setAttribute('src', asset('storage/destinationEditor/' . $imageName));
                //     }
                // }
                // $content = $dom->saveHTML();


                preg_match_all('/<img[^>]+>/', $content, $matches);
                foreach ($matches[0] as $imageTag) {
                    preg_match('/src="([^"]+)"/', $imageTag, $srcMatches);
                    if (isset($srcMatches[1])) {
                        $src = $srcMatches[1];
                        if (strpos($src, 'data:image') === 0) {
                            list(, $data) = explode(',', $src);
                            $imageData = base64_decode($data);
                            $imageName = uniqid() . '.webp'; // Generate a unique image name
                            $path = public_path('storage/destinationEditor') . '/' . $imageName;
                            file_put_contents($path, $imageData);
                            $newSrc = asset('storage/destinationEditor/' . $imageName);
                            $content = str_replace($src, $newSrc, $content);
                        }
                    }
                }

                $editorData[] = [
                    'id' => $key,
                    'day' => $editor['day'],
                    'content' => $content,
                    'main_img' => $editor_main_image_data,
                ];
            }
        }

        $destination->destination_long_detail = json_encode($editorData);




        $accomodationEditorData = [];
        if ($request->has('accomodation_editor_data')) {
            // new change
            $accomodationEditors = json_decode($request->input('accomodation_editor_data'), true);

            foreach ($accomodationEditors as $key => $accomodationEditor) {


                $acc_main_image_data = null;
                $fileIndex = $key + 1;
                if ($request->hasFile("acc_main_image.$fileIndex")) {
                    $file = $request->file("acc_main_image")[$fileIndex];
                    $acc_main_image = media($file, 'storage/destinations');
                    $acc_main_image_data = basename($acc_main_image);
                }

                $accomodation_content = $accomodationEditor['accomodation_content'];

                $accomodation_content = str_replace('<o:p>', '<p>', $accomodation_content);
                $accomodation_content = str_replace('</o:p>', '</p>', $accomodation_content);

                // Additional cleanup steps
                $accomodation_content = str_replace('&nbsp;', ' ', $accomodation_content); // Replace non-breaking spaces with regular spaces
                $accomodation_content = preg_replace('/<span[^>]*>/', '', $accomodation_content); // Remove <span> tags and their attributes
                $accomodation_content = str_replace('</span>', '', $accomodation_content); // Remove closing </span> tags
                $accomodation_content = preg_replace('/<p[^>]*>/', '<p>', $accomodation_content); // Remove <p> tags and their attributes
                libxml_use_internal_errors(true);
                $dom = new \DomDocument();
                $dom->loadHtml($accomodation_content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
                $images = $dom->getElementsByTagName('img');



                preg_match_all('/<img[^>]+>/', $accomodation_content, $matches);
                foreach ($matches[0] as $imageTag) {
                    preg_match('/src="([^"]+)"/', $imageTag, $srcMatches);
                    if (isset($srcMatches[1])) {
                        $src = $srcMatches[1];
                        if (strpos($src, 'data:image') === 0) {
                            list(, $data) = explode(',', $src);
                            $imageData = base64_decode($data);
                            $imageName = uniqid() . '.webp'; // Generate a unique image name
                            $path = public_path('storage/destinationEditor') . '/' . $imageName;
                            file_put_contents($path, $imageData);
                            $newSrc = asset('storage/destinationEditor/' . $imageName);
                            $accomodation_content = str_replace($src, $newSrc, $accomodation_content);
                        }
                    }
                }

                $accomodationEditorData[] = [
                    'id' => $key,
                    'day' => $accomodationEditor['accomodation_day'],
                    'content' => $accomodation_content,
                    'main_img' => $acc_main_image_data,
                ];
            }
        }


        $destination->destination_accomodation_detail = json_encode($accomodationEditorData);


        $destination->admin_commision = $request->admin_commision;

        if ($request->filled('flag')) {
            $destination->flag = $request->flag;
        }
        if ($request->filled('price')) {
            $destination->price = $request->price;
        }
        if ($request->filled('final_price')) {
            $destination->final_price = $request->final_price;
        }

        if ($request->filled('two_person_price')) {
            $destination->two_person_price = $request->two_person_price;
        }
        if ($request->filled('three_to_four_price')) {
            $destination->three_to_four_price = $request->three_to_four_price;
        }
        if ($request->filled('five_to_six_price')) {
            $destination->five_to_six_price = $request->five_to_six_price;
        }
        if ($request->filled('six_plus_price')) {
            $destination->six_plus_price = $request->six_plus_price;
        }
        if ($request->filled('two_final_price')) {

            $destination->two_final_price = $request->two_final_price;
        }
        if ($request->filled('three_to_four_final_price')) {
            $destination->three_to_four_final_price = $request->three_to_four_final_price;
        }
        if ($request->filled('five_to_six_final_price')) {
            $destination->five_to_six_final_price = $request->five_to_six_final_price;
        }
        if ($request->filled('six_plus_final_price')) {
            $destination->six_plus_final_price = $request->six_plus_final_price;
        }
        $destination->save();

        return response()->json($destination, 200);
    }

    public function show($id)
    {

        $user = auth()->user();

        $commission = Commissions::where('country', $user->subscriber->country)->first();
        $destination = Destinations::findOrFail($id);
        $destinationCategories = DestinationCategory::all();
        $destinationTypes = DestinationType::all();
        $destinationCountryLocation = countryAndLocations::all();


        $destinationInclude = Auth::user()->destinationIncludes;
        $destinationNotInclude = Auth::user()->destinationNotIncludes;
        $destinationExtras = Auth::user()->destinationExtras;

        //  return view('dashboard.destinations.addDestination', ['destination' => $destination, 'destinationCategories' => $destinationCategories, 'destinationTypes' => $destinationTypes]);

        return view('dashboard.destinations.update', ['destination' => $destination, 'destinationCategories' => $destinationCategories, 'destinationTypes' => $destinationTypes, 'destinationCountryLocation' => $destinationCountryLocation, 'destinationInclude' => $destinationInclude, 'destinationNotInclude' => $destinationNotInclude, 'destinationExtras' => $destinationExtras, 'commission' => $commission]);
    }

    public function destinationSlots($id)
    {
        $destinationDetail = Destinations::findOrFail($id);
        if ($destinationDetail) {
            // Retrieve all dates associated with the destination
            $datesData = json_decode($destinationDetail->departure_date_time);
            $dates = explode(', ', $datesData);

            // Initialize an empty array to store slots
            $slotsData = [];

            foreach ($dates as $date) {

                // Retrieve slots for each date
                $dateSlots = destinationSlots::where('destination_id', $destinationDetail->id)
                    ->where('date', customDate($date, 'F d, Y'))
                    ->first();

                // Store the slots data in the array
                $slotsData[$date] = [
                    'id' => $dateSlots ? $dateSlots->id : null,
                    'slots' => $dateSlots ? $dateSlots->slots : null,
                ];
            }
        }
        return view('dashboard.destinations.slotsManagment.listing', ['destination' => $destinationDetail, 'slotsData' => $slotsData]);
    }

    public function storeDestinationSlots(Request $request)
    {
        if ($data = destinationSlots::where('destination_id', $request->destination_id)->where('date', $request->date)->first()) {
            $data->slots = $request->slots;
            $data->save();
            return response()->json(['message' => 'slots updated'], 422);
        }
        $destinationSlots = new destinationSlots();
        $destinationSlots->date = $request->date;

        $destinationSlots->slots = $request->slots;
        $destinationSlots->destination_id = $request->destination_id;
        $destinationSlots->save();
        return response()->json(null, 204);
    }
    public function deleteDestinationSlots($id)
    {
        $destinationSlots = destinationSlots::findOrFail($id);
        $destinationSlots->delete();
        return response()->json(null, 204);
    }
    public function update(Request $request, $id)
    {

        $destination = Destinations::findOrFail($id);

        $destination->title = $request->title;


        $destination->slug = Str::slug($request->title);

        $destination->short_description = $request->short_description;
        $destination->departure_location = $request->departure_location;

        $destination->page_h1_heading = $request->page_h1_heading;
        $destination->meta_title = $request->meta_title;
        $destination->meta_description = $request->meta_description;
        $destination->meta_keywords = $request->meta_keywords;

        $destination->alt_text_cover_image = $request->alt_text_cover_image;
        $destination->alt_text_banner_image = $request->alt_text_banner_image;


        $destination->single_room_price = $request->single_room_price;

        $destination->days = $request->days;
        $destination->nights = $request->nights;

        $departureDateTime = json_encode($request->departure_date_time);
        $destination->departure_date_time = $departureDateTime;

        $destination->currency = $request->currency;
        $destination->destination_country = $request->destination_country;



        $destination->category_id = $request->category;
        $destination->type_id = $request->type;

        $destination->include_in = $request->has('include_in')
            ? json_encode($request->input('include_in'), JSON_UNESCAPED_UNICODE)
            : json_encode([], JSON_UNESCAPED_UNICODE);

        $destination->not_include = $request->has('not_include')
            ? json_encode($request->input('not_include'), JSON_UNESCAPED_UNICODE)
            : json_encode([], JSON_UNESCAPED_UNICODE);

        $destination->extras = $request->has('extras')
            ? json_encode($request->input('extras'), JSON_UNESCAPED_UNICODE)
            : json_encode([], JSON_UNESCAPED_UNICODE);

        // if (isset($request->city)) {
        //     $destinationCities = implode(',', $request->input('city'));
        //     $destination->city = $destinationCities;
        // }
        // if (isset($request->destination_list)) {
        //     $destinationDetail = implode(',', $request->input('destination_list'));
        //     $destination->destination_detail = $destinationDetail;
        // }

        // Handle image upload

        if ($request->hasFile('destination_pdf')) {
            $file = $request->file('destination_pdf');
            $pdfImagePath = media($file, 'storage/destinationPdf');
            $pdfImagePath = basename($pdfImagePath);
            $destination->destination_pdf = $pdfImagePath;
        }

        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $logoImagePath = media($file, 'storage/destinations');
            $logoImagePath = basename($logoImagePath);
            $destination->cover_img = $logoImagePath;
        }
        // Handle image upload
        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $BannerImagePath = media($file, 'storage/destinations');
            $BannerImagePath = basename($BannerImagePath);
            $destination->banner_image = $BannerImagePath;
        }

        // dd($request->image_gallery);
        $existingImagePaths = json_decode($destination->image_gallery, true);
        $newImagePaths = [];
        // if ($request->hasFile('update_image_gallery')) {

        //     foreach ($request->file('update_image_gallery') as $image) {
        //         $imageName = uniqid() . '.' . $image->extension();
        //         $image->move(public_path('storage/destinations'), $imageName);
        //         $imageName = basename($imageName);
        //         $newImagePaths[] = $imageName;
        //     }
        // }

        if ($request->hasFile('update_image_gallery')) {
            foreach ($request->file('update_image_gallery') as $image) {
                $convertedImageName = media($image, 'storage/destinations');
                $newImagePaths[] = $convertedImageName;
            }
        }

        // Merge the new image paths with the existing ones
        $mergedImagePaths = array_merge($existingImagePaths, $newImagePaths);
        $destination->image_gallery = json_encode($mergedImagePaths);

        $editorData = [];
        if ($request->has('editor_data')) {

            $existingEditorData = [];
            if ($destination->destination_long_detail) {
                $existingEditorData = json_decode($destination->destination_long_detail, true) ?? [];
            }

            // new change
            $editors = json_decode($request->input('editor_data'), true);


            foreach ($editors as $key => $editor) {


                $editor_main_image_data = null;

                // Find existing image for this editor by matching day or id
                foreach ($existingEditorData as $existingEditor) {
                    if (($existingEditor['id'] == $key || $existingEditor['day'] == $editor['day'])
                        && isset($existingEditor['main_img'])
                    ) {
                        $editor_main_image_data = $existingEditor['main_img'];
                        break;
                    }
                }

                $fileIndex = $key + 1;

                // Only update image if a new one is uploaded
                if (isset($request->file('main_image')[$fileIndex]) && $request->file('main_image')[$fileIndex]->isValid()) {
                    $file = $request->file('main_image')[$fileIndex];
                    $editor_main_image = media($file, 'storage/destinations');
                    $editor_main_image_data = basename($editor_main_image);
                }


                $content = $editor['content'];

                $content = str_replace('<o:p>', '<p>', $content);
                $content = str_replace('</o:p>', '</p>', $content);

                // Additional cleanup steps
                $content = str_replace('&nbsp;', ' ', $content); // Replace non-breaking spaces with regular spaces
                $content = preg_replace('/<span[^>]*>/', '', $content); // Remove <span> tags and their attributes
                $content = str_replace('</span>', '', $content); // Remove closing </span> tags
                $content = preg_replace('/<p[^>]*>/', '<p>', $content); // Remove <p> tags and their attributes
                libxml_use_internal_errors(true);
                $dom = new \DomDocument();
                $dom->loadHtml($content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
                $images = $dom->getElementsByTagName('img');

                // foreach ($images as $item => $image) {

                //   $src = $image->getAttribute('src');
                //  if (strpos($src, 'data:image') === 0) {

                //        list(, $data) = explode(',', $src);
                //       $imageData = base64_decode($data);
                //      $imageName = uniqid() . '.webp'; // Generate a unique image name
                //        $path = public_path('storage/destinationEditor') . '/' . $imageName;

                //       file_put_contents($path, $imageData);
                //      $image->setAttribute('src', asset('storage/destinationEditor/' . $imageName));

                //        }
                //  }
                //  dd($finalContent);
                //    $finalContent = $dom->saveHTML();


                preg_match_all('/<img[^>]+>/', $content, $matches);
                foreach ($matches[0] as $imageTag) {
                    preg_match('/src="([^"]+)"/', $imageTag, $srcMatches);
                    if (isset($srcMatches[1])) {
                        $src = $srcMatches[1];
                        if (strpos($src, 'data:image') === 0) {
                            list(, $data) = explode(',', $src);
                            $imageData = base64_decode($data);
                            $imageName = uniqid() . '.webp'; // Generate a unique image name
                            $path = public_path('storage/destinationEditor') . '/' . $imageName;
                            file_put_contents($path, $imageData);
                            $newSrc = asset('storage/destinationEditor/' . $imageName);
                            $content = str_replace($src, $newSrc, $content);
                        }
                    }
                }

                $editorData[] = [
                    'id' => $key,
                    'day' => $editor['day'],
                    'content' => $content,
                    'main_img' => $editor_main_image_data,
                ];
            }
        }


        $destination->destination_long_detail = json_encode($editorData);


        $accomodationEditorData = [];
        if ($request->has('accomodation_editor_data')) {

            $existingAccomudationData = [];
            if ($destination->destination_accomodation_detail) {
                $existingAccomudationData = json_decode($destination->destination_accomodation_detail, true) ?? [];
            }

            // new change
            $accomodationEditors = json_decode($request->input('accomodation_editor_data'), true);

            foreach ($accomodationEditors as $key => $accomodationEditor) {

                $acc_main_image_data = null;

                // Find existing image for this editor by matching day or id
                foreach ($existingAccomudationData as $existingAccomudation) {
                    if (($existingAccomudation['id'] == $key || $existingAccomudation['day'] == $editor['day'])
                        && isset($existingAccomudation['main_img'])
                    ) {
                        $acc_main_image_data = $existingAccomudation['main_img'];
                        break;
                    }
                }

                $fileIndex = $key + 1;

                // Only update image if a new one is uploaded
                if (isset($request->file('acc_main_image')[$fileIndex]) && $request->file('acc_main_image')[$fileIndex]->isValid()) {
                    $file = $request->file('acc_main_image')[$fileIndex];
                    $acc_main_image = media($file, 'storage/destinations');
                    $acc_main_image_data = basename($acc_main_image);
                }



                $accomodation_content = $accomodationEditor['accomodation_content'];

                $accomodation_content = str_replace('<o:p>', '<p>', $accomodation_content);
                $accomodation_content = str_replace('</o:p>', '</p>', $accomodation_content);

                // Additional cleanup steps
                $accomodation_content = str_replace('&nbsp;', ' ', $accomodation_content); // Replace non-breaking spaces with regular spaces
                $accomodation_content = preg_replace('/<span[^>]*>/', '', $accomodation_content); // Remove <span> tags and their attributes
                $accomodation_content = str_replace('</span>', '', $accomodation_content); // Remove closing </span> tags
                $accomodation_content = preg_replace('/<p[^>]*>/', '<p>', $accomodation_content); // Remove <p> tags and their attributes
                libxml_use_internal_errors(true);
                $dom = new \DomDocument();
                $dom->loadHtml($accomodation_content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
                $images = $dom->getElementsByTagName('img');



                preg_match_all('/<img[^>]+>/', $accomodation_content, $matches);
                foreach ($matches[0] as $imageTag) {
                    preg_match('/src="([^"]+)"/', $imageTag, $srcMatches);
                    if (isset($srcMatches[1])) {
                        $src = $srcMatches[1];
                        if (strpos($src, 'data:image') === 0) {
                            list(, $data) = explode(',', $src);
                            $imageData = base64_decode($data);
                            $imageName = uniqid() . '.webp'; // Generate a unique image name
                            $path = public_path('storage/destinationEditor') . '/' . $imageName;
                            file_put_contents($path, $imageData);
                            $newSrc = asset('storage/destinationEditor/' . $imageName);
                            $accomodation_content = str_replace($src, $newSrc, $accomodation_content);
                        }
                    }
                }

                $accomodationEditorData[] = [
                    'id' => $key,
                    'day' => $accomodationEditor['accomodation_day'],
                    'content' => $accomodation_content,
                    'main_img' => $acc_main_image_data,
                ];
            }
        }


        $destination->destination_accomodation_detail = json_encode($accomodationEditorData);

        if ($request->filled('price')) {
            $destination->price = $request->price;
        }
        if ($request->filled('booking_slots')) {
            $destination->total_slots = $request->booking_slots;
        }
        if ($request->filled('final_price')) {
            $destination->final_price = $request->final_price;
        }
        if ($request->filled('admin_commision')) {
            $destination->admin_commision = $request->admin_commision;
        }

        if ($request->filled('two_person_price')) {
            $destination->two_person_price = $request->two_person_price;
        }
        if ($request->filled('three_to_four_price')) {
            $destination->three_to_four_price = $request->three_to_four_price;
        }
        if ($request->filled('five_to_six_price')) {
            $destination->five_to_six_price = $request->five_to_six_price;
        }
        if ($request->filled('six_plus_price')) {
            $destination->six_plus_price = $request->six_plus_price;
        }
        if ($request->filled('two_final_price')) {
            $destination->two_final_price = $request->two_final_price;
        }
        if ($request->filled('three_to_four_final_price')) {
            $destination->three_to_four_final_price = $request->three_to_four_final_price;
        }
        if ($request->filled('five_to_six_final_price')) {
            $destination->five_to_six_final_price = $request->five_to_six_final_price;
        }
        if ($request->filled('six_plus_final_price')) {
            $destination->six_plus_final_price = $request->six_plus_final_price;
        }

        $destination->save();

        return response()->json($destination, 200);
    }

    public function deleteGalleryImage(Request $request)
    {

        $destination = Destinations::find($request->destinationId);

        $images = json_decode($destination->image_gallery);
        // dd($images);
        // Check if the image exists in the array
        if (isset($images[$request->id])) {
            // Get the image path to delete
            $imagePath = $images[$request->id];
            // Delete the image from storage
            // Storage::delete($imagePath);
            // Remove the image from the array
            unset($images[$request->id]);

            // Update the image_gallery field in the database
            $destination->image_gallery = json_encode(array_values($images));
            $destination->save();

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false, 'message' => 'Image not found in gallery']);
    }

    public function destroy($id)
    {
        $destination = Destinations::findOrFail($id);
        $destination->delete();
        return response()->json(null, 204);
    }
    public function status($id)
    {
        $destination = Destinations::findOrFail($id);
        $destination->status = !$destination->status; // Toggle status
        $destination->save();
        return response()->json($destination, 200);
    }
    public function popular($id)
    {
        $destination = Destinations::findOrFail($id);
        $destination->popular = !$destination->popular; // Toggle status
        $destination->save();
        return response()->json($destination, 200);
    }
    public function trending($id)
    {
        $destination = Destinations::findOrFail($id);
        $destination->trending = !$destination->trending; // Toggle status
        $destination->save();
        return response()->json($destination, 200);
    }
    public function makePrivate($id)
    {
        $destination = Destinations::findOrFail($id);
        $destination->flag = !$destination->flag; // Toggle status
        $destination->save();
        return response()->json($destination, 200);
    }

    public function destinationCategoryList()
    {
        $destinationCatList = DestinationCategory::all();

        return view('dashboard.destinations.destinationCategoryList', ['destinationCatList' => $destinationCatList]);
    }

    public function destinationCategoryStore(Request $request)
    {

        $destinationCategory = new DestinationCategory();
        $destinationCategory->title = $request->title;

        $destinationCategory->page_h1_heading = $request->page_h1_heading;
        $destinationCategory->meta_title = $request->meta_title;
        $destinationCategory->meta_description = $request->meta_description;
        $destinationCategory->meta_keywords = $request->meta_keywords;

        $destinationCategory->alt_text_cover_image = $request->alt_text_cover_image;
        $destinationCategory->alt_text_banner_image = $request->alt_text_banner_image;

        $destinationCategory->slug = Str::slug($request->title);

        if (DestinationCategory::where('slug', $destinationCategory->slug)->exists()) {
            return response()->json(['message' => 'Category with the same slug already exists'], 422);
        }

        // Handle image upload
        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $destinationCategoryPath = media($file, 'storage/destinations');
            $destinationCategoryPath = basename($destinationCategoryPath);
            $destinationCategory->cover_image = $destinationCategoryPath;
        }

        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $destinationCategoryBannerPath = media($file, 'storage/destinations');
            $destinationCategoryBannerPath = basename($destinationCategoryBannerPath);
            $destinationCategory->banner_image = $destinationCategoryBannerPath;
        }
        $destinationCategory->save();
        return response()->json($destinationCategory, 200);
    }

    public function destinationCategoryDestroy($id)
    {
        $destinationCategory = DestinationCategory::findOrFail($id);
        $destinationCategory->delete();
        return response()->json(null, 204);
    }

    public function destinationCategoryUpdate(Request $request, $id)
    {
        $destinationCategory = DestinationCategory::findOrFail($id);

        $destinationCategory->title = $request->title;
        $destinationCategory->page_h1_heading = $request->page_h1_heading;
        $destinationCategory->meta_title = $request->meta_title;
        $destinationCategory->meta_description = $request->meta_description;
        $destinationCategory->meta_keywords = $request->meta_keywords;

        $destinationCategory->alt_text_cover_image = $request->alt_text_cover_image;
        $destinationCategory->alt_text_banner_image = $request->alt_text_banner_image;

        $newSlug = Str::slug($request->title);

        // Check if the slug has changed
        if ($destinationCategory->slug !== $newSlug) {
            if (DestinationCategory::where('slug', $newSlug)->exists()) {
                return response()->json(['message' => 'Category with the same slug already exists'], 422);
            }
            $destinationCategory->slug = $newSlug;
        }

        // Handle image upload
        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $destinationCategoryPath = media($file, 'storage/destinations');
            $destinationCategoryPath = basename($destinationCategoryPath);
            $destinationCategory->cover_image = $destinationCategoryPath;
        }

        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $destinationCategoryBannerPath = media($file, 'storage/destinations');
            $destinationCategoryBannerPath = basename($destinationCategoryBannerPath);
            $destinationCategory->banner_image = $destinationCategoryBannerPath;
        }
        $destinationCategory->save();
        return response()->json($destinationCategory, 200);
    }

    public function destinationTypeList()
    {
        $destinationTypeList = DestinationType::all();

        return view('dashboard.destinations.destinationTypeList', ['destinationTypeList' => $destinationTypeList]);
    }

    public function destinationTypeStore(Request $request)
    {

        $destinationType = new DestinationType();
        $destinationType->title = $request->title;

        $destinationType->page_h1_heading = $request->page_h1_heading;
        $destinationType->meta_title = $request->meta_title;
        $destinationType->meta_description = $request->meta_description;
        $destinationType->meta_keywords = $request->meta_keywords;

        $destinationType->alt_text_cover_image = $request->alt_text_cover_image;
        $destinationType->alt_text_banner_image = $request->alt_text_banner_image;

        $destinationType->slug = Str::slug($request->title);

        if (DestinationType::where('slug', $destinationType->slug)->exists()) {
            return response()->json(['message' => 'Type with the same slug already exists'], 422);
        }

        // Handle image upload
        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $destinationTypePath = media($file, 'storage/destinations');
            $destinationTypePath = basename($destinationTypePath);
            $destinationType->cover_image = $destinationTypePath;
        }
        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $destinationTypeBannerPath = media($file, 'storage/destinations');
            $destinationTypeBannerPath = basename($destinationTypeBannerPath);
            $destinationType->banner_image = $destinationTypeBannerPath;
        }

        $destinationType->save();
        return response()->json($destinationType, 200);
    }

    public function destinationTypeDestroy($id)
    {
        $destinationType = DestinationType::findOrFail($id);
        $destinationType->delete();
        return response()->json(null, 204);
    }

    public function destinationTypeUpdate(Request $request, $id)
    {
        $destinationType = DestinationType::findOrFail($id);

        $destinationType->title = $request->title;

        $destinationType->page_h1_heading = $request->page_h1_heading;
        $destinationType->meta_title = $request->meta_title;
        $destinationType->meta_description = $request->meta_description;
        $destinationType->meta_keywords = $request->meta_keywords;

        $destinationType->alt_text_cover_image = $request->alt_text_cover_image;
        $destinationType->alt_text_banner_image = $request->alt_text_banner_image;

        $newSlug = Str::slug($request->title);

        // Check if the slug has changed
        if ($destinationType->slug !== $newSlug) {
            if (DestinationType::where('slug', $newSlug)->exists()) {
                return response()->json(['message' => 'Type with the same slug already exists'], 422);
            }
            $destinationType->slug = $newSlug;
        }

        // Handle image upload
        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $destinationTypePath = media($file, 'storage/destinations');
            $destinationTypePath = basename($destinationTypePath);
            $destinationType->cover_image = $destinationTypePath;
        }
        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $destinationTypeBannerPath = media($file, 'storage/destinations');
            $destinationTypeBannerPath = basename($destinationTypeBannerPath);
            $destinationType->banner_image = $destinationTypeBannerPath;
        }

        $destinationType->save();
        return response()->json($destinationType, 200);
    }

    public function destinationCountryLocationsList()
    {
        $countryAndLocations = countryAndLocations::all();
        return view('dashboard.destinations.countryAndLocations.countryAndLocations', ['countryAndLocations' => $countryAndLocations]);
    }

    public function destinationCountryLocationsStore(Request $request)
    {

        $countryAndLocations = new countryAndLocations();
        $countryAndLocations->country = $request->country;
        $countryAndLocations->locations = $request->locations;
        $countryAndLocations->city = $request->city;

        if (countryAndLocations::where('country', $request->country)->where('city', $request->city)->exists()) {
            return response()->json(['message' => 'country and city with the same name already exists'], 422);
        }
        $countryAndLocations->save();
        return response()->json($countryAndLocations, 200);
    }

    public function destinationCountryLocationsUpdate(Request $request, $id)
    {
        $destinationCountryLocation = countryAndLocations::findOrFail($id);

        // $destinationCountryLocation->country = $request->country;
        $newCountry = $request->country;
        $newCity = $request->country;

        // Check if the slug has changed
        if ($destinationCountryLocation->country !== $newCountry && $destinationCountryLocation->city !== $newCity) {
            if (countryAndLocations::where('country', $newCountry)->where('city', $newCity)->exists()) {
                return response()->json(['message' => 'Country and City with the same name already exists'], 422);
            }
            $destinationCountryLocation->country = $newCountry;
            $destinationCountryLocation->city = $newCity;
        }

        $destinationCountryLocation->locations = $request->locations;

        $destinationCountryLocation->save();
        return response()->json($destinationCountryLocation, 200);
    }

    public function destinationCountryLocationsDestroy($id)
    {
        $destinationCountryLocation = countryAndLocations::findOrFail($id);
        $destinationCountryLocation->delete();
        return response()->json(null, 204);
    }

    public function destinationIncludeList()
    {
        $destinationIncludes = Auth::user()->destinationIncludes;

        return view('dashboard.destinations.destinationIncludes.listing', ['destinationIncludes' => $destinationIncludes]);
    }

    public function destinationIncludeStore(Request $request)
    {
        $destinationInclude = new destination_includes();
        $destinationInclude->title = $request->title;
        if (destination_includes::where('title', $request->title)->exists()) {
            return response()->json(['message' => 'include with same name already exists'], 422);
        }
        $destinationInclude->user_id = Auth::id();
        $destinationInclude->save();
        return response()->json($destinationInclude, 200);
    }

    public function destinationIncludeUpdate(Request $request, $id)
    {
        $destinationInclude = destination_includes::findOrFail($id);

        $newInclude = $request->title;

        if ($destinationInclude->title !== $newInclude) {
            if (destination_includes::where('title', $newInclude)->exists()) {
                return response()->json(['message' => 'already exists'], 422);
            }
            $destinationInclude->title = $newInclude;
        }

        $destinationInclude->save();
        return response()->json($destinationInclude, 200);
    }

    public function destinationIncludeDestroy($id)
    {
        $destinationInclude = destination_includes::findOrFail($id);
        $destinationInclude->delete();
        return response()->json(null, 204);
    }

    public function destinationNotIncludeList()
    {

        $destinationNotIncludes = Auth::user()->destinationNotIncludes;

        return view('dashboard.destinations.destinationNotIncludes.listing', ['destinationNotIncludes' => $destinationNotIncludes]);
    }
    public function destinationNotIncludeStore(Request $request)
    {
        $destinationNotInclude = new destination_not_includes();
        $destinationNotInclude->title = $request->title;
        if (destination_not_includes::where('title', $request->title)->exists()) {
            return response()->json(['message' => 'not include with same name already exists'], 422);
        }
        $destinationNotInclude->user_id = Auth::id();
        $destinationNotInclude->save();
        return response()->json($destinationNotInclude, 200);
    }

    public function destinationNotIncludeUpdate(Request $request, $id)
    {
        $destinationNotInclude = destination_not_includes::findOrFail($id);

        $newNotInclude = $request->title;

        if ($destinationNotInclude->title !== $newNotInclude) {
            if (destination_not_includes::where('title', $newNotInclude)->exists()) {
                return response()->json(['message' => 'already exists'], 422);
            }
            $destinationNotInclude->title = $newNotInclude;
        }

        $destinationNotInclude->save();
        return response()->json($destinationNotInclude, 200);
    }

    public function destinationNotIncludeDestroy($id)
    {
        $destinationNotInclude = destination_not_includes::findOrFail($id);
        $destinationNotInclude->delete();
        return response()->json(null, 204);
    }

    public function destinationExtrasList()
    {

        $destinationExtras = Auth::user()->destinationExtras;


        return view('dashboard.destinations.destinationExtras.listing', ['destinationExtras' => $destinationExtras]);
    }

    public function destinationNotExtrasStore(Request $request)
    {
        $destinationExtras = new destination_extras();
        $destinationExtras->title = $request->title;
        $destinationExtras->price = $request->price;
        if (destination_extras::where('title', $request->title)->exists()) {
            return response()->json(['message' => 'extras with same name already exists'], 422);
        }
        $destinationExtras->user_id = Auth::id();
        $destinationExtras->save();
        return response()->json($destinationExtras, 200);
    }

    public function destinationNotExtrasUpdate(Request $request, $id)
    {
        $destinationExtras = destination_extras::findOrFail($id);

        $newExtra = $request->title;

        if ($destinationExtras->title !== $newExtra) {
            if (destination_extras::where('title', $newExtra)->exists()) {
                return response()->json(['message' => 'already exists'], 422);
            }
            $destinationExtras->title = $newExtra;
        }
        $destinationExtras->price = $request->price;
        $destinationExtras->save();
        return response()->json($destinationExtras, 200);
    }

    public function destinationNotExtrasDestroy($id)
    {
        $destinationExtras = destination_extras::findOrFail($id);
        $destinationExtras->delete();
        return response()->json(null, 204);
    }
    public function showDestinationBooking($id)
    {
        $booking = DestinationBooking::findOrFail($id);
        $destinationExtras = Auth::user()->destinationExtras;
        return view('dashboard.destinationBooking.updateBooking', compact('booking', 'destinationExtras'));
    }
    public function updateDestinationBooking(Request $request, $id)
    {
        $booking = DestinationBooking::findOrFail($id);

        $booking->tickets = $request->tickets;
        $booking->adultes = $request->adultes;
        $booking->children = $request->children;
        $booking->seperate_room = $request->seperate_room;
        $booking->single_price = $request->single_price;
        $booking->date = $request->date;

        // Handle extras field safely
        if ($request->has('extras') && is_array($request->extras) && count($request->extras) > 0) {
            $booking->extras = implode(',', $request->extras);
        } else {
            $booking->extras = null; // or '' if your DB doesn't allow null
        }

        $booking->save();

        return response()->json($booking, 200);
    }




    public function paymentUpdate(Request $request, $id)
    {
        $payment = bookingPayments::findOrFail($id);

        $payment->charges = $request->charges;
        $payment->receive_payment = $request->receive_amount;

        $payment->save();
        return response()->json($payment, 200);
    }




    public function duplicateRecord(Request $request, $id)
    {
        $originalRecord = Destinations::find($id);

        if (!$originalRecord) {
            return response()->json(['error' => 'Record not found'], 404);
        }

        // Clone the original record
        $newRecord = $originalRecord->replicate();
        $newRecord->created_at = now();
        $newRecord->updated_at = now();

        // Generate unique title
        $baseTitle = $originalRecord->title;
        $newTitle = $baseTitle . ' (copy)';

        // Find the maximum number suffix in the existing records
        $maxSuffix = Destinations::where('title', 'like', $baseTitle . ' (copy)%')
            ->select(DB::raw('COALESCE(SUBSTRING_INDEX(title, " ", -1) + 0, 0) AS suffix'))
            ->orderBy('suffix', 'desc')
            ->pluck('suffix')
            ->first();

        $nextSuffix = $maxSuffix + 1;

        if ($nextSuffix > 9) { // Adjust this limit based on your requirement for suffix length
            $nextSuffix = 10; // If you want a fixed length or different behavior, adjust this
        }

        $newTitle = $baseTitle . ' (copy' . ($nextSuffix > 0 ? ' ' . $nextSuffix : '') . ')';
        $newRecord->title = $newTitle;

        // Save the new record
        $newRecord->save();

        return response()->json(['success' => 'Record duplicated successfully!']);
    }
}
