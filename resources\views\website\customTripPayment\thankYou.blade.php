@extends('website.include.layout')
@section('title', 'Thank you')

@section('Default Meta Title')
@section('Default Meta Keywords')
@section('Default Meta Description')
@section('Default H1 Heading')


@section('content')

<style>
.thankyou_page_box {
    background: var(--secondary-bg-color);
    max-width: 450px;
    margin: 0 auto;
    border-radius: 8px;
    padding: 25px 25px;
}

.thankyou_page_box figure {
    width: 83px;
    margin-bottom: 25px;
}

.thankyou_page_box h5 {
    font-size: 20px;
    font-weight: 700;
    line-height: 25.2px;
    color: var(--main-text-color);
    padding-bottom: 20px;
}

.thankyou_page_box p {
    font-size: 16px;
    font-weight: 500;
    line-height: 20.16px;
    color: var(--custom-text-color-one);
    padding-bottom: 20px;
}

.thankyou_page_box a {
    background: var(--primary-bg-color);
    border-radius: 10px;
    color: var(--primary-text-color);
    border-radius: 10px;
    font-size: 16px;
    font-weight: 700;
    line-height: 20.16px;
    padding: 14px 10px;
}
</style>
<section class="login_section display-block float-left w-100 mt-5">

    <div style="max-width: 540px;margin:0 auto;">
        <div class="login_section_box d-grid w-100 align-items-center">
            <div class="login_tabs_main  d-flex w-100 align-items-center justify-content-center w-100 flex-column">
                <div class="login_section_box_heading d-grid w-100">


                    <div
                        class="thankyou_page_box d-flex w-100 align-items-center justify-content-center text-center flex-column">
                        <!-- <figure><img class="w-100" src="images/thankyou.png"></figure> -->
                        <h5>Payment Successfull</h5>
                        <p>check your profile for more details</p>
                        <p>Login is required to access profile </p>
                        <a class="w-100" href="/user/profile/bookingdetails">Profile</a>
                    </div>

                </div>
            </div>

        </div>

    </div>
</section>

@endsection