<?php

namespace App\Http\Controllers\website;

use App\Http\Controllers\Controller;
use App\Models\adminData;
use App\Models\bookingPayments;
use App\Models\customTrips;
use App\Models\DestinationBooking;
use App\Models\Destinations;
use App\Models\destinationSlots;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;


class paymentController extends Controller
{
    // direct payment
    public function customTripDirectPayment(Request $request)
    {
        if ($request->trip_type == 'custom') {
            $customTripdata = customTrips::where('id', $request->id)->first();
            $user = User::where('id', $request->user_id)->first();
            if ($request->payment_type === 'advance') {
                $customTripdata->status = 'advancePaymentSuccess';

            } elseif ($request->payment_type === 'remaining') {
                $customTripdata->status = 'remainingPaymentSuccess';

            } elseif ($request->payment_type === 'complete') {
                $customTripdata->status = 'completePaymentSuccess';

            }
            $payment = new bookingPayments();
            $payment->custom_trip_id = $request->id;
            $payment->payment_type = $request->payment_type;
            $payment->price = $request->price;
            $payment->user_id = $request->user_id;
            $payment->trip_type = $request->trip_type;

            if ($request->hasFile('payment_slip')) {
                $file = $request->file('payment_slip');
                $advanceDirectPaymentImagePath = media($file, 'storage/directPaymentSlip');
                $advanceDirectPaymentImagePath = basename($advanceDirectPaymentImagePath);
                $payment->direct_slip = $advanceDirectPaymentImagePath;
            }
            $customTripdata->save();
            $payment->save();
            $adminData = adminData::first();
            if ($request->payment_type === 'advance') {
                sendMail([
                    'view' => 'emailTemplates.admin_custom_trip_advance_payment_template',
                    'to' => $adminData['primary_email'],
                    'subject' => 'Advance payment',
                    'data' => [
                        'custom_trip_id' => $request->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);

                sendMail([
                    'view' => 'emailTemplates.user_custom_trip_advance_payment_template',
                    'to' => $user->email,
                    'subject' => 'Advance payment',
                    'data' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);
            } elseif ($request->payment_type === 'remaining') {
                sendMail([
                    'view' => 'emailTemplates.admin_custom_trip_remaining_payment_template',
                    'to' => $adminData['primary_email'],
                    'subject' => 'Remaining payment',
                    'data' => [
                        'custom_trip_id' => $request->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);

                sendMail([
                    'view' => 'emailTemplates.user_custom_trip_remaining_payment_template',
                    'to' => $user->email,
                    'subject' => 'Remaining payment',
                    'data' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);
            } elseif ($request->payment_type === 'complete') {
                sendMail([
                    'view' => 'emailTemplates.admin_custom_trip_complete_payment_template',
                    'to' => $adminData['primary_email'],
                    'subject' => 'Complete payment',
                    'data' => [
                        'custom_trip_id' => $request->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);

                sendMail([
                    'view' => 'emailTemplates.user_custom_trip_complete_payment_template',
                    'to' => $user->email,
                    'subject' => 'Complete payment',
                    'data' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);
            }

            return response()->json(['success' => 'Payment submit successfully', 'route' => route('thank-you')]);

        } elseif ($request->trip_type == 'booking') {

            $Bookingdata = DestinationBooking::where('id', $request->id)->first();
            $Destinationdata = Destinations::where('id', $Bookingdata->destination_id)->first();
            $Userdata = User::where('id', $Destinationdata->user_id)->first();

            $user = User::where('id', $request->user_id)->first();

            if ($request->payment_type === 'advance') {
                $Bookingdata->status = 'advancePaymentSuccess';

            } elseif ($request->payment_type === 'remaining') {
                $Bookingdata->status = 'remainingPaymentSuccess';

            } elseif ($request->payment_type === 'complete') {
                $Bookingdata->status = 'completePaymentSuccess';

            }
            $payment = new bookingPayments();
            $payment->booking_id = $request->id;
            $payment->payment_type = $request->payment_type;
            $payment->price = $request->price;
            $payment->user_id = $request->user_id;
            $payment->trip_type = $request->trip_type;

            if ($request->hasFile('payment_slip')) {
                $file = $request->file('payment_slip');
                $advanceDirectPaymentImagePath = media($file, 'storage/directPaymentSlip');
                $advanceDirectPaymentImagePath = basename($advanceDirectPaymentImagePath);
                $payment->direct_slip = $advanceDirectPaymentImagePath;
            }
            $Bookingdata->save();
            $payment->save();
            $adminData = adminData::first();
            if ($Userdata->role !== 'admin') {
                // User is not an admin, so use the email from $Userdata
                $recipientEmail = $Userdata->email;
            } else {
                // User is an admin, so use the primary email from $adminData
                $recipientEmail = $adminData['primary_email'];
            }
            if ($request->payment_type === 'advance') {
                $slotsupdate = destinationSlots::where('destination_id', $Bookingdata->destination_id)->where('date', $Bookingdata->date)->first();
                $slotsupdate->book_slots = $Bookingdata->tickets;
                $slotsupdate->save();

                sendMail([
                    'view' => 'emailTemplates.admin_booking_trip_advance_payment_template',
                    'to' => $recipientEmail,
                    'subject' => 'Advance payment',
                    'data' => [
                        'booked_destination_id' => $request->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);

                sendMail([
                    'view' => 'emailTemplates.user_booking_trip_advance_payment_template',
                    'to' => $user->email,
                    'subject' => 'Advance payment',
                    'data' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);
            } elseif ($request->payment_type === 'remaining') {
                sendMail([
                    'view' => 'emailTemplates.admin_booking_trip_remaining_payment_template',
                    'to' => $recipientEmail,
                    'subject' => 'Remaining payment',
                    'data' => [
                        'booking_id' => $request->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);

                sendMail([
                    'view' => 'emailTemplates.user_booking_trip_remaining_payment_template',
                    'to' => $user->email,
                    'subject' => 'Remaining payment',
                    'data' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);
            } elseif ($request->payment_type === 'complete') {
                $slotsupdate = destinationSlots::where('destination_id', $Bookingdata->destination_id)->where('date', $Bookingdata->date)->first();
                $slotsupdate->book_slots = $Bookingdata->tickets;
                $slotsupdate->save();
                sendMail([
                    'view' => 'emailTemplates.admin_booking_trip_complete_payment_template',
                    'to' => $recipientEmail,
                    'subject' => 'Complete payment',
                    'data' => [
                        'booking_id' => $request->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);

                sendMail([
                    'view' => 'emailTemplates.user_booking_trip_complete_payment_template',
                    'to' => $user->email,
                    'subject' => 'Complete payment',
                    'data' => [
                        'name' => $user->name,
                        'email' => $user->email,
                        'price' => $request->price,
                    ],
                ]);
            }

            return response()->json(['success' => 'Payment submit successfully', 'route' => route('thank-you')]);

        } else {
            return response()->json(['error' => 'System error']);
        }
    }

    public function stripPayment(Request $request)
    {
        // Assuming $request->price holds the price in dollars
        $priceInDollars = $request->price; 

       // Convert price to cents (multiply by 100) 
       $priceInCents = $priceInDollars * 100;
       
        try {
            // Initialize Stripe with your API key
            \Stripe\Stripe::setApiKey(env('STRIPE_SECRET'));

            // Use the token created by Stripe Elements
            $paymentIntent = \Stripe\PaymentIntent::create([
                'amount' => $priceInCents,
                'currency' => 'usd',
                'payment_method_types' => ['card'],
                'description' => 'destination booking payment',
                'payment_method_data' => [
                    'type' => 'card',
                    'card' => [
                        'token' => $request->token,
                    ],
                ],
                'confirm' => true, // Confirm the payment intent immediately
            ]);

            // Handle the payment intent status (succeeded, requires_action, etc.)
            if ($paymentIntent->status === 'succeeded') {
                
           
                if ($request->trip_type == 'custom') {
                    $customTripdata = customTrips::where('id', $request->id)->first();
                    $user = User::where('id', $request->user_id)->first();
                    if ($request->payment_type === 'advance') {
                        $customTripdata->status = 'advancePaymentSuccess';

                    } elseif ($request->payment_type === 'remaining') {
                        $customTripdata->status = 'remainingPaymentSuccess';

                    } elseif ($request->payment_type === 'complete') {
                        $customTripdata->status = 'completePaymentSuccess';

                    }
                    $payment = new bookingPayments();
                    $payment->custom_trip_id = $request->id;
                    $payment->payment_type = $request->payment_type;
                    $payment->price = $request->price;
                    $payment->user_id = $request->user_id;
                    $payment->trip_type = $request->trip_type;
                     $payment->payment_id = $paymentIntent->payment_method;
                   
                    
                    if ($request->hasFile('payment_slip')) {
                        $file = $request->file('payment_slip');
                        $advanceDirectPaymentImagePath = media($file, 'storage/directPaymentSlip');
                        $advanceDirectPaymentImagePath = basename($advanceDirectPaymentImagePath);
                        $payment->direct_slip = $advanceDirectPaymentImagePath;
                    }
                    $customTripdata->save();
                    $payment->save();
                    $adminData = adminData::first();
                    if ($request->payment_type === 'advance') {
                        sendMail([
                            'view' => 'emailTemplates.admin_custom_trip_advance_payment_template',
                            'to' => $adminData['primary_email'],
                            'subject' => 'Advance payment',
                            'data' => [
                                'custom_trip_id' => $request->id,
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);

                        sendMail([
                            'view' => 'emailTemplates.user_custom_trip_advance_payment_template',
                            'to' => $user->email,
                            'subject' => 'Advance payment',
                            'data' => [
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);
                    } elseif ($request->payment_type === 'remaining') {
                        sendMail([
                            'view' => 'emailTemplates.admin_custom_trip_remaining_payment_template',
                            'to' => $adminData['primary_email'],
                            'subject' => 'Remaining payment',
                            'data' => [
                                'custom_trip_id' => $request->id,
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);

                        sendMail([
                            'view' => 'emailTemplates.user_custom_trip_remaining_payment_template',
                            'to' => $user->email,
                            'subject' => 'Remaining payment',
                            'data' => [
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);
                    } elseif ($request->payment_type === 'complete') {
                        sendMail([
                            'view' => 'emailTemplates.admin_custom_trip_complete_payment_template',
                            'to' => $adminData['primary_email'],
                            'subject' => 'Complete payment',
                            'data' => [
                                'custom_trip_id' => $request->id,
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);

                        sendMail([
                            'view' => 'emailTemplates.user_custom_trip_complete_payment_template',
                            'to' => $user->email,
                            'subject' => 'Complete payment',
                            'data' => [
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);
                    }

                    return response()->json(['success' => 'Payment submit successfully', 'route' => route('thank-you')]);

                } elseif ($request->trip_type == 'booking') {

                    $Bookingdata = DestinationBooking::where('id', $request->id)->first();
                    $Destinationdata = Destinations::where('id', $Bookingdata->destination_id)->first();
                    $Userdata = User::where('id', $Destinationdata->user_id)->first();

                    $user = User::where('id', $request->user_id)->first();

                    if ($request->payment_type === 'advance') {
                        $Bookingdata->status = 'advancePaymentSuccess';

                    } elseif ($request->payment_type === 'remaining') {
                        $Bookingdata->status = 'remainingPaymentSuccess';

                    } elseif ($request->payment_type === 'complete') {
                        $Bookingdata->status = 'completePaymentSuccess';

                    }
                    $payment = new bookingPayments();
                    $payment->booking_id = $request->id;
                    $payment->payment_type = $request->payment_type;
                    $payment->price = $request->price;
                    $payment->user_id = $request->user_id;
                    $payment->trip_type = $request->trip_type;
                    $payment->payment_id = $paymentIntent->payment_method;

                    if ($request->hasFile('payment_slip')) {
                        $file = $request->file('payment_slip');
                        $advanceDirectPaymentImagePath = media($file, 'storage/directPaymentSlip');
                        $advanceDirectPaymentImagePath = basename($advanceDirectPaymentImagePath);
                        $payment->direct_slip = $advanceDirectPaymentImagePath;
                    }
                    $Bookingdata->save();
                    $payment->save();
                    $adminData = adminData::first();
                    if ($Userdata->role !== 'admin') {
                        // User is not an admin, so use the email from $Userdata
                        $recipientEmail = $Userdata->email;
                    } else {
                        // User is an admin, so use the primary email from $adminData
                        $recipientEmail = $adminData['primary_email'];
                    }
                    if ($request->payment_type === 'advance') {
                        $slotsupdate = destinationSlots::where('destination_id', $Bookingdata->destination_id)->where('date', $Bookingdata->date)->first();
                        $slotsupdate->book_slots = $Bookingdata->tickets;
                        $slotsupdate->save();
                        sendMail([
                            'view' => 'emailTemplates.admin_booking_trip_advance_payment_template',
                            'to' => $recipientEmail,
                            'subject' => 'Advance payment',
                            'data' => [
                                'booked_destination_id' => $request->id,
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);

                        sendMail([
                            'view' => 'emailTemplates.user_booking_trip_advance_payment_template',
                            'to' => $user->email,
                            'subject' => 'Advance payment',
                            'data' => [
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);
                    } elseif ($request->payment_type === 'remaining') {
                        sendMail([
                            'view' => 'emailTemplates.admin_booking_trip_remaining_payment_template',
                            'to' => $recipientEmail,
                            'subject' => 'Remaining payment',
                            'data' => [
                                'booking_id' => $request->id,
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);

                        sendMail([
                            'view' => 'emailTemplates.user_booking_trip_remaining_payment_template',
                            'to' => $user->email,
                            'subject' => 'Remaining payment',
                            'data' => [
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);
                    } elseif ($request->payment_type === 'complete') {
                        $slotsupdate = destinationSlots::where('destination_id', $Bookingdata->destination_id)->where('date', $Bookingdata->date)->first();
                        $slotsupdate->book_slots = $Bookingdata->tickets;
                        $slotsupdate->save();
                        sendMail([
                            'view' => 'emailTemplates.admin_booking_trip_complete_payment_template',
                            'to' => $recipientEmail,
                            'subject' => 'Complete payment',
                            'data' => [
                                'booking_id' => $request->id,
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);

                        sendMail([
                            'view' => 'emailTemplates.user_booking_trip_complete_payment_template',
                            'to' => $user->email,
                            'subject' => 'Complete payment',
                            'data' => [
                                'name' => $user->name,
                                'email' => $user->email,
                                'price' => $request->price,
                            ],
                        ]);
                    }

                    return response()->json(['success' => 'Payment submit successfully', 'route' => route('thank-you')]);

                } else {
                    return response()->json(['error' => 'System error']);
                }
                // Payment succeeded, you can save the payment details to your database
                // return response()->json(['paymentIntentId' => $paymentIntent->id]);
            } else {
                // Payment requires further action, handle as needed
                return response()->json(['error' => 'Payment requires further action'], 400);
            }
        } catch (\Stripe\Exception\ApiErrorException $ex) {
            // Handle any Stripe API errors
            return response()->json(['error' => $ex->getMessage()], 500);
        } catch (Exception $ex) {
            // Handle any other exceptions
            return response()->json(['error' => 'An error occurred'], 500);
        }
    }



}
