// Skeleton loader HTML
const skeletonLoaderHTML = `
 <div class="single_plan_box d-flex w-100 flex-column">
 <div class="loading-skeleton bg_box d-flex w-100 flex-column h-100 justify-content-between">
     <div class="single_plan_box_data_header d-flex w-100 align-items-center justify-content-center flex-column text-center">
         <h3>free</h3>
         <span><strong>ETB 123</strong>/year</span>
     </div>
     
     <ul class="single_plan_features_list">
             <li>
                 <small>Tag1</small>
                 <i class="fas fa-check"></i>
             </li>
             <li>
                 <small>Tag2</small>
                 <i class="fas fa-check"></i>
             </li>
         
             <li>
                 <small>Tag3</small>
                 <i class="fas fa-check"></i>
             </li>
         
     </ul>
     <div class="single_plan_btn d-flex w-100 align-items-center justify-content-center flex-column">
     <a class="loading-skeleton d-flex w-100 align-items-center justify-content-center flex-column">Subscribe</a>
     </div>
 </div>
 </div>
 `;
document.querySelector(".agent_plan_list_box").innerHTML =
    skeletonLoaderHTML.repeat(4);

document.addEventListener("DOMContentLoaded", function () {
    //get subscrition data

    let baseURL = window.location.origin;
    let apiEndpoint = "/api/subscriptions?status=1";
    let apiUrl = baseURL + apiEndpoint;

    // Display skeleton loader

    const isLoggedIn = fetch(apiUrl)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((subscriptionsData) => {
            console.log(subscriptionsData);
            const pakagesList = document.querySelector(".agent_plan_list_box");
            // Clear existing list
            pakagesList.innerHTML = "";

            subscriptionsData.forEach((subscription) => {
                const featuresArray = JSON.parse(subscription.features);

                const subscriptionBox = document.createElement("div");
                subscriptionBox.classList.add(
                    "single_plan_box",
                    "d-flex",
                    "w-100",
                    "flex-column"
                );

                subscriptionBox.innerHTML = `
        <div class="bg_box d-flex w-100 flex-column h-100 justify-content-between">
            <div class="single_plan_box_data_header d-flex w-100 align-items-center justify-content-center flex-column text-center">
                <h3>${subscription.package_type}</h3>
                <span><strong>${subscription.currency} ${
                    subscription.price
                }</strong>/${subscription.duration}</span>
            </div>
            
            <ul class="single_plan_features_list">
                ${featuresArray
                    .map(
                        (feature) => `
                    <li>
                        <small>${feature.value}</small>
                        <i class="fas fa-check"></i>
                    </li>
                `
                    )
                    .join("")}
            </ul>
            <div class="single_plan_btn d-flex w-100 align-items-center justify-content-center flex-column">
            <a class="d-flex w-100 align-items-center justify-content-center flex-column" href="/subscribe/${
                subscription.package_type
            }">Subscribe Now!</a>
            </div>
        </div>
    `;

                pakagesList.appendChild(subscriptionBox);
            });
        })
        .catch((error) => {
            console.error(
                "There was a problem with the fetch operation:",
                error.message
            );
        });
});
