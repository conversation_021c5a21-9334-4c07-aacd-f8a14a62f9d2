<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class bookingPayments extends Model
{
    use HasFactory;

    protected $fillable = [

        'user_id',
        'custom_trip_id',
        'direct_slip',
        'payment_type',
        'price',
        'trip_type',
        'booking_id',
        'charges',
        'receive_payment',

    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function bookedDestination()
    {
        return $this->belongsTo(DestinationBooking::class, 'booking_id', 'id');
    }
    public function bookedBy()
    {
        return $this->belongsTo(User::class, 'user_id'); // Assuming 'user_id' is the foreign key for the user who booked
    }

}
