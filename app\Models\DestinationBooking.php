<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DestinationBooking extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'destination_id',
        'tickets',
        'single_price',
        'phone',
        'date',
        'adultes',
        'children',
        'message',
        'extras',
        'status',
        'terms',
        'seperate_room',
        'type',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function destination()
    {
        return $this->belongsTo(Destinations::class);
    }

    public function bookedBy()
    {
        return $this->belongsTo(User::class, 'user_id'); // Assuming 'user_id' is the foreign key for the user who booked
    }

}
