<?php $login_user = auth()->user();?>

<div class="pofile_page_sidebar">
    <div class="pofile_sidebar_header">
        <span>Hello!</span>
        <h5><?php echo $login_user['name']; ?></h5>
    </div>
    <div class="res_sidebar_toggle_btn">
        <span>Select Option</span><i class="fas fa-filter"></i>
    </div>
    <div class="pofile_sidebar_tab_section">



        <div class="pofile_sidebar_tab_list">
            <div class="res_sidebar_toggle_btn res_cross_btn">
                <i class="fas fa-times"></i>
            </div>
            <a class="single_pofile_tab  <?php echo ($_SERVER['REQUEST_URI'] == '/user/profile') ? "active" : ''; ?>"
                href="{{route('website.profile.profile')}}">Personal Info</a>

            <a class="single_pofile_tab <?php echo ($_SERVER['REQUEST_URI'] == '/user/profile/bookingdetails') ? "active" : ''; ?>"
                href="{{route('website.profile.bookingDetail')}}">Booking Details</a>

            <a class="single_pofile_tab <?php echo ($_SERVER['REQUEST_URI'] == '/user/profile/payment/history') ? "active" : ''; ?>"
                href="{{route('website.profile.paymentHistory')}}">Payment History</a>

            <a class="single_pofile_tab <?php echo ($_SERVER['REQUEST_URI'] == '/user/profile/change-password') ? "active" : ''; ?>"
                href="{{route('website.profile.changePassword')}}">Change password</a>
            <a class="single_pofile_tab" id="profile_logout_btn">Logout</a>
        </div>
    </div>
</div>