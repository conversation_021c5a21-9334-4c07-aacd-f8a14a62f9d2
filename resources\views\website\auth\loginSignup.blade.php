@extends('website.include.layout')
@section('title', 'Login / Register')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')



@section('content')

<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('/website/images/subscribeBanner.webp');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">

                <h2>Log In or Sign Up for Your Travel Africa Account</h2>
                <p>Whether you're a tour operator or a tourist, log in or sign up for a Travel Africa account to access
                    exclusive travel deals, manage bookings, and join our vibrant travel community. Start your journey
                    with us today.</p>
            </div>
        </div>
    </div>
</section>

<section class="login_section display-block float-left w-100">

    <div class="custom_container">
        <div class="login_section_parent">

            <!-- banner slider -->
            <div id="bannerSlider" class="carousel login_slider slide w-100" data-bs-ride="carousel">
                <div class="carousel-indicators banner_dots">
                    <button type="button" data-bs-target="#bannerSlider" data-bs-slide-to="0" class="active"
                        aria-current="true" aria-label="Slide 1"></button>
                    <!-- <button type="button" data-bs-target="#bannerSlider" data-bs-slide-to="1"
                        aria-label="Slide 2"></button>
                    <button type="button" data-bs-target="#bannerSlider" data-bs-slide-to="2"
                        aria-label="Slide 3"></button> -->
                </div>
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <img src="{{asset('website/images/travelafrica.webp')}}" width="477" height="636" decoding="async" loading="lazy" class="d-block w-100 h-100" alt="slide1">
                    </div>
                    <!-- <div class="carousel-item">
                        <img src="{{asset('website/images/login2.png')}}" class="d-block w-100" alt="slide2">
                    </div>
                    <div class="carousel-item">
                        <img src="{{asset('website/images/login3.png')}}" class="d-block w-100" alt="slide3">
                    </div> -->
                </div>
            </div>

            <div class="login_section_box d-grid w-100 align-items-center">
                <div class="login_tabs_main  d-flex w-100 align-items-center justify-content-center w-100 flex-column">
                    <div class="login_section_box_heading d-grid w-100">
                        <h3>Register OR Login to your account</h3>
                        <p>If you already register then login to your account .
                            Other wise click on signup and submit the form by filling information to
                            register your self.
                        </p>
                    </div>


                    <div class="nav login_tabs" role="tablist">
                        <a class="nav-link active" id="login-tab" data-bs-toggle="tab" data-bs-target="#loginTab"
                            type="button" role="tab" aria-controls="loginTab" aria-selected="true">Login</a>
                        <a class="nav-link" id="signup-tab" data-bs-toggle="tab" data-bs-target="#signupTab"
                            type="button" role="tab" aria-controls="signupTab" aria-selected="false">Signup</a>
                    </div>

                    <div class="tab-content login_tab_content">
                        <div class="tab-pane fade show active" id="loginTab" role="tabpanel"
                            aria-labelledby="login-tab">

                            <div class="login_section_box_heading">
                                <h4>Login to your account</h4>

                            </div>

                            <form method="POST" id="login_form" enctype="multipart/form-data">
                                @csrf
                                <div class=" login_form_content_box">

                                    <div class="login_form_field_box">
                                        <div class="login_single_field_box">
                                            <span>Email</span>
                                            <input type="email" placeholder="Enter your email" id="login_email"
                                                name="login_email">
                                            <i class="fas fa-envelope"></i>
                                            <label class="error" generated="true" for="login_email"></label>
                                        </div>
                                    </div>
                                    <div class="login_form_field_box">
                                        <div class="login_single_field_box">
                                            <span>Password</span>
                                            <input class="password" type="password" placeholder="Enter password"
                                                id="login_password" name="login_password">
                                            <i class="far fa-eye-slash"></i>
                                            <label class="error" generated="true" for="login_password"></label>
                                        </div>
                                    </div>
                                    <div class="login_form_signin_btn">
                                        <button>Login</button>
                                    </div>
                                </div>
                            </form>


                        </div>
                        <div class="tab-pane fade" id="signupTab" role="tabpanel" aria-labelledby="signup-tab">

                            <div class="login_section_box_heading">
                                <h4>Create your account</h4>

                            </div>


                            <form method="POST" id="signup_form" enctype="multipart/form-data">
                                @csrf
                                <div class="login_form_content_box">
                                    <div class="login_form_field_box_parent">
                                        <div class="login_form_field_box">
                                            <div class="login_single_field_box">
                                                <span>Name</span>
                                                <input id="name" type="text" placeholder="Enter your name" name="name">
                                                <label class="error" generated="true" for="name"></label>
                                            </div>

                                        </div>
                                        <div class="login_form_field_box">
                                            <div class="login_single_field_box">
                                                <span>Email</span>
                                                <input type="email" placeholder="Enter your email" id="email"
                                                    name="email">
                                                <i class="fas fa-envelope"></i>
                                                <label class="error" generated="true" for="email"></label>
                                            </div>

                                        </div>

                                    </div>

                                    <div for="profile_img" class="drag_and_drop_box" data-for="profile_img">
                                        <small>select your profile image</small>
                                        <input id="profile_img" type="file" accept="image/*" name="profile_img"
                                            class="dropify" data-max-file-size="2M"
                                            data-allowed-file-extensions="jpg jpeg png webp" />
                                        <label class="error" generated="true" for="profile_img"></label>
                                    </div>

                                    <div class="login_form_field_box">
                                        <div class="login_single_field_box">
                                            <span>Password</span>
                                            <input id="password" class="password" type="password"
                                                placeholder="Enter password" name="password" autocomplete>
                                            <i class="far fa-eye-slash"></i>
                                            <label class="error" generated="true" for="password"></label>
                                        </div>

                                    </div>
                                    <div class="login_form_field_box">
                                        <div class="login_single_field_box">
                                            <span>Confirm Password</span>
                                            <input type="password" class="password" placeholder="Enter password"
                                                id="password_confirmation" name="password_confirmation" autocomplete>
                                            <i class="far fa-eye-slash"></i>
                                            <label class="error" generated="true" for="password_confirmation"></label>
                                        </div>

                                    </div>
                                    <div class="login_form_signin_btn">
                                        <button>Sign up</button>
                                    </div>
                                </div>
                            </form>



                        </div>
                    </div>
                </div>
                <div class="forget_password_btn">
                    <a type="button" data-bs-toggle="modal" data-bs-target="#forgetPassword">
                        Forgot password?
                    </a>
                </div>





            </div>

        </div>
    </div>
</section>

<section class="modal fade" id="forgetPassword" tabindex="-1" aria-labelledby="forgetPasswordLabel" aria-hidden="true">
    <div class="modal-dialog forget_password_container" role="document">
        <div class="forget_password_popup d-flex w-100 flex-column align-items-center justify-content-center">

            <div
                class="forget_password_popup_des d-flex w-100 flex-column align-items-center justify-content-center text-center">
                <h5>Forgot Password</h5>
            </div>
            <form method="post" class="w-100" id="reset_password_form">
                @csrf
                <div class="login_form_content_box">

                    <div class="login_form_field_box">
                        <div class="login_single_field_box">
                            <span>Email</span>
                            <input type="email" placeholder="Enter your email" name="email" required>
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>

                    <div class="forget_password_popup_btn">
                        <input type="submit" value="Submit">
                        <a data-bs-dismiss="modal"
                            class="d-flex w-100 align-items-center justify-content-center text-center">close</a>
                    </div>
                </div>
            </form>


        </div>

    </div>

</section>

@endsection
