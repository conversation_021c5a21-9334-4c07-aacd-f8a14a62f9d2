
/* member section css start */


.member_section {
  float: left;
  display: inline-block;
  width: 100%;
  padding:48px 10px 48px 10px;
  border-bottom: 1px solid #000000;
  border-top: 1px solid #000000;
 }

.member_main {
float: left;
width: 100%;
height: 100%;
display: flex;
align-items: center;
text-align: center;
}
.custom_sliding_box {
float:left;
width:25%;
padding:0px 20px;
display: flex;
flex-direction: column;
justify-content: center;
}

.custom_sliding_box small {
padding-bottom: 16px;
display: flex;
align-items: center;
justify-content: center;
}
.custom_sliding_box small i {
width: 40px;
height: 40px;
display: flex;
align-items: center;
justify-content: center;
border-radius: 50%;
background: var(--primary-color);
color: #fff;
font-size: 20px;
}
.custom_sliding_box  h5 {
font-style: normal;
font-weight: 400;
font-size: 48px;
line-height: 50px;
text-align: center;
text-transform: uppercase;
color: #000000;
padding-bottom: 16px;
margin: 0px;
}
.custom_sliding_box p {
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: #000000;
padding-bottom: 24px;
margin: 0px;
}
.member_slider_arrow_box_main{
display: flex;
justify-content: center;
}
.member_slider_arrow_box {
display: flex;

}
.member_slider_arrow_box  a.member_pre_arrow.slick-arrow {
justify-content: center;
background: #EAE5DB;
display: flex;
cursor: pointer;
}
.member_slider_arrow_box  a.member_pre_arrow.slick-arrow:first-child{
border-top-left-radius: 56px;
border-bottom-left-radius: 56px;
padding: 12px 8px 12px 16px;
}
.member_slider_arrow_box  a.member_pre_arrow.slick-arrow:last-child{
border-top-right-radius: 56px;
border-bottom-right-radius: 56px;
padding: 12px 16px 12px 8px;
}
.member_slider_arrow_box a.member_pre_arrow.slick-arrow i {
font-size: 14px;
}
.member_list {
  float: left;
  width: 75%;
  padding: 0px 20px;
  -webkit-mask-image: linear-gradient(to left, transparent, black 20%, black 100%, transparent 100%);
}
.member_list ul {
  margin: 0px -8px;
  padding: 0px;
}
.member_list ul li {
  list-style: none;
  width: 33.333%;
  padding: 0px 8px 0px 8px;
  float: left;
    margin: 0px !important;
}

.member_figure_box {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

}
.member_figure_box figure {
  margin-bottom: 0px;
}
.member_figure_box figure a {
  width: 100%;
  display: block;
      height: 200px;
    background: #FCFAF5;
    padding: 10px 15px;
}
.member_figure_box figure a img {
width:100%;
height:100%;
object-fit: contain;
}
.member_figure_tags {
  display: flex;
  width: 100%;
  height: 100%;
padding: 15px 8px 10px 8px;
flex-direction: column;
    text-align: center;
    align-items: center;
}
.member_figure_tags h6{
 font-weight: 400;
line-height: 23px;
letter-spacing: 0.01em;
color: #000000E5;
font-size: 16px;
padding-bottom: 7px;

}
.member_figure_tags strong{
font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 24px;
    color: #000000;
    margin-bottom: 8px;
    text-overflow: ellipsis;
    word-wrap: break-word;
    overflow: hidden;
    white-space: nowrap;
}
.member_figure_tags strong a{
   color: #000000; 
    
}
.member_figure_tags strong a:hover{
   color: #000000; 
}
.member_figure_tags p{
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 21px;
color: #000000;
margin-bottom: 24px;
height: 44px;
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
text-overflow: ellipsis;
word-wrap: break-word;
overflow: hidden;
}
.member_figure_tags_btn{
display: flex;
}
.member_figure_tags_btn a{
font-style: normal;
font-weight: 500;
font-size: 16px;
line-height: 20px;
display: flex;
align-items: center;
text-transform: capitalize;
color: var(--primary-color);
cursor: pointer;
}
.member_figure_tags_btn a:hover{

    color: #FCC917 !important;
    transition: 0.2s ease-in-out;
    
}
/* members page extra css start*/

.member_page_main .custom_sliding_box{
width: 100%;
text-align: center
}
.member_page_list{
  display: flex;
  margin: 0 -8px;
  flex-wrap: wrap;
}
.member_page_list li{
  flex: 0 0 33.33%;
  max-width: 33.33%;
display: flex;
padding: 8px 8px;
}
@media (max-width: 1024px){
  .member_page_main .custom_sliding_box {
    margin-bottom: 0px;
}
}
@media (max-width: 768px){
.member_page_list li {
  flex: 0 0 50%;
  max-width: 50%;
}
}
/* members page extra css end*/

@media (max-width: 1024px) {
.member_main {
display: flex;
flex-direction: column;
justify-content: center;
width: 100%;
margin: 0px 0px;

}
.custom_sliding_box {
margin-bottom: 30px;
padding: 0px;
width: 100%;
}
.member_list {
width:100%;
padding:0px;
}

}
@media (max-width: 768px) {
.member_figure_tags {
text-align: center;
}
.member_figure_tags_btn {
justify-content: center;
}
}
@media (max-width: 540px) {
.custom_sliding_box h5 {
font-size: 24px;
line-height: 31px;
padding-bottom: 8px;
}
.custom_sliding_box p {
padding-bottom: 16px;
}
.member_section {
padding: 30px 10px 30px 10px;
}


}
@media (max-width: 540px) {
.member_figure_tags strong {
font-size: 16px;
line-height: 20px;
margin-bottom: 6px;
}
.member_figure_tags p {
font-size: 12px;
line-height: 18px;
height: 36px;
margin-bottom: 15px;
}
}


/* member section css end */


/* event section css start */


.event_section {
  float: left;
  display: block;
  width: 100%;
  padding:60px 15px 45px 15px;
  border-bottom: 1px solid #000000;
 }

.event_main {
width: 100%;
display: flex;
flex-direction: column;
}
.event_sliding_box {
width:100%;
display: flex;
flex-direction: column;
justify-content: center;
padding-bottom: 30px;
}

.event_sliding_box small {
padding-bottom: 18px;
display: flex;
align-items: center;
justify-content: center;
}
.event_sliding_box small i {
width: 40px;
height: 40px;
display: flex;
align-items: center;
justify-content: center;
border-radius: 50%;
background: var(--primary-color);
color: #fff;
font-size: 20px;
}
.event_sliding_box h5{
font-style: normal;
font-weight: 400;
font-size: 48px;
line-height: 50px;
text-align: center;
text-transform: uppercase;
color: #000000;
padding-bottom: 35px;
margin: 0px;
}
.event_tabs_parent{
  display: flex;
  width: 100%;
  flex-direction: column;
}

.event_tabs {
  display: flex;
  width: 100%;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: center;
  overflow-x: auto;
  overflow-y: hidden !important;
  border: unset !important;
}
.event_tabs li.nav-item {
  list-style: none;
  margin-right: 15px;
  margin-bottom: 0px;
}
.event_tabs li.nav-item:last-child{
  margin-right: 0px;
}
.event_tabs li.nav-item a{
padding: 12px 20px 12px 20px;
display: inline-block;
white-space: nowrap;
font-size: 14px;
font-weight: 400;
line-height: 19px;
letter-spacing: 0em;
border: 1px solid #000;
border-radius: 50px;
text-decoration: none;
color: #000;
text-transform: uppercase;
margin: 0px !important;
  transition: 0.2s ease-in-out;
    
}

.event_tabs li.nav-item a.active{
  background-color: var(--primary-color);
  border-color: var(--primary-color);
color: #fff;
}
.event_tabs li.nav-item a:hover{
    background-color: #FCC917 !important;
    transition: 0.2s ease-in-out;
    color: #000 !important;
  border-color: #FCC917 !important;
    
}
.event_list {
  margin: 0px -15px;
  padding: 0px;
display: flex;
align-items: center;
justify-content: center;
flex-wrap: wrap;
}
.event_list li {
  flex: 0 0 25%;
  list-style: none;
  width: 25%;
  padding: 0px 15px 0px 15px;
  float: left;
    margin:15px 0px !important;
    border-right: 0.75px dashed #000000;
}
.event_list li:last-child{
  border: unset;
}

.event_figure_box {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

}
.event_figure_box figure {
  margin-bottom: 0px;
}
.event_figure_box figure a {
  width: 100%;
  height: 100%;
  display: block;
}
.event_figure_box figure a img {
width:100%;
height:100%;
object-fit: cover;
}
.event_figure_tags {
  display: flex;
  width: 100%;
  height: 100%;
padding: 15px 8px 10px 8px;
flex-direction: column;
text-align: left;
}
.event_figure_tags small{
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: #000000E5;
  font-size: 14px;
  padding-bottom: 7px;

}
.event_figure_tags strong{
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #000000;
  margin-bottom: 8px;
  height: 44px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  word-wrap: break-word;
  overflow: hidden;
}
.event_figure_tags strong a{
    
      color: #000000 !important;
}
.event_figure_tags strong a:hover{
    
      color: #000000 !important;
}
.event_figure_tags h6{
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 21px;
color: #000000;
margin-bottom: 25px;
white-space: nowrap;
text-overflow: ellipsis;
word-wrap: break-word;
overflow: hidden;

}
.event_figure_tags_btn{
display: flex;
}
.event_figure_tags_btn a{
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 20px;
display: flex;
align-items: center;
text-transform: capitalize;
background-color: var(--primary-color) ;
color: #fff !important;
cursor: pointer;
display: flex;
align-items: center;
justify-content: center;
width: 160px;
height: 42px;
border-radius: 50px;
transition: 0.2s ease-in-out;
}
.event_figure_tags_btn a:hover{
    
        background-color: #FCC917 !important;
        color: #000 !important;
    transition: 0.2s ease-in-out;
}
@media (max-width: 1024px) {
  .event_section {
    padding: 40px 15px 25px 15px;
    }
    .event_list {
      padding: 0px 10px;
   
    }
    .event_list li {
      flex: 0 0 33.33%;
      width: 33.33%;
    }
}
@media (max-width: 768px) {
  .event_tabs{
    justify-content: flex-start;
  }
  .event_list li {
    flex: 0 0 50%;
    width: 50%;
  }
  .event_list {
    padding: 0px 0px;
 
  }
  .event_list li:nth-child(even) {
    border: unset;
  }
  
.event_figure_tags {
text-align: center;
}
.event_figure_tags_btn {
justify-content: center;
}
}

@media (max-width: 540px) {
  .event_sliding_box h5 {
    font-size: 24px;
    line-height: 31px;
    }
    
    .event_section {
    padding: 30px 15px 30px 15px;
    }
    
.event_figure_tags strong {
font-size: 16px;
line-height: 20px;
margin-bottom: 6px;
height: 41px;
}
.event_figure_tags p {
font-size: 12px;
line-height: 18px;
height: 36px;
margin-bottom: 15px;
}
}
@media (max-width: 430px) {
  .event_list li {
    flex: 0 0 100%;
    width: 100%;
    border-right: unset;
  border-bottom: 0.75px dashed #000000;
  padding-bottom: 10px;
}
.event_list li:last-child{
  border: unset;
  padding-bottom: 0px;
}

}

/* event section css end */


/*  home page mision and vision section css start  */

.home_section_2_main {
  background: #FCFAF5;
  display: block;
  float: left;
  width: 100%;
  padding: 64px 15px;

}

.home_section_2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px 55px;
  width: 100%;
  height: 100%;

}

.home_section_2_figure_box {
  width: 100%;
  height: 100%;
}

.home_section_2_figure_box figure {
  margin: 0px;
  width: 100%;
  height: 100%;
  object-fit: cover;
}



.home_section_2_figure_box figure img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

.home_section_2_body {
  display: grid;
  gap: 20px 20px;
  width: 100%;
}
.home_section_2_content{
  display: flex;
  flex-direction: column;
  width: 100%;
}
.home_section_2_content h3{

font-size: 36px;
font-weight: 500;
line-height: 48px;
letter-spacing: 0em;
color: #000;
padding-bottom: 11px;
}
.home_section_2_content small{
font-size: 14px;
font-style: italic;
font-weight: 500;
line-height: 19px;
letter-spacing: 0em;
text-align: justify;
padding-bottom: 15px;
color: var(--primary-color);
}
.home_section_2_content p{
  font-size: 16px;
font-style: italic;
font-weight: 400;
line-height: 25px;
letter-spacing: 0em;
text-align: justify;
color: #000000E5;
padding-bottom: 10px;
}
.home_section_2_content ul{
  font-size: 16px;
font-weight: 400;
line-height: 25px;
letter-spacing: 0em;
color: #000000E5;
display: grid;
gap: 10px;
margin-left: 15px;
padding-bottom: 10px;
}



@media(max-width:1024px) {
  .home_section_2{
    grid-template-columns: 1fr;
  }
  .home_section_2_figure_box figure img {
height: 420px;
}
.home_section_2_main {
padding: 40px 15px;
}
}

@media(max-width:768px) {
  .home_section_2_main {
padding: 30px 15px;
}
.home_section_2_content {
align-items: center;
text-align: center;
}
.home_section_2_content p{
text-align: center;
}
}

@media(max-width:540px) {
  .home_section_2_figure_box figure img {
height: 300px;
}


}

/*  home page mision and vision section css end  */



/* home page pakages section css start */

.pakages_list_section {
  display:block;
  float: left;
  width:100%;
 padding:64px 15px 64px 15px;
 background: #FCFAF5;
}
.pakages_heading {
display: flex;
width: 100%;
align-items: center;
justify-content: center;
padding-bottom: 40px;
text-align: center;
flex-direction: column;
position: relative;
max-width: 450px;
margin: 0 auto;
}
.pakages_heading h3 {
font-size: 36px;
font-weight: 700;
line-height: 34px;
letter-spacing: 0em;
color: #000000;
position: relative;
padding-bottom: 30px;
padding-left: 8px;
padding-right: 8px;
margin: 0px;
}
.pakages_heading p {
font-size: 16px;
font-weight: 500;
line-height: 19px;
letter-spacing: 0em;
text-align: center;
color: #515151;
}
.pakages_list_box{
display: grid;
grid-template-columns: 1fr 1fr 1fr 1fr;
gap: 30px 20px;
width: 100%;

}
.single_pakage_box_data{
background: #FFFFFF;
box-shadow: 0px 4px 34px rgba(0, 0, 0, 0.08);
border-radius: 16px;
width:100%;
   height: 100%;
}
.single_pakage_box_data_header{
   margin-bottom: 33px;
   display: flex;
   width: 100%;
   flex-direction: column;
   align-items: center;
   justify-content: center;
   text-align: center;
   padding: 24px 10px;
   border-radius: 5px 5px 0px 0px;
}
.green_box{
 background: var(--primary-color);
}
.blue_box{
 background: var(--primary-color);
}
.red_box{
 background: var(--primary-color);
}
.single_pakage_box_data_header h3{
  font-style: normal;
  font-weight: 800;
  font-size: 32px;
  line-height: 34px;
  color: #FFFFFF;
  padding-bottom: 15px;
   
}
.single_pakage_box_data_header span{
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 18px;
  color: #FFFFFF;
  text-transform: capitalize;
}
.single_pakage_box_data_content{
display: flex;
flex-direction: column;
width:100%;
padding:0px 0px 32px 0px;
 height: 100%;
     transition: 0.2s ease-in-out;
}
.single_pakage_features_list {
 padding: 0px 24px;
}

.single_pakage_box_data_get_start_btn{
   display: flex;
   flex-direction: column;
   justify-content: center;
   width: 100%;
   align-items: center;
margin-top: auto;
 padding: 0px 24px;
}
.single_pakage_box_data_get_start_btn a{
   width: 100%;
height: 48px;
border-radius: 5px;
display: flex;
align-items: center;
text-align: center;
   justify-content: center;
   font-style: normal;
font-weight: 700;
font-size: 14px;
line-height: 18px;
color: #FFFFFF !important;
}
.single_pakage_box_data_get_start_btn a:hover{
color: #FFFFFF;
}

.single_pakage_features_list ul {
   display: grid;
   padding-bottom: 37px;
   gap: 17px;
}

.single_pakage_features_list ul li {
   display: flex;
   padding-bottom: 17px;
   list-style: none;
   width: 100%;
   justify-content: space-between;
align-items: center;
border-bottom: 1px solid rgba(0, 0, 0, 0.09);
}
.single_pakage_features_list ul li:last-child{
padding-bottom:0px;
border-bottom: unset;
}
.single_pakage_features_list ul li i{
padding-left:7px;
color:#5BA249;
font-size: 13px;

}
.single_pakage_features_list ul li small{
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 16px;
color: #000000;
}
.single_pakage_box_data_content:hover .single_pakage_box_data_header{
        transition: 0.2s ease-in-out;
    background: #FCC917 !important;

}
.single_pakage_box_data_content:hover .single_pakage_box_data_header *{
        transition: 0.2s ease-in-out;
color: #000 !important;
}
.single_pakage_box_data_content:hover .single_pakage_box_data_get_start_btn a{
      background: #FCC917 !important; 
color: #000 !important;
    transition: 0.2s ease-in-out;
}

@media(max-width:1024px){
.pakages_list_box{
grid-template-columns: 1fr 1fr;
gap: 20px 20px;
grid-template-areas: ". .";
}
.pakages_list_section {
   padding: 40px 15px 40px 15px;
}
}
@media(max-width:540px){

.pakages_list_box{
grid-template-columns: 1fr;
gap: 30px 20px;
grid-template-areas: ".";
}
.pakages_list_section {
   padding: 30px 15px 30px 15px;
}
}
/* home page pakages section css start */




/* brand section style start */
.product_brand_section {
  display: inline-block;
  width: 100%;
  padding: 80px 15px 80px 15px;
}

.product_brand_heading {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-bottom: 50px;
  text-align: center;
  flex-direction: column;
  position: relative;
  max-width: 450px;
  margin: 0 auto;
}
.brand_asset_1 {
  position: absolute;
  top: -170px;
  right: 0px;
  z-index: -1;
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.brand_asset_1 img {
  max-width: 300px;
}
.brand_asset_2 {
  position: absolute;
  top: -202px;
  right: 0px;
  z-index: -1;
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.brand_asset_2 img {
  max-width: 400px;
}

.brand_asset_3 {
  position: absolute;
  top: -107px;
  right: 0px;
  z-index: -1;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand_asset_3 img {
  max-width: 400px;
}

.product_brand_heading h3 {
  font-size: 36px;
  font-weight: 700;
  line-height: 44px;
  letter-spacing: 0em;
  color: #000000;
  position: relative;
  padding-bottom: 30px;
  padding-left: 8px;
  padding-right: 8px;
  margin: 0px;
}

.product_brand_heading h3 img {
  position: absolute;
  bottom: 18px;
  left: 0px;
  display: flex;
  width: 100%;
}

.product_brand_heading p {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: center;
  color: #515151;
}

.brand_slider_arrow_box {
  display: flex;
  justify-content: space-between;
  height: 100%;
  align-items: center;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
}

.brand_slider_arrow_box a.slider_arrow.slick-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
  cursor: pointer;
}

.brand_slider_arrow_box a.slider_arrow.slick-arrow i {
  color: var(--primary-color);
  font-size: 22px;
}

.product_brand_list {
  float: left;
  width: 100%;
  padding: 0px 80px;
  position: relative;
}

.product_brand_list ul {
  margin: 0px -15px;
  padding: 0px;
}

.product_brand_list ul li {
  list-style: none;
  width: 33.333%;
  padding: 0px 15px 0px 15px;
  float: left;
  margin: 0px !important;
}

.product_brand_figure_box {
  float: left;
  text-align: center;
  display: flex;
  justify-content: center;
  width: 100%;
}

.product_brand_figure_box figure {
  margin: 0px !important;
}

.product_brand_figure_box figure img {
    padding: 10px 10px;
    width: 100%;
    height: 150px;
    object-fit: contain;
    background: #FCFAF5;
}

.product_brand_list .slick-track {
  display: flex;
  align-items: center;
}

@media (max-width: 1024px) {
  .product_brand_list {
    padding: 0px 40px;
  }
  .product_brand_section {
    padding: 60px 15px 60px 15px;
  }
}
@media (max-width: 768px) {
  .product_brand_heading {
    padding-bottom: 30px;
  }
 
}
@media (max-width: 540px) {
  .product_brand_section {
    padding: 40px 15px 40px 15px;
  }
  .brand_asset_1 img {
    width: 100%;
  }

  .brand_asset_2 img {
    width: 100%;
  }

  .brand_asset_3 img {
    width: 100%;
  }
}

/* brand section style end */



/* reviews section css start */


.reviews_section {
  float: left;
  display: inline-block;
  width: 100%;
  padding:80px 10px 80px 10px;
  background: #FCFAF5;
 }

.reviews_main {
float: left;
width: 100%;
height: 100%;
display: flex;
align-items: center;
text-align: center;
}
.review_sliding_box {
float:left;
width:40%;
padding:0px 20px;
display: flex;
flex-direction: column;
}
.add_review_btn {
    display: flex;
}
.add_review_btn a {
    width: 185px;
    height: 50px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 500;
    line-height: 13px;
    letter-spacing: 0em;
    color: #fff !important;
    background-color: var(--primary-color);
}
.dummy_img_main{
    display: flex;
    width: 100%;
    align-items: center;
justify-content: space-between;
}
.dummy_img_two{
  display: flex;
    width: 100%;
}
.dummy_img_two img {
  width: 100%;
}

.review_sliding_box  h5 {
  font-weight: 500;
    line-height: 56px;
    text-transform: capitalize;
    color: #000000;
    font-size: 40px;
    margin: 0px;
    text-align: left;
    padding-bottom: 24px;
}

.reviews_slider_arrow_box_main{
display: flex;
justify-content: center;
margin-top: 18px;
}
.reviews_slider_arrow_box {
display: flex;

}
.reviews_slider_arrow_box  a.reviews_pre_arrow.slick-arrow {
justify-content: center;
background: #EAE5DB;
display: flex;
cursor: pointer;
}
.reviews_slider_arrow_box  a.reviews_pre_arrow.slick-arrow:first-child{
border-top-left-radius: 56px;
border-bottom-left-radius: 56px;
padding: 12px 8px 12px 16px;
}
.reviews_slider_arrow_box  a.reviews_pre_arrow.slick-arrow:last-child{
border-top-right-radius: 56px;
border-bottom-right-radius: 56px;
padding: 12px 16px 12px 8px;
}
.reviews_slider_arrow_box a.reviews_pre_arrow.slick-arrow i {
font-size: 14px;
}
.reviews_list {
  float: left;
  width: 60%;
  padding: 0px 20px;
}
.reviews_list ul {
  margin: 0px -8px;
  padding: 0px;
}
.reviews_list ul li {
  list-style: none;
  width: 33.333%;
  padding: 0px 8px 0px 8px;
  float: left;
    margin: 0px !important;
}

.reviews_figure_tags {
  display: flex;
  width: 100%;
  height: 100%;
flex-direction: column;
text-align: left;
}

.reviews_figure_tags strong{
  font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: #000000;
    margin-bottom: 18px;
    text-overflow: ellipsis;
    word-wrap: break-word;
    overflow: hidden;
}
.reviews_figure_tags p{
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 21px;
color: #000000;
margin-bottom: 35px;

}
.reviews_figure_tags small{
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 21px;
color: #000000;

}

.slider_progress {
    display: block;
    width: 40%;
    height: 12px;
    border-radius: 25px;
    overflow: hidden;
    background-color: #EAE5DB;
    background-image: linear-gradient(to right, var(--primary-color), #FCFAF5);
    background-repeat: no-repeat;
    background-size: 0 100%;
    transition: background-size .4s ease-in-out;
margin-top: 30px;
}
@media (max-width: 1024px) {
  .reviews_section {
    padding: 40px 10px 40px 10px;
}
.reviews_main {
display: flex;
flex-direction: column;
justify-content: center;
width: 100%;
margin: 0px 0px;

}
.review_sliding_box {
margin-bottom: 30px;
padding: 0px;
width: 100%;
}
.reviews_list {
width:100%;
padding:0px;
}

}
@media (max-width: 768px) {
.reviews_figure_tags {
text-align: center;
}
.reviews_figure_tags_btn {
justify-content: center;
}
.slider_progress {
    width: 100%;
    
}
}
@media (max-width: 540px) {
.review_sliding_box h5 {
font-size: 24px;
line-height: 31px;
padding-bottom: 8px;
}

.reviews_section {
padding: 30px 10px 30px 10px;
}


}
@media (max-width: 540px) {
.reviews_figure_tags strong {
font-size: 16px;
line-height: 20px;
margin-bottom: 15px;
}
.reviews_figure_tags p {
font-size: 12px;
line-height: 18px;

margin-bottom: 15px;
}
}


/* reviews section css end */


/* gallery section style start */

.gallery_section {
  display: inline-block;
  width: 100%;
  padding: 80px 15px 80px 15px;
}

.gallery_heading {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-bottom: 50px;
  text-align: center;
  flex-direction: column;
  position: relative;
  max-width: 450px;
  margin: 0 auto;
}
.gallery_asset_1 {
  position: absolute;
  top: -170px;
  right: 0px;
  z-index: -1;
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.gallery_asset_1 img {
  max-width: 300px;
}

.gallery_heading h3 {
  font-size: 36px;
  font-weight: 700;
  line-height: 44px;
  letter-spacing: 0em;
  color: #000000;
  position: relative;
  padding-bottom: 30px;
  padding-left: 8px;
  padding-right: 8px;
  margin: 0px;
}

.gallery_heading h3 img {
  position: absolute;
  bottom: 18px;
  left: 0px;
  display: flex;
  width: 100%;
}

.gallery_heading p {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: center;
  color: #515151;
}
.gallery_list{
  display: flex;
  margin: 0px -10px;
flex-wrap: wrap;
justify-content: center;
}
.gallery_list > button{
  flex: 0 0 25%;
  max-width: 25%;
  padding: 10px 10px;
    border: unset;
   
    background: unset;
}
.gallery_list button figure{
      width: 100%;
  height: 100%;
}
.gallery_list button figure img{
  width: 100%;
  height: 100%;
 object-fit: cover;
}
.gallery_popup figure {
    width: 100%;
    height: 100%;
}
.gallery_popup figure img{
    width: 100%;
    height: 100%;
     object-fit: cover;
}
@media (max-width: 1024px) {
  .gallery_section {
    padding: 40px 15px 40px 15px;
  }
  .gallery_list button{
    flex: 0 0 33.33%;
    max-width: 33.33%;
  }
}

@media (max-width: 540px) {
  .gallery_list{
    margin: 0px -6px;
  }
  .gallery_list button{
    flex: 0 0 50%;
    max-width: 50%;
    padding: 8px 6px;
  }

  .gallery_asset_1 img {
    width: 100%;
  }

  
}
/* gallery section style end */


/* jobboard page css start */


.job_board_section_1 {
  display: inline-block;
  width: 100%;
  padding: 60px 15px 40px 15px;
}

.job_board_section1_heading {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-bottom: 50px;
  text-align: center;
  flex-direction: column;
  position: relative;
  max-width: 768px;
  margin: 0 auto;
}
.job_board_asset_1 {
  position: absolute;
  top: -170px;
  right: 0px;
  z-index: -1;
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.job_board_asset_1 img {
  max-width: 300px;
}

.job_board_section1_heading h3 {
  font-size: 36px;
font-weight: 700;
line-height: 44px;
letter-spacing: 0em;
color: #000000;
position: relative;
padding-bottom: 41px;
padding-left: 8px;
padding-right: 8px;
margin: 0px;
}

.job_board_section1_heading h3 img {
  position: absolute;
  bottom: 18px;
  left: 0px;
  display: flex;
  width: 100%;
}
.job_board_section1_detail {
    display: grid;
    gap: 10px;
}
.job_board_section1_detail p {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: center;
  color: #515151;
}
@media (max-width: 540px) {

.job_board_asset_1 img {
  width: 100%;
}
.job_board_section_1 {
    padding: 40px 15px 30px 15px;
}
.job_board_section1_heading {
    padding-bottom: 25px;
  }
}


.jobs_list {
  margin: 0px -15px;
  padding: 0px;
display: flex;
align-items: center;
justify-content: center;
flex-wrap: wrap;
}
.jobs_list li {
  flex: 0 0 25%;
  list-style: none;
  width: 25%;
  padding: 0px 15px 0px 15px;
  float: left;
    margin:15px 0px !important;
    border-right: 0.75px dashed #000000;
}
.jobs_list li:last-child{
  border: unset;
}

.jobs_figure_box {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

}
.jobs_figure_box figure {
  margin-bottom: 0px;
}
.jobs_figure_box figure a {
  width: 100%;
  height: 100%;
  display: block;
}
.jobs_figure_box figure a img {
width:100%;
height:100%;
object-fit: cover;
}
.jobs_figure_tags {
  display: flex;
  width: 100%;
  height: 100%;
padding: 15px 8px 10px 8px;
flex-direction: column;
text-align: left;
}
.jobs_figure_tags small{
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: #000000E5;
  font-size: 14px;
  padding-bottom: 7px;

}
.jobs_figure_tags strong{
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #000000;
  margin-bottom: 8px;
  height: 44px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  word-wrap: break-word;
  overflow: hidden;
}
.jobs_figure_tags strong a{
        color: #000;
}
.jobs_figure_tags strong a:hover{
        color: #000;
}
.jobs_figure_tags p{
  font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 21px;
    color: #000000;
    margin-bottom: 25px;
    height: 42px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    word-wrap: break-word;
    overflow: hidden;

}
.jobs_figure_tags_btn{
display: flex;
}
.jobs_figure_tags_btn a{
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 20px;
display: flex;
align-items: center;
text-transform: capitalize;
background-color: var(--primary-color) ;
color: #fff !important;
cursor: pointer;
display: flex;
align-items: center;
justify-content: center;
width: 160px;
height: 42px;
border-radius: 50px;
}
.jobs_figure_tags_btn a:hover{
        background: #FCC917 !important;
            transition: 0.2s ease-in-out;
            color:#000 !important;
}

@media (max-width: 1024px) {
  
    .jobs_list {
      padding: 0px 10px;
   
    }
    .jobs_list li {
      flex: 0 0 33.33%;
      width: 33.33%;
    }
  
}
@media (max-width: 768px) {
  .jobs_list li {
    flex: 0 0 50%;
    width: 50%;
  }
  .jobs_list {
    padding: 0px 0px;
 
  }
  .jobs_list li:nth-child(even) {
    border: unset;
  }
  
.jobs_figure_tags {
text-align: center;
}
.jobs_figure_tags_btn {
justify-content: center;
}
}

@media (max-width: 540px) {
   
.jobs_figure_tags strong {
font-size: 16px;
line-height: 20px;
margin-bottom: 6px;
height: 41px;
}

}
@media (max-width: 430px) {
  .jobs_list li {
    flex: 0 0 100%;
    width: 100%;
    border-right: unset;
  border-bottom: 0.75px dashed #000000;
  padding-bottom: 10px;
}
.jobs_list li:last-child{
  border: unset;
  padding-bottom: 0px;
}

}


/* jobboard page css end */

/* single job page css start */


.single_job_page_parent{
  float: left;
    display: inline-block;
    width: 100%;
    padding: 40px 15px 30px 15px;
}
.single_job_page_box{
  display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px 40px;
    width: 100%;
}
.single_job_listing{
  display: grid;
    width: 100%;
    gap: 30px;

}
.single_job_header {
    display: grid;
    gap: 20px;
}
.single_job_header h3 {
  font-size: 26px;
    line-height: 37px;
    font-weight: 600;
    text-transform: capitalize !important;
    font-family: 'Work Sans', sans-serif !important;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 7px;
}
.single_job_header ul {
    display: grid;
    gap: 10px;
}
.single_job_header ul li{
  list-style: none;
    display: flex;
    flex-wrap: wrap;
}
.single_job_header ul li small{
  font-size: 18px;
    line-height: 25px;
    font-weight: 400;
    padding-left: 10px;
}

.single_job_desc{
  display: grid;
  gap: 20px;
  width: 100%;
}
.single_job_desc > div {
    display: grid;
    gap: 15px;
}
.single_job_desc p{
  font-size: 18px;
    line-height: 25px;
    font-weight: 400;
}
.single_job_desc h3 {
    font-size: 20px;
    line-height: 32px;
    font-weight: 600;
    text-transform: capitalize !important;
    font-family: 'Work Sans', sans-serif !important;
}
.single_job_desc ul {
    padding-left: 20px;
    display: grid;
    gap: 10px;
}
.single_job_desc ul li{
  font-size: 18px;
    line-height: 25px;
    font-weight: 400;
}
.single_job_organizer{
  display: flex;
  width: 100%;
      flex-direction: column;
    border-bottom: 1px solid #EAE5DB;
    padding-bottom: 15px;
    margin-bottom: 15px;
}
.single_job_organizer figure{
  display: flex;
    align-items: center;
    margin-right: 14px;
}
.single_job_organizer figure img {
width: 100px;
    height: 100px;
    display: flex;
    border-radius: 50%;
    margin-right: 15px;
    background: #f3f3f3;
    padding: 5px;
    object-fit: contain;
}
.single_job_organizer figure figcaption{
  display: flex;
  flex-direction: column;
} 
.single_job_organizer figure figcaption span {
  font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 25px;
    color: rgba(0, 0, 0, 0.6);
    padding-bottom: 4px;
}

.single_job_organizer figure figcaption small {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: rgba(0, 0, 0, 0.6);
}
.job_back_btn{
  display: flex;
  align-items: center;
}
.job_back_btn a{
  font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 25px;
    color: var(--primary-color);
    text-decoration: none;
}
.job_back_btn a{
    font-size: 18px;
    line-height: 25px;
    color: var(--primary-color);
    padding-right: 10px;
}
.single_job_aside_detail {
    display: flex;
    width: 100%;
    padding: 20px 15px 20px 15px;
    margin: 0px !important;
    background: #FCFAF5;
    flex-direction: column;
    border-radius: 4px;
}
.single_job_aside_detail h3 {
    font-size: 32px;
    line-height: 48px;
    padding-bottom: 10px;
    font-weight: 500;
}
.single_job_aside_detail strong {
    font-size: 22px;
    line-height: 35px;
    margin-bottom: 20px;
    font-weight: 400;
    border-top: 1px solid #EAE5DB;
    border-bottom: 1px solid #EAE5DB;
    padding: 10px 0px;
}
.single_job_aside_detail a {
  display: flex;
  background-color: var(--primary-color);
  color: #fff !important;
  align-items: center;
  justify-content: center;
  padding: 10px 10px;
  border-radius: 4px;
  font-size: 22px;
    line-height: 35px;
    font-weight: 500;
    transition: 0.2s ease-in-out;
}
.single_job_aside_detail a:hover{
        transition: 0.2s ease-in-out;
    background: #FCC917;
    color: #000 !important;
}

@media (max-width: 992px){
.single_job_page_box {
   grid-template-columns: 1fr;
}
.single_event_aside {
    padding-bottom: 30px;
}
.single_job_organizer figure img {
    width: 70px;
    height: 70px;
  }
  .single_job_aside_detail a {
    padding: 6px 10px;
    font-size: 18px;
}
.single_job_aside_detail h3 {
    font-size: 26px;
}
.single_job_aside_detail strong {
    font-size: 18px;
    padding: 6px 0px;
}
}

/* single job page css end */


/* member page css start */

section.about_tab_section {
  padding: 30px 15px;
    float: left;
    width: 100%;
}
.member_page_section_1 {
  display: inline-block;
  width: 100%;
  padding: 60px 15px 40px 15px;
}

.member_page_section1_heading {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding-bottom: 50px;
  text-align: center;
  flex-direction: column;
  position: relative;
  max-width: 768px;
  margin: 0 auto;
}
.member_page_asset_1 {
  position: absolute;
  top: -170px;
  right: 0px;
  z-index: -1;
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.member_page_asset_1 img {
  max-width: 300px;
}

.member_page_section1_heading h3 {
  font-size: 36px;
font-weight: 700;
line-height: 44px;
letter-spacing: 0em;
color: #000000;
position: relative;
padding-bottom: 41px;
padding-left: 8px;
padding-right: 8px;
margin: 0px;
}

.member_page_section1_heading h3 img {
  position: absolute;
  bottom: 18px;
  left: 0px;
  display: flex;
  width: 100%;
}

.member_page_section1_heading p {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0em;
  text-align: center;
  color: #515151;
}

.member_toggle_section {
    display: flex;
    width: 100%;
    flex-direction: column;
    overflow: hidden;
}
.member_toggle_section .member_toggle_box {
    padding: 0px;
    background: transparent;
    border-radius: 8px;
}
.member_single_toggle {
  background: #FCFAF5;
border: 1px solid #000!important;
margin-bottom: 20px;
}
.member_toggle_header {
    margin-bottom: 0px;
    border: unset !important;
}
.member_toggle_header button {
    padding: 20px 20px 20px 20px;
    font-size: 24px;
    font-weight: 700;
    font-family: 'Work Sans', sans-serif !important; 
    background-color: #FCFAF5 !important;
    border-radius: 8px !important;
color: #000;
box-shadow: unset;
font-weight: 500;
font-size: 20px;
}
.member_toggle_header button:focus {
border-color: unset !important;
box-shadow: unset !important;
}
.member_toggle_header .accordion-button:not(.collapsed) {
    background: var(--primary-color) !important;
    color: #fff;
    box-shadow: unset;
        border-radius: unset !important;
}
.member_toggle_header .accordion-button::after {
    background-image: url("../images/plus.webp") !important;
    transform: scale(.7) !important;
}
.member_toggle_header .accordion-button:not(.collapsed)::after {
    background-image: url("../images/minus.webp") !important;
}
.member_toggle_content{
  display: grid;
gap: 15px;
padding: 20px 15px 25px 15px;
}
.member_toggle_content p {
    color: #000;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
}
.about_member_list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    width: 100%;
    gap: 25px 30px;

        padding: 15px 0px;
}
.about_single_member {
    display: grid;
    width: 100%;
}
.about_single_member img {
width: 100%;
    margin-bottom: 15px;
    background: #EAE5DB;
    padding: 25px;
}
.about_single_member h3 {
    font-size: 18px;
    padding-bottom: 10px;
    line-height: 26px;
}
.about_single_member p {
    font-size: 14px;
    line-height: 19px;
    font-weight: normal;
}
 .member_toggle_content ul{
  display: grid;
  gap: 10px;
  padding-left: 20px;
}
 .member_toggle_content ul li{
  color: #000;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
   list-style-type: disc;
}
/* single member page css start */

.single_member_page_heading {
  display: flex;
  width: 100%;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  flex-direction: column;
  position: relative;
  max-width: 768px;

}
.member_page_asset_1 {
  position: absolute;
  top: -170px;
  right: 0px;
  z-index: -1;
  display: flex;
  width: 100%;
  justify-content: flex-end;
}

.member_page_asset_1 img {
  max-width: 300px;
}

.single_member_page_heading h3 {
  font-size: 36px;
font-weight: 700;
line-height: 44px;
letter-spacing: 0em;
color: #000000;
position: relative;
padding-bottom: 41px;

margin: 0px;
}

.single_member_page_heading h3 img {
  position: absolute;
  bottom: 18px;
  left: 0px;
  display: flex;
  width: 100%;
}

.single_member_page_heading p {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0em;
  color: #515151;
  padding-bottom: 20px;
}

.member_comapny_detail {
  display: flex;
  width: 100%;
  flex-direction: column;
 padding-top:20px;   
}
.member_comapny_detail h3 {
  font-size: 20px;
font-weight: 600;
line-height: 28px;
letter-spacing: 0em;
color: #000000;

padding-bottom: 15px;

}
.member_comapny_detail p {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0em;
  color: #515151;
  padding-bottom: 10px;
}


/* single member page css end */
@media (max-width: 1024px) {
  .member_page_section_1 {
    padding: 40px 15px 40px 15px;
  }
  .about_member_list {
    grid-template-columns: 1fr 1fr 1fr;

}
}
@media (max-width: 540px) {
  .about_member_list {
    grid-template-columns: 1fr 1fr;

}
  .member_page_asset_1 img {
    width: 100%;
  }
  .member_toggle_content {
padding: 20px 10px 25px 10px;
}
.member_toggle_header button {
padding: 20px 10px 20px 10px;
font-size: 16px;
}
.member_toggle_content p {
font-size: 14px;
line-height: 22px;
}
.member_toggle_content ul li{
font-size: 14px;
line-height: 22px;
}
.member_page_section_1 {
padding: 30px 10px 20px 10px;
}
  
}

/*  member page css end  */

/* blog section css start */


.blog_section {
  float: left;
  display: inline-block;
  width: 100%;
  padding:80px 15px 80px 15px;
background-color: #FCFAF5;
 }

.blog_main {
float: left;
width: 100%;
height: 100%;
display: flex;
align-items: center;
text-align: center;
}
.blog_sliding_box {
float:left;
width:25%;
padding:0px 20px;
display: flex;
flex-direction: column;
justify-content: center;
}

.blog_sliding_box small {
padding-bottom: 16px;
display: flex;
align-items: center;
justify-content: center;
}
.blog_sliding_box small i {
width: 40px;
height: 40px;
display: flex;
align-items: center;
justify-content: center;
border-radius: 50%;
background: var(--primary-color);
color: #fff;
font-size: 20px;
}
.blog_sliding_box  h5 {
font-style: normal;
font-weight: 400;
font-size: 48px;
line-height: 50px;
text-align: center;
text-transform: uppercase;
color: #000000;
padding-bottom: 16px;
margin: 0px;
}
.blog_sliding_box p {
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
color: #000000;
padding-bottom: 24px;
margin: 0px;
}
.blog_slider_arrow_box_main{
display: flex;
justify-content: center;
}
.blog_slider_arrow_box {
display: flex;

}
.blog_slider_arrow_box  a.blog_pre_arrow.slick-arrow {
justify-content: center;
background: #EAE5DB;
display: flex;
cursor: pointer;
}
.blog_slider_arrow_box  a.blog_pre_arrow.slick-arrow:first-child{
border-top-left-radius: 56px;
border-bottom-left-radius: 56px;
padding: 12px 8px 12px 16px;
}
.blog_slider_arrow_box  a.blog_pre_arrow.slick-arrow:last-child{
border-top-right-radius: 56px;
border-bottom-right-radius: 56px;
padding: 12px 16px 12px 8px;
}
.blog_slider_arrow_box a.blog_pre_arrow.slick-arrow i {
font-size: 14px;
}
.blog_list {
  float: left;
  width: 75%;
  padding: 0px 20px;
  -webkit-mask-image: linear-gradient(to left, transparent, black 20%, black 100%, transparent 100%);
}
.blog_list ul {
  margin: 0px -15px;
  padding: 0px;
}
.blog_list ul li {
  list-style: none;
  width: 33.333%;
  padding: 0px 15px 0px 15px;
  float: left;
  margin: 0px !important;
  border-left: 0.75px dashed #000000
}

.blog_figure_box {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

}
.blog_figure_box figure {
  margin-bottom: 0px;
}
.blog_figure_box figure a {
  width: 100%;
  height: 100%;
  display: block;
}
.blog_figure_box figure a img {
width:100%;
height:100%;
object-fit: cover;
}
.blog_figure_tags {
  display: flex;
  width: 100%;
  height: 100%;
padding: 15px 8px 0px 8px;
flex-direction: column;
text-align: left;
}
.blog_figure_tags h6{
 font-weight: 400;
line-height: 23px;
letter-spacing: 0.01em;
color: #000000E5;
font-size: 16px;
padding-bottom: 7px;

}
.blog_figure_tags strong{
font-style: normal;
font-weight: 600;
font-size: 20px;
line-height: 24px;
color: #000000;
margin-bottom: 25px;
height: 47px;
display: -webkit-box;
-webkit-line-clamp: 2;
-webkit-box-orient: vertical;
text-overflow: ellipsis;
word-wrap: break-word;
overflow: hidden;
}
.blog_figure_tags strong a{
   color: #000000; 
}
.blog_figure_tags strong a:hover{
   color: #000000; 
}
.blog_figure_tags_btn{
display: flex;
}
.blog_figure_tags_btn a{
font-style: normal;
font-weight: 500;
font-size: 16px;
line-height: 20px;
display: flex;
align-items: center;
text-transform: capitalize;
color: var(--primary-color);
cursor: pointer;
text-decoration: underline;
}
.blog_figure_tags_btn a:hover{
    color: #FCC917 !important;
    transition: 0.2s ease-in-out;
}
@media (max-width: 1024px) {
  .blog_section {
    padding: 40px 15px 40px 15px;
}
.blog_main {
display: flex;
flex-direction: column;
justify-content: center;
width: 100%;
margin: 0px 0px;

}
.blog_sliding_box {
margin-bottom: 30px;
padding: 0px;
width: 100%;
}
.blog_list {
width:100%;
padding:0px;
}

}
@media (max-width: 768px) {
.blog_figure_tags {
text-align: center;
}
.blog_figure_tags_btn {
justify-content: center;
}
}
@media (max-width: 540px) {


.blog_section {
padding: 30px 15px 30px 15px;
}


}
@media (max-width: 540px) {
.blog_figure_tags strong {
font-size: 16px;
line-height: 20px;
margin-bottom: 6px;
height: 40px;
}

}


/* blog section css end */


/* member search css start */

.member_search_section {
  display: block;
  width: 100%;
  padding: 60px 15px;
  float: left;
  background: #FCFAF5;
}
.member_search_box {
  display: grid;
  gap: 20px 20px;
  align-items: center;
  grid-template-columns: 1fr 1fr;
  grid-template-areas: ". .";
  grid-template-rows: auto;
}
.member_search_box_content {
  display: flex;
  flex-direction: column;
}
.member_search_box_content h3 {
  font-style: normal;
  font-weight: 600;
  font-size: 32px;
  line-height: 32px;
  color: #000000;
  padding-bottom: 8px;
}
.member_search_box_content p {
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 24px;
  color: #000000;
}



.member_form_fields_content {
  display: flex;
  align-items: center;
  position: relative;
}

.member_form_fields_content input {
  border: unset;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0px 160px 0px 20px;
  margin: 0px !important;
  background: #EAE5DB;
  border-radius: 40px;
  height: 56px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
  color: #000000;
}
.member_form_fields_content input:focus-visible {
  outline: none !important;
}
.member_form_fields_content input::placeholder {
  font-size: 14px;
  line-height: 16px;
  color: #000000;
  display: flex;
  align-items: center;
}
.member_form_fields_content button {
  height: 56px;
  width: 155px;
  position: absolute;
  content: "";
  top: 0px;
  right: 0px;
  border: unset !important;
  background: #000000;
  border-radius: 0px 40px 40px 0px;
}
.member_form_fields_content button i {
  display: none;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #FCFAF5;
  
}

.member_form_fields_content label.error {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #ea2227;
  margin: 0px !important;
  position: absolute;
  bottom: -30px;
  left: 10px;
}
.member_form_fields_content button span {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 12px;
  text-transform: uppercase;
  color: #FCFAF5;
}

.member_form_fields_content button:hover {
  color: #FCFAF5;
}
.member_form_fields_content button:focus {
  outline: 0;
  box-shadow: unset !important;
}

@media (max-width: 1024px) {
  .member_search_box {
    grid-template-columns: 1fr 1.5fr;
  }
  .member_search_section {
    padding: 40px 15px;
}
}
@media (max-width: 768px) {
  .member_search_box {
    grid-template-columns: 1fr;
    grid-template-areas: ".";
  }
  .member_search_box h3 {
    text-align: center;
  }
  .member_search_box_content {
    text-align: center;
  }
}
@media (max-width: 540px) {
  .member_form_fields_content button i {
    display: block;
  }
  .member_form_fields_content button span {
    display: none;
  }

  .member_form_fields_content button {
    width: 70px;
  }
  .member_form_fields_content input {
    padding: 0px 80px 0px 15px;
    min-width: 100% !important;
    min-height: 56px !important;
  }
  .member_search_box_content h3 {
    font-size: 24px;
    line-height: 28px;
  }
}

/* member search css end  */


/* inner banner css start */
.inner_banner_section {
  float: left;
display: inline-block;
  width: 100%;
  
  }

  .inner_banner_text_section{
    float: left;
    width: 100%;
    margin-bottom: 0px;
    position: relative;
    height: 100%;
    background-image: url('../images/eventbanner.png');
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    padding: 170px 10px 50px 10px
      }
      .inner_banner_text_section.login_signup_bg{
        background-image: url('../images/login.png'); 
      }
       .inner_banner_text_section.job_page_banner{
        background-image: url('../images/job_page_banner.png'); 
      }
          .inner_banner_text_section.profile_page_banner{
        background-image: url('../images/profile_page_banner.png'); 
      }
       .inner_banner_text_section.contact_page_banner{
        background-image: url('../images/contact_page_banner.png'); 
      }
         .inner_banner_text_section.blog_page_banner{
        background-image: url('../images/blog_page_banner.png'); 
      }
        .inner_banner_text_section.membership_page_banner{
        background-image: url('../images/job_page_banner.png'); 
      }
      .inner_banner_text_section.login_signup_page_banner{
        background-image: url('../images/login_signup_page_banner.png'); 
      }
      
      
      .inner_banner_container{
        max-width: 1140px;
        margin: 0 auto; 
      }
      .inner_banner_slider_text {
        display: flex;
        flex-direction: column;
        width: 100%;
        max-width: 840px;
      }
      .inner_banner_slider_text h2 {
       font-size: 46px;
    font-weight: 400;
    line-height: 60px;
    letter-spacing: 0em;
    text-align: left;
    color: #FFFFFF;
    padding-bottom: 22px;
      }
      
      .inner_banner_slider_text p{
        font-size: 20px;
        font-weight: 400;
        line-height: 28px;
        letter-spacing: 0em;
        color: #FFFFFF;
        text-align: justify;
      }
    

     
     
      @media (max-width: 1024px) {
        .inner_banner_slider_text h2 {
          font-size: 48px;
          line-height: 58px;
      
        }
        .inner_banner_slider_text p {
          font-size: 18px;
          font-weight: 400;
        }
        .inner_banner_slider_text {
          max-width: 750px;
      }
   
      }

      @media (max-width: 768px) {
        .inner_banner_text_section {
    padding: 200px 10px 40px 10px;
}
        .inner_banner_slider_text h2 {
          font-size: 36px;
          line-height: 40px;
      }
      .inner_banner_slider_text p {
        font-size: 16px;
        line-height: 26px;
    }
  
      }
      
      @media (max-width: 540px) {
        .inner_banner_slider_text h2 {
          font-size: 28px;
          line-height: 33px;
      }
      .inner_banner_slider_text p {
        font-size: 14px;
        line-height: 20px;
    }

      }


/* inner banner css start */

/* event page css start */

.events_page_parent {
  float: left;
  display: inline-block;
  width: 100%;
  padding: 60px 15px;
}

.events_page_box {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 30px 40px;
  width: 100%;
}

.events_listing_main {
  display: flex;
  flex-direction: column;
}

.event_filter {
  background: #FCFAF5;
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
  border-radius: 4px;
  align-items: center;
  padding: 5px 10px;
}

.event_filter h2 {
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 30px;
  padding-right: 10px;
}

.event_filter ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 0px !important;
}

.event_filter ul li {
  padding: 6px 10px 5px 10px;
  display: flex;
  align-items: center;
  border: 1px solid var(--primary-color);
  border-radius: 50px;
  font-size: 16px;
  font-weight: 500;
  margin: 5px 15px 5px 0px;
  line-height: 24px;
}

.event_filter ul li i {
  margin-left: 10px;
}

.events_listing {
  margin: 0px -10px;
  padding: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.event_figure_box_main {
  flex: 0 0 50%;
  list-style: none;
  width: 50%;
  padding: 10px 10px 10px 10px;

}

.single_event_filter {
  margin-bottom: 15px;
  background: transparent !important;
  border: unset !important;
}

.res_event_filters {
  display: none;
  margin-bottom: 20px;
}

.res_event_filters a {
  background-color: #EAE5DB;
  border-radius: 4px;
  padding: 14px 15px;
  width: 100%;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 500;
  display: flex;
}

.filter_close_btn {
  display: none;
  padding: 20px 15px 0px 15px;
  justify-content: flex-end;
}

.filter_close_btn i {
  background-color: var(--primary-color);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: #fff;
}

.event_filter_header button.accordion-button {
  background: transparent !important;
  border: unset !important;
  box-shadow: unset !important;
  color: #000 !important;
  font-size: 20px;
  font-weight: 500;
  font-family: 'Work Sans', sans-serif;
  text-transform: capitalize;
  margin: 0px !important;
  padding: 14px 15px !important;
}

.event_filter_header button.accordion-button:focus-visible {
  outline: unset !important;
}

.event_filter_header button.accordion-button:focus {
  outline: unset !important;
  border: unset !important;
  box-shadow: unset !important;
}

.event_filter_header {
  margin-bottom: 0px !important;
  background-color: #EAE5DB;
  border-radius: 4px;
 
}

.event_filter_header button.accordion-button:hover {
  color: #000 !important;
}

.event_filter_header button.accordion-button:hover .accordion-button::after {
  color: #000 !important;
}

.event_fields {
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: auto;
  gap: 16px 0px;
  width: 100%;
  grid-template-areas: ".";
  padding: 25px 5px 20px 5px;
}

.event_checkbox_field {
  display: flex;
  align-items: center;
  width: 100%;
}

.event_checkbox_input {
  width: 21px;
  height: 21px;
  position: relative;
  display: inline-block;
  margin: 0px;
  min-width: 21px;
}

.event_checkbox_input input {
  opacity: 0;
  width: 0;
  height: 0;
}

.event_checkbox_input .checked_input_style {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccd6eb;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  height: 100%;
  box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.12);
  border-radius: 2px;
}

.event_checkbox_input .checked_input_style i {
  color: #ccd6eb;
  font-size: 14px;
}

.event_checkbox_input input:checked+.checked_input_style {
  background: var(--primary-color);
}

.event_checkbox_input input:not(:checked)+.checked_input_style {
  background-color: #ccd6eb;
}

.event_checkbox_input input:checked+.checked_input_style i {
  color: #ffffff;
}

.event_checkbox_field p {
  font-style: normal;
  font-weight: 500;
  font-size: 15px;
  line-height: 16px;
  color: rgba(0, 0, 0, 0.7);
  padding-left: 10px;
  margin: 0px;
}

@media(max-width:1024px) {
  .events_page_box {
    grid-template-columns: 1fr 300px;
  }

  .events_page_parent {
    padding: 40px 15px;
  }
}

@media(max-width:768px) {
  .events_page_parent {
    padding: 30px 15px;
  }

  .event_filter_section {
    padding: 60px 10px 10px 10px;
  }

  .events_page_box {
    display: flex;
    flex-direction: column-reverse;
    gap: 0px;
  }

  .res_event_filters {
    display: flex;
  }

  .events_page_box aside {
    position: fixed;
    top: 0px;
    right: 0px;
    width: 320px;
    height: 100vh;
    overflow: auto;
    background: #FCFAF5;
    z-index: 99;
    transform: translatex(100%);
    transition: 0.3s ease-in-out;
  }

  .events_page_box aside.active {
    transform: translatex(0%);
    transition: 0.3s ease-in-out;
  }

  .filter_close_btn {
    display: flex;
  }

}

@media(max-width:540px) {
  .event_filter_box {
    grid-template-columns: 1fr;
  }

}
/* event page css end */




/* single event banner css start */
.single_event_section {

  display: inline-block;
    width: 100%;
    
    }
  
    .single_event_text_section{
      float: left;
      width: 100%;
      margin-bottom: 0px;
      position: relative;
      height: 100%;
      background-image: url('../images/eventbanner.png');
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      padding: 170px 10px 50px 10px
        }
         .single_event_text_section.membership_page_banner{
           background-image: url('../images/membership_page_banner.png');   
         }
      
        .single_event_container{
          max-width: 1140px;
          margin: 0 auto; 
        }
        .single_event_slider_text {
          display: flex;
          flex-direction: column;
          width: 100%;
          max-width: 840px;
        }
        .single_event_slider_text h2 {
          font-size: 38px;
      font-weight: 400;
      line-height: 45px;
      letter-spacing: 0em;
      text-align: left;
      color: #FFFFFF;
      padding-bottom: 15px;
        }
        
        .single_event_slider_text p{
          font-size: 16px;
      font-weight: 400;
      line-height: 26px;
      letter-spacing: 0em;
      color: #FFFFFF;
      text-align: justify;
      padding-bottom: 20px;
        }
      
        .event_date_time {
      display: flex;
      flex-direction: column;
  }
  .event_date_time span {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0em;
      color: #FFFFFF;
      text-align: justify;
  
      padding-bottom: 8px;
  
      display: flex;
      align-items: center;
  }
  .event_date_time span i{
    font-size: 16px;
      line-height: 24px;
      color: #FFFFFF;
      padding-right: 10px;
  }
       
       
        @media (max-width: 1024px) {
          .single_event_slider_text h2 {
            font-size: 48px;
            line-height: 58px;
        
          }
          .single_event_slider_text p {
            font-size: 18px;
            font-weight: 400;
            line-height: 32px;
          }
          .single_event_slider_text {
            max-width: 750px;
        }
     
        }
  
        @media (max-width: 768px) {
          .single_event_text_section {
      padding: 190px 10px 20px 10px;
  }
          .single_event_slider_text h2 {
            font-size: 36px;
            line-height: 40px;
        }
        .single_event_slider_text p {
          font-size: 16px;
          line-height: 28px;
      }
    
        }
        
        @media (max-width: 540px) {
          .single_event_slider_text h2 {
            font-size: 28px;
            line-height: 33px;
        }
        .single_event_slider_text p {
          font-size: 14px;
          line-height: 25px;
      }
  
        }
  
  /* single event banner css end */
  
  
  .single_event_page_parent{
    float: left;
      display: inline-block;
      width: 100%;
      padding: 40px 15px 30px 15px;
  }
  .single_event_page_box{
    display: grid;
      grid-template-columns: 1fr 400px;
      gap: 30px 40px;
      width: 100%;
  }
  .single_event_listing{
  display: flex;
  flex-direction: column;
  width: 100%;
  }
  .single_event_listing > figure{
    width: 100%;
    height: 100%;
  margin-bottom: 15px;
      
  }
  .single_event_listing > figure img{
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  .single_event_desc{
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 30px;
  }
  .single_event_organizer{
    display: flex;
      align-items: center;
      border-bottom: 1px solid #EAE5DB;
      padding-bottom: 15px;
      margin-bottom: 15px;
  }
  .single_event_organizer figure{
    display: flex;
      align-items: center;
      margin-right: 14px;
  }
  .single_event_organizer figure img {
    width: 100px;
      height: 100px;
      display: flex;
      border-radius: 50%;
      margin-right: 15px;
      background: #f3f3f3;
      padding: 5px;
  }
  .single_event_organizer figure figcaption{
    display: flex;
    flex-direction: column;
  } 
  .single_event_organizer figure figcaption span {
    font-style: normal;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: rgba(0, 0, 0, 0.6);
      padding-bottom: 4px;
  }
  
  .single_event_organizer figure figcaption small {
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      color: rgba(0, 0, 0, 0.6);
  }
  .event_back_btn{
    display: flex;
    align-items: center;
  }
  .event_back_btn a{
    font-style: normal;
      font-weight: 500;
      font-size: 18px;
      line-height: 25px;
      color: var(--primary-color);
      text-decoration: none;
  }
  .event_back_btn a{
      font-size: 18px;
      line-height: 25px;
      color: var(--primary-color);
      padding-right: 10px;
  }
  .single_event_aside_detail {
      display: flex;
      width: 100%;
      padding: 20px 15px 20px 15px;
      margin: 0px !important;
      background: #FCFAF5;
      flex-direction: column;
      border-radius: 4px;
  }
  .single_event_aside_detail h3 {
      font-size: 32px;
      line-height: 48px;
      padding-bottom: 10px;
      font-weight: 500;
  }
  .single_event_aside_detail strong {
      font-size: 22px;
      line-height: 35px;
      margin-bottom: 20px;
      font-weight: 400;
      border-top: 1px solid #EAE5DB;
      border-bottom: 1px solid #EAE5DB;
      padding: 10px 0px;
  }
  .single_event_aside_detail a {
    display: flex;
    background-color: var(--primary-color);
    color: #fff !important;
    align-items: center;
    justify-content: center;
    padding: 10px 10px;
    border-radius: 4px;
    font-size: 22px;
      line-height: 35px;
      font-weight: 500;
  
  }
  .related_events_box{
    display: inline-block;
    float: left;
    width: 100%;
    padding: 40px 15px;
    background: #FCFAF5;
  }
  .related_events_header{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding-bottom: 32px;
  }
  .related_events_header h4{
       font-size: 32px;
      line-height: 48px;
      font-weight: 500;
      text-transform: capitalize;
  }
  .related_event_list{
    margin: 0px -10px;
      padding: 0px;
      -webkit-mask-image: linear-gradient(to left, transparent, black 20%, black 100%, transparent 100%);
  }
  .single_related_event{
      padding: 0px 10px 0px 10px;
  }
  @media (max-width: 992px){
  .single_event_page_box {
      display: flex;
      flex-direction: column-reverse;
      gap: 0px;
  }
  .single_event_aside {
      padding-bottom: 30px;
  }
  .single_event_organizer figure img {
      width: 70px;
      height: 70px;
    }
    .single_event_aside_detail a {
      padding: 6px 10px;
      font-size: 18px;
  }
  .single_event_aside_detail h3 {
      font-size: 26px;
  }
  .single_event_aside_detail strong {
      font-size: 18px;
      padding: 6px 0px;
  }
  }
  
  
  /* inner single event css end */
  
  
  /* blog page css start */
  
  
  .single_article_parent {
    display: inline-block;
    width: 100%;
    padding: 40px 10px 30px 10px;
  }
  
  .blog_listing {
    margin: 0px -10px;
    padding: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }
  .single_blog_box_main {
      flex: 0 0 33.33%;
      list-style: none;
       width: 33.33%;
       padding: 10px 10px 10px 10px;
        display: flex;
        flex-direction: column;
        max-width: 100%;
    }
  
    .single_blog_box_main:hover {
        text-decoration: none;
    }
  
    .single_blog_box_figure {
        display: flex;
        width: 100%;
    }
  
    .single_blog_box_figure img {
      width: 100%;
    height: 210px;
    object-fit: cover;
    }
  
    .single_blog_box {
        width: 100%;
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 16px 5px 0px 5px;
    }
  
    .single_blog_box_des_tags {
        display: flex;
        justify-content: flex-start;
        width: 100%;
        align-items: center;
        flex-wrap: wrap;
        padding-bottom: 8px;
    }
  
    .single_blog_box_des_tags span {
        font-style: normal;
        font-weight: 400;
        font-size: 11px;
        line-height: 12px;
        border-right: 1px solid #000000;
        color: #000000;
        padding-right: 6px;
        margin-right: 6px;
    }
  
  
    .single_blog_box_des_tags small {
        font-style: normal;
        font-weight: 400;
        font-size: 11px;
        line-height: 12px;
        color: #000000;
    }
  
    .single_blog_box h3 {
        font-style: normal;
        font-weight: 600;
        font-size: 16px;
        line-height: 22px;
        color: #000000;
        margin-bottom: 9px;
        display: -webkit-box !important;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        height: 42px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        font-family: 'Work Sans', sans-serif !important;
    }
  
    .single_blog_box p {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 14px;
        width: 100%;
        word-break: break-all;
        display: -webkit-box !important;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        height: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
  
    .single_blog_box strong {
        font-style: normal;
        font-weight: 600;
        font-size: 14px;
        line-height: 26px;
        text-decoration-line: underline;
        color: var(--primary-color);
           transition: 0.2s ease-in-out;
    }
  
  .single_blog_box_main:hover .single_blog_box strong{
      transition: 0.2s ease-in-out;
      color:#FCC917 !important;
  }
  
  
  
  .single_article_box {
    display: grid;
    grid-template-columns: 70% 30%;
    margin: 0px -10px;
  }
  
  .single_article_data {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0px 10px;
  }
  
  .single_article_sidebar {
    padding: 0px 10px;
    width: 100%;
  }
  .single_article_data {
    display: flex;
    flex-direction: column;
  }
  .single_article_bread_crums {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .single_article_bread_crums a {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    text-transform: capitalize;
    color: #5a5a5a;
    text-decoration: none;
    padding: 5px 5px;
  }
  
  .single_article_bread_crums a:hover {
    color: #000;
    text-decoration: none;
  }
  
  .single_article_bread_crums i {
    font-size: 14px;
    color: #5a5a5a;
    padding: 5px 5px;
  }
  
  .single_article_tags {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    width: 100%;
    flex-wrap: wrap;
  }
  
  .single_article_tags_heading {
    display: flex;
    background: #000;
    border-radius: 2px;
    color: #fff;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: bold;
    margin-right: 10px;
    margin-bottom: 10px;
  }
  
  .single_article_tags span {
    margin-right: 10px;
    background: #e5e5e5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    line-height: 18px;
    text-transform: capitalize;
    margin-bottom: 10px;
  }
  
  .single_article_category {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    width: 100%;
    flex-wrap: wrap;
  }
  
  .single_article_category span {
    margin-right: 10px;
    background: #e5e5e5;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    line-height: 18px;
    text-transform: capitalize;
    margin-bottom: 10px;
  }
  
  .single_article_heading {
    display: flex;
    width: 100%;
    align-items: center;
    margin-bottom: 30px;
  }
  
  
  .single_article_heading h3 {
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 29px;
    color: #000000;
    margin: 0px;
  }
  
  .single_article_short_detail {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
  }
  
  .single_article_short_detial_left {
    display: flex;
    align-items: center;
  }
  
  .single_article_short_detial_left figure {
    display: flex;
    align-items: center;
    margin-right: 14px;
    margin-bottom: 0px;
  }
  
  .single_article_short_detial_left figure img {
  width: 40px;
      height: 40px;
      display: flex;
      border-radius: 50%;
      margin-right: 15px;
      background: #f3f3f3;
      padding: 5px;
      object-fit: contain;
  }
  
  .single_article_short_detial_left figure span {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: rgba(0, 0, 0, 0.6);
  }
  
  .single_article_short_detial_left small {
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: rgba(0, 0, 0, 0.6);
  }
  
  .single_article_short_detial_right {
    display: flex;
    align-items: center;
  }
  
  .single_article_short_detial_right i {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-right: 10px;
  }
  
  .single_article_short_detial_right small {
    font-style: normal;
    font-weight: 400;
    font-size: 11px;
    line-height: 18px;
    color: #000;
  }
  
  .article_share_button {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .article_share_button a {
    margin-right: 10px;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    padding: 8px;
    text-decoration: none;
  }
  
  .article_share_button a:hover {
    text-decoration: none;
  }
  
  .article_share_button a.fb {
    background: #425893;
  }
  
  .article_share_button a.insta {
    background: #e95950;
  }
  
  .article_share_button a.twitter {
    background: #4D9EEA;
  }
  
  .article_share_button a.whatsapp {
    background: #65D072;
  }
  
  .article_share_button a:last-child {
    margin-right: 0px;
  }
  
  .article_share_button a i {
    font-size: 16px;
    color: #fff;
  }
  
  .single_article_main_img {
    display: flex;
    width: 100%;
    margin-bottom: 20px;
  }
  
  .single_article_main_img figure {
    display: flex;
    width: 100%;
    margin: 0px;
  }
  
  .single_article_main_img figure img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .single_article_main_detail {
    margin-bottom: 30px;
    display: inline-block;
    width: 100%;
  }
  
  /* article comment form css start */
  .article_coment_section {
    width: 100%;
    display: inline-block;
  }
  
  .article_comment_heading {
    display: flex;
    width: 100%;
  margin-bottom: 25px;
  }
  .article_comment_heading h4 {
    font-size: 20px;
    color: #000;
    text-transform: capitalize;
    font-weight: 600;
    margin: 0px;
  }
  .blog_section_one {
    display: block;
    width: 100%;
    padding: 20px 15px 20px 15px;
    background-color: #FCFAF5;
    border-radius: 4px;
  }
  .blog_page_form_content {
    display: grid;
    width: 100%;
    grid-template-columns: auto;
    grid-template-rows: auto;
  }
  .blog_form_fields {
    display: grid;
    width: 100%;
    grid-template-columns: auto;
    grid-template-rows: auto;
    gap: 7px 0px;
    padding-bottom: 10px;
  }
  .blog_form_fields span {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 26px;
    color: #000000;
  }
  .blog_form_fields input {
    background: #EAE5DB;
    border: 1px solid #EAE5DB;
    border-radius: 4px;
    height: 52px;
    padding: 0px 15px 0px 15px;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 26px;
    color: #000;
    width: 100%;
  
  }
  .blog_form_fields label {
      color: red;
  }
  
  .blog_form_fields textarea {
    background: #EAE5DB;
    border: 1px solid #EAE5DB;
    border-radius: 4px;
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 26px;
    color: #000;
    width: 100%;
    padding: 13px;
  }
  .blog_form_submit_btn {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    padding-top: 20px;
  }
  .blog_form_submit_btn button {
    border: 1px solid var(--primary-color);
    background: var(--primary-color);
    border-radius: 4px;
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 21px;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 250px;
    height: 56px;
    cursor: pointer;
  }
  /* article comment form css end */
  
  
  /* article comment list css start */
  .single_article_comments_main {
    display: flex;
    width: 100%;
    margin-bottom: 40px;
  }
  .single_article_comments_list {
    display: flex;
    flex-direction: column;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 2px;
    padding: 20px;
  }
  .single_article_comment {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 20px;
  }
  
  .single_article_comment:last-child {
    margin-bottom: 0px;
  }
  
  .single_article_comment figure {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .single_article_comment figure img {
      width: 40px;
      border-radius: 50%;
      background: #f5f5f5;
      padding: 6px;
  }
  
  .single_article_comment figure figcaption {
    display: flex;
    flex-direction: column;
    margin-left: 11px;
  }
  
  .single_article_comment figure figcaption small {
    font-size: 14px;
    font-weight: 600;
    text-transform: capitalize;
    padding-bottom: 2px;
    line-height: 22px;
    color: #000;
  }
  
  .single_article_comment figure figcaption span {
    color: #979797;
    font-size: 14px;
    line-height: 16px;
    font-weight: normal;
  }
  
  .single_article_comment p {
    font-size: 14px;
    color: #212121;
    line-height: 25px;
    font-weight: normal;
    margin: 0px;
  }
  /* article comment list css end */
  
  /* popular article css start */
  .popular_article_box {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  
  .popular_article_heading {
    display: flex;
    align-items: center;
    width: 100%;
    border-bottom: 1px solid #000;
    margin-bottom: 20px;
    justify-content: space-between;
    padding-bottom: 5px;
  }
  
  .popular_article_heading strong {
    font-size: 17px;
    background: #000;
    color: #fff;
    padding: 10px 14px;
    background-color: var(--primary-color);
    font-weight: 600;
    border-radius: 4px;
    text-transform: uppercase;
        transition: 0.2s ease-in-out;
  }
  
  .popular_article_list {
    display: grid;
    width: 100%;
    gap: 20px;
  }
  
  .popular_single_article {
    display: grid;
    width: 100%;
    gap: 20px;
    grid-template-columns: 1fr 2fr;
    text-decoration: none;
  }
  .popular_single_article:hover{
    text-decoration: none;
  }
  .popular_single_article:hover .popular_single_article_details strong{
   color: #FCC917;
       transition: 0.2s ease-in-out;
  }
  
  .popular_single_article_details h3 {
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 22px;
    color: #000000;
    margin-bottom: 9px;
    word-break: break-all;
    font-family: 'Work Sans', sans-serif !important; 
  }
  
  .popular_single_article_details {
    display: flex;
    flex-direction: column;
    width: 100%;
  }
  .popular_single_article_details strong {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 26px;
    text-decoration-line: underline;
    color: var(--primary-color);
  }
  .popular_single_article_inner_details {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  
  .popular_single_article_inner_details span {
    font-style: normal;
    font-weight: 400;
    font-size: 11px;
    line-height: 12px;
    border-right: 1px solid #000000;
    color: #000000;
    padding-right: 6px;
    margin-right: 6px;
  }
  
  
  .popular_single_article_inner_details small {
    font-style: normal;
    font-weight: 400;
    font-size: 11px;
    line-height: 12px;
    color: #000000;
  }
  
  .popular_single_article figure {
    width: 100%;
    margin: 0px;
  }
  
  .popular_single_article figure img {
    width: 100%;
  }
  /* popular article css end */
  
  
  @media(max-width:1024px){
    .single_article_box {
        grid-template-columns: 100%;
        margin: 0px 0px;
    }
    .single_article_data {
        padding: 0px 0px;
        margin-bottom: 30px;
    }
    .single_article_sidebar {
        padding: 0px 0px;
    }
  
    .popular_article_list {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 16px 16px;
    }
    .popular_single_article figure img {
        height: 150px;
        object-fit: cover;
    }
    .popular_single_article {
        gap: 15px;
        grid-template-columns: 1fr;
    }
    .popular_single_article_details h3{
        display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    height: 42px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    }
  }
  @media(max-width:768px){
  .single_article_category {
    margin-bottom: 10px;
  }
  .popular_article_list {
    grid-template-columns: 1fr 1fr;
  }
  .blog_listing {
    margin: 0px -8px;
  }
  .single_blog_box_main {
    flex: 0 0 50%;
    width: 50%;
    padding: 8px 8px 8px 8px;
  }
  
  
  }
  @media(max-width:540px){
    .single_blog_box_des_tags {
      flex-direction: column;
      border-bottom: 1px solid #000000;
      margin-bottom: 8px;
    }
    .single_blog_box_des_tags span {
      border-right: unset;
      padding-right: 0px;
      margin-right: 0px;
      padding-bottom: 6px;
    }
    .single_blog_box h3 {
      text-align: center;
    }
    .single_blog_box p {
      text-align: center;
    }
    .single_blog_box strong {
      text-align: center;
    }
    .popular_single_article {
        text-align: center;
    }
    .popular_single_article_inner_details {
        flex-direction: column;
        border-bottom: 1px solid #9b9b9b;
        padding-bottom: 8px;
        width: 100%;
        margin-bottom: 8px;
    }
    .popular_single_article_inner_details span {
        border-right: unset;
        padding-bottom: 5px;
        margin: 0px;
        padding-right: 0px;
    }
    .popular_single_article_details h3 {
        text-align: center;
    }
  }
  
  .related_blog_box {
    display: inline-block;
    float: left;
    width: 100%;
    padding: 40px 15px;
    background: #FCFAF5;
  }
  
  .related_blog_header {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding-bottom: 32px;
  }
  
  .related_blog_header h4 {
    font-size: 32px;
    line-height: 48px;
    font-weight: 500;
    text-transform: capitalize;
  }
  
  .related_blog_list {
    margin: 0px -10px;
    padding: 0px;
    -webkit-mask-image: linear-gradient(to left, transparent, black 20%, black 100%, transparent 100%);
  }
  
  .single_related_blog {
    padding: 0px 10px 0px 10px;
  }
  
  /* blog page css end */
  
  /* timmer box css start */
  .timer_box{
    padding-top: 30px;
    max-width: 540px;
    margin: 0 auto;
  }
  .timer_box ul{
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 20px 30px;
    align-items: center;
  } 
  .timer_box ul li{
    list-style: none;
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    border: 2px solid var(--primary-color);
    border-radius: 4px;
    padding: 13px;
  }
  .timer_box ul li small{
  font-style: normal;
  font-weight: 800;
  font-size: 32px;
  line-height: 34px;
  color: #000;
  padding-bottom: 8px;
  }
  .timer_box ul li span{
    font-style: normal;
    font-weight: 500;
    font-size: 26px;
    color: #000;
    }
    @media(max-width:768px){
    .timer_box ul {
      grid-template-columns: 1fr 1fr;
  }
  }
  /* timer box css end */
  
  