@extends('dashboard.include.layout')
@section('title', 'Dashboard')
@section("wrapper")

<div class="pagination-list d-flex w-100">
    <a> / Dashboard </a>
</div>


<div class="datatable_parent">
    <div class="d-flex w-100 mb-4 justify-content-end">

    </div>
    <table id="destination_listing" class="data_table display" style="width:100%">
        <thead>
            <tr>
                <th style="min-width: 40px;"></th>
                <th style="min-width: 100px;">User Name</th>
                <th style="min-width: 80px;">Status</th>
                <th style="min-width: 80px;">Featured</th>
                <th style="min-width: 80px;">Trending</th>
                <th style="min-width: 80px;">Private</th>
                <th style="min-width: 140px;">Cover Image</th>
                <th style="min-width: 220px;">Title</th>


                <th style="min-width: 80px;">Price </th>
                <th style="min-width: 80px;">Commision </th>
                <th style="min-width: 80px;">Final Price </th>

                <th style="min-width: 16px;">Action</th>
            </tr>
        </thead>
        <tbody>
            @foreach($destinations as $destination)
            <tr class="destination_row">
                <td></td>
                <td>{{ $destination->user->name }}</td>
                <td>
                    <label class="toogle_switch mb-3">
                        <input type="checkbox" class="destination_switch_input"
                            {{ $destination->status==1 ? "checked" : ""}} data-id="{{ $destination->id }}">
                        <span class="agent_switch switch round" data-id="{{ $destination->id }}"></span>
                    </label>
                </td>


                <td>
                    <label class="toogle_switch mb-3">
                        <input type="checkbox" class="destination_popular_switch_input"
                            {{ $destination->popular==1 ? "checked" : ""}} data-id="{{ $destination->id }}">
                        <span class="agent_switch switch round" data-id="{{ $destination->id }}"></span>
                    </label>
                </td>

                <td>
                    <label class="toogle_switch mb-3">
                        <input type="checkbox" class="destination_trending_switch_input"
                            {{ $destination->trending==1 ? "checked" : ""}} data-id="{{ $destination->id }}">
                        <span class="agent_switch switch round" data-id="{{ $destination->id }}"></span>
                    </label>
                </td>

                <td>
                    <label class="toogle_switch mb-3">
                        <input type="checkbox" class="destination_private_switch_input"
                            {{ $destination->flag==1 ? "checked" : ""}} data-id="{{ $destination->id }}">
                        <span class="agent_switch switch round" data-id="{{ $destination->id }}"></span>
                    </label>
                </td>


                <td>
                    <div style="width: 2.375rem;
                      height: 2.375rem;">
                        <img src="{{$destination ? asset('storage/destinations/'.$destination->cover_img) : ''}}"
                            class="w-100 h-100">
                    </div>
                </td>

                <td>{{ $destination->title }}</td>

                <td>{{ $destination->price }}</td>
                <td>{{ $destination->admin_commision }}</td>
                <td>{{ $destination->final_price }}</td>

                <td>
                    <div class="action_btn d-flex align-items-center">
                        <a href="{{ route('dashboard.destinations.update', $destination->id) }}"
                            class="edit_btn update_destination"><i class="far fa-edit"></i></a>

                    </div>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>



</div>


@endsection