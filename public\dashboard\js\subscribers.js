// Skeleton loader HTML
const skeletonLoaderHTML = `
      <div class="col-xl-3 col-sm-6">
    <div class="loading-skeleton bg_box py-4 px-4 d-flex flex-column align-items-center">

        <img style="width: 110px;height:100px;"
            alt="" width="100" class="img-fluid rounded-circle mb-3 img-thumbnail shadow-sm">
        <h5 class="mb-0"><PERSON><PERSON></h5>
        <span class="small text-uppercase text-muted mt-2 mb-3 d-flex text-center justify-content-center">CEO
            -Founder</span>
        <div class="d-grid gap-3 w-100">
            <a class="cursor-pointer">View Profile </a>

            <a class="cursor-pointer">View Listing</a>
        </div>
    </div>
</div>
`;

// Display skeleton loader

document.addEventListener("DOMContentLoaded", function () {
    const agentList = document.querySelector(".agent_list");
    const agentData = document.querySelector(".agent_data");
    if (!agentList) {
        return; // Exit if .agent_list is not found
    }

    agentList.innerHTML = skeletonLoaderHTML.repeat(4);

    let baseURL = window.location.origin;
    let apiEndpoint = "/api/subscriber/list";
    let apiUrl = baseURL + apiEndpoint;

    fetch(apiUrl)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((data) => {
            const subscribersData = data.subscribers;
            const totalSubscribers = data.totalSubscribers;
            const totalApproved = data.totalApproved;
            const totalRejected = data.totalRejected;
            const totalVerificationPending = data.totalVerificationPending;
            // Clear existing list

            agentList.innerHTML = "";

            agentData.innerHTML = "";

            agentData.innerHTML = `
            <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Total Subscribers</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">${totalSubscribers}</h3>
                            </div>
                            <small>Total Subscriber Requests</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Approved Subscribers</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">${totalApproved}</h3>
                            </div>
                            <small>Total Approved Subscribers</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-4">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-start justify-content-between">
                    <div class="content-left">
                        <span>Pending Approval Subscribers</span>
                        <div class="d-flex align-items-end mt-2">
                            <h3 class="mb-0 me-2">${totalVerificationPending}</h3>
                        </div>
                        <small>Total Pending Approval's</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Rejected Subscribers</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">${totalRejected}</h3>
                            </div>
                            <small>Total Rejected Subscribers</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;

            subscribersData.forEach((subscriber) => {
                const user = subscriber.user; // Access the user data
                const imagePath = `${baseURL}/storage/userImages/${user.profile_image}`;
                const subscriberBox = document.createElement("div");
                subscriberBox.classList.add(
                    "col-xl-3",
                    "col-sm-6",
                    "deleted_list"
                );

                subscriberBox.innerHTML = `
                    <div class="bg_box py-4 px-4 d-flex flex-column align-items-center">                  
                    <div class="d-flex align-items-center">
                    <div class="d-flex flex-column p-2 align-items-center">
                    <h6 class="mb-2">Status</h6>
                    <label class="toogle_switch mb-3">
                            <input type="checkbox" class="agent_switch_input" ${
                                subscriber.status == 1 ? "checked" : ""
                            } data-id="${subscriber.id}">
                            <span class="agent_switch switch round" data-id="${
                                subscriber.id
                            }"></span>
                        </label>
                    </div>
                    <div class="d-flex flex-column p-2 align-items-center">
                    <h6 class="mb-2">Aproval status</h6>
                    <label class="toogle_switch mb-3">
                            <input type="checkbox" class="agent_aproval_switch_input" ${
                                subscriber.aproval == 1 ? "checked" : ""
                            } data-id="${subscriber.id}">
                            <span class="agent_switch switch round" data-id="${
                                subscriber.id
                            }"></span>
                        </label>
                    </div>
                    </div>
                        <img src="${imagePath}" alt="" style="width: 110px;height:100px;"
                            class="img-fluid rounded-circle mb-3 img-thumbnail shadow-sm">
                        <h5 class="mb-0">${user.name}</h5>
                        <span class="small text-uppercase text-muted mt-2 mb-3 d-flex text-center justify-content-center">${
                            subscriber.subscription
                        }</span>
                        <div class="d-grid gap-3 w-100">
                        <span class="small text-uppercase text-muted mt-2 mb-3 d-flex text-center justify-content-center">${
                            subscriber.rejected == 1
                                ? "subscription rejected"
                                : ""
                        }</span>
                        <a class="${
                            subscriber.rejected == 1
                                ? "custom_btn_2 cursor-pointer reject_subscriber danger"
                                : "custom_btn_2 cursor-pointer reject_subscriber"
                        }"  data-id="${
                    subscriber.id
                }"  class="custom_btn_2 cursor-pointer reject_subscriber" >Reject</a>  
                       
                        <a  data-id="${
                            subscriber.id
                        }"  class="custom_btn_2 cursor-pointer delete_subscriber" >Delete</a>   
                        <a href="/dashboard/agent/profile/${
                            subscriber.user_id
                        }" class="custom_btn_2 cursor-pointer">View Profile</a>
                            <a href="/destination/list/${
                                subscriber.user_id
                            }" class="custom_btn_3 cursor-pointer">View Listing</a>
                        </div>
                    </div>
                `;
                agentList.appendChild(subscriberBox);
            });
        })

        .catch((error) => {
            console.error(
                "There was a problem with the fetch operation:",
                error.message
            );
        });
});

document.addEventListener("change", function (event) {
    if (event.target.classList.contains("agent_switch_input")) {
        const subscriberId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/subscribers/${subscriberId}/status`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { status: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
                $(".form_process_loader").addClass("d-none");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
                $(".form_process_loader").addClass("d-none");
            },
        });
    }
});

document.addEventListener("change", function (event) {
    if (event.target.classList.contains("agent_aproval_switch_input")) {
        const subscriberId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/subscriber/aproval/${subscriberId}/status`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { status: checked },
            success: function (response) {
                // Show success message or update UI as needed
                $(".form_process_loader").addClass("d-none");
                showAlert("updated successfully !", "success");
                window.location.reload();
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
                $(".form_process_loader").addClass("d-none");
            },
        });
    }
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_subscriber")) {
        const subscriptionId = event.target.getAttribute("data-id");
        // console.log(subscriptionId);
        document
            .querySelector("#subscriber_confirmDeleteBtn")
            .setAttribute("data-id", subscriptionId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#subscriber_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/user/subscription/${getId}`;
        let apiUrl = baseURL + apiEndpoint;

        $(".form_loader").addClass("d-flex").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Subscription deleted successfully!", "success");
                $(".form_loader").removeClass("d-flex").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_subscriber")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".deleted_list").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting subscription.",
                    "error"
                );
            },
        });
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("reject_subscriber")) {
        const rejectionId = event.target.getAttribute("data-id");
        // console.log(rejectionId);
        document
            .querySelector("#subscriber_confirmRejectBtn")
            .setAttribute("data-id", rejectionId);
        var myModal = new bootstrap.Modal(
            document.getElementById("rejectConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#subscriber_confirmRejectBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        const rejectReason = document.getElementById("rejectReason").value; // Get the rejection reason from the input field

        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/user/subscription/reject/${getId}`;
        let apiUrl = baseURL + apiEndpoint;

        $(".form_loader").addClass("d-flex").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "POST",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { message: rejectReason },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Subscription rejected successfully!", "success");
                $(".form_loader").removeClass("d-flex").addClass("d-none");

                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("rejectConfirmationModal")
                );
                myModal.hide();
                window.location.reload();
            },
            error: function (xhr, status, error) {
                // Handle error response
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting subscription.",
                    "error"
                );
            },
        });
    });
});
