@extends('dashboard.include.layout')
@section('title', 'profile')
@section("wrapper")

<div class="pagination-list d-flex w-100">
    <a> / Dashboard / Profile</a>
</div>

<div class="d-flex mb-3 justify-content-end">
    <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button" data-bs-toggle="offcanvas"
        data-bs-target="#offcanvasRestPassword"><span>Reset Password</span></button>
</div>
@php($user = auth()->user())


<form id="update_admin_profile" method="post" enctype="multipart/form-data" novalidate="novalidate">

    @csrf
    <input type="hidden" name="id" value="{{$user->id}}">
    <div class="d-grid gap-3 w-100 bg_box">
        <div class="card_heading">
            <h5>Profile Detail</h5>
        </div>
        <div class="d-grid gap-3 w-100">
            <div class="form_field_box d-grid gap-2 w-100">
                <label for="profile_img">Profile Image</label>
                <input id="profile_img" type="file" name="profile_img" class="dropify" data-max-file-size="2M"
                    data-allowed-file-extensions="jpg jpeg png webp"
                    data-default-file="{{$user ? asset('storage/userImages/'.$user->profile_image) : ''}}" />
            </div>
            <div class="form_field_box d-grid gap-2 w-100">
                <label for="name">Name</label>
                <input type="text" id="name" class="custom_form_field" name="name" value="{{$user->name}}" />
            </div>
            <div class="form_field_box d-grid gap-2 w-100">
                <label for="email">Email</label>
                <input type="email" id="email" class="custom_form_field" name="email" readonly value="{{$user->email}}" />
            </div>
            <div class="form_field_box d-grid gap-2 w-100">
                <label for="about">About the Guide</label>
                <input type="text" id="about" class="custom_form_field" name="about" value="{{$user->subscriber->guide_detail}}" />
            </div>
           
            <div class="form_field_box d-grid gap-2 w-100">
                <label for="lang">Languages Spoken</label>
                <input
                    type="text"
                    id="lang"
                    class="custom_form_field custom_tags_input"
                    name="lang"
                    value="{{$user->subscriber->languages_spoken}}" />
            </div>

            <div class="form_field_box d-grid gap-2 w-100">
                <label for="certificate">Certifications</label>
                <input
                    type="text"
                    id="certificate"
                    class="custom_form_field custom_tags_input"
                    name="certificate"

                    value="{{$user->subscriber->certificates}}" />
            </div>

             <div class="form_field_box d-grid gap-2 w-100">
                <label for="agent_points">Why travel with {{$user->name}}</label>
                <input
                    type="text"
                    id="agent_points"
                    class="custom_form_field custom_tags_input"
                    name="agent_points"

                    value="{{$user->subscriber->agent_points}}" />
            </div>

             <div class="form_field_box d-grid gap-2 w-100">
                <label for="c_name">Company Name</label>
                <input type="text" id="c_name" class="custom_form_field" name="c_name" value="{{$user->subscriber->company_name}}" />
            </div>
            <div class="form_field_box d-grid gap-2 w-100">
                <label for="bio">Company Bio</label>
                <input type="text" id="bio" class="custom_form_field" name="bio" value="{{$user->subscriber->company_description}}" />
            </div>


        </div>
        <div class="d-flex mb-3 justify-content-end">

            <button type="submit" class="custom_btn_1">update</button>
        </div>

    </div>
</form>
@include("dashboard.adminProfile.resetPassword")
@endsection