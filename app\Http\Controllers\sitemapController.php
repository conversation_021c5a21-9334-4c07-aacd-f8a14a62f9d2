<?php
namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\DestinationCategory;
use App\Models\Destinations;
use App\Models\DestinationType;
use Carbon\Carbon;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;

class sitemapController extends Controller
{
    public function generateSitemap()
    {
        $sitemap = Sitemap::create()
            ->add(Url::create('/')
                    ->setLastModificationDate(Carbon::yesterday())
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY)
                    ->setPriority(0.9));

        //Add more URLs as needed
        //  For example, if you have a blog, you could add blog posts dynamically
        $blog = Blog::where('status', 1)->where('type', 'blog')->get();
        foreach ($blog as $post) {
            $sitemap->add(Url::create('/blog/detail/' . $post->slug)
                    ->setLastModificationDate($post->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.8));
        }

        $news = Blog::where('status', 1)->where('type', 'news')->get();
        foreach ($news as $post) {
            $sitemap->add(Url::create('/news/detail/' . $post->slug)
                    ->setLastModificationDate($post->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.8));
        }

        $destinationType = DestinationType::all();
        $destinationCategory = DestinationCategory::all();
        $destination = Destinations::where('status', 1)->get();

        foreach ($destinationType as $type) {
            $sitemap->add(Url::create('/destination/type/' . $type->slug)
                    ->setLastModificationDate($type->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.8));
        }
        foreach ($destinationCategory as $cat) {
            $sitemap->add(Url::create('/destination/category/' . $cat->slug)
                    ->setLastModificationDate($cat->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.8));
        }
        foreach ($destination as $des) {
            $sitemap->add(Url::create('/destination/detail/' . $des->slug)
                    ->setLastModificationDate($des->updated_at)
                    ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY)
                    ->setPriority(0.8));
        }

        // Add additional URLs manually
        $sitemap->add('/home')
            ->add('/about')
            ->add('/contact')
            ->add('/blog')
            ->add('/news')
            ->add('/subscription')
            ->add('/destination/types')
            ->add('/destination/categories')
            ->add('/destinations')
        ;

        // Return the sitemap as an XML response
        return $sitemap->render();
    }

}
