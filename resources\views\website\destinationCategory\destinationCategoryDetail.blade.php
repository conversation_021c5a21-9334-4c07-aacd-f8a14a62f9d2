@extends('website.include.layout')
@section('title', 'Destination Category | ' . $category->meta_title ?? 'Default Meta Title')

@section('meta_title', $category->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $category->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $category->meta_description ?? 'Default Meta Description')
@section('h1', $category->page_h1_heading ?? 'Default H1 Heading')


@section('content')


<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('<?php echo url('storage/destinations/' . $category->banner_image); ?>');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">
                <h2>{{$category->title}}</h2>
                <p>Travel With Us</p>
            </div>
        </div>
    </div>
</section>

<div class="sop_page_parent w-100 d-block">
    <div class="custom_container">
        <div class="shop_content_body d-flex">

            <div class="d-flex flex-column">
                <div class="single_article_bread_crums d-flex w-100 align-items-center flex-wrap">
                    <a href="/">Home</a><i class="fas fa-angle-right"></i><a href="{{route('destinations')}}">
                        Destinations</a> <i class="fas fa-angle-right"></i><a
                        href="{{route('website.destinationCategory.destinationCategory')}}">
                        Categories</a> <i class="fas fa-angle-right"></i><a>{{$category->title}}</a>
                </div>
                <div class="shop_sort_filter_parent d-flex align-items-center justify-content-between w-100 flex-wrap">

                    <h5>Results ({{ $destinations->count() }})</h5>

                </div>
                <div class="product_listing_box d-flex flex-column w-100">
                    <div class="d-flex flex-column">

                        <div class="product_listing_section row">

                            @foreach($destinations as $destination)

                            @php
                            $departureDatesTimesArray = explode(", ", $destination->departure_date_time);
                            $departureDateTimeObjects = [];
                            foreach ($departureDatesTimesArray as $dateTimeString) {
                            $dateTimeObject = \DateTime::createFromFormat('Y-m-d h:i A', $dateTimeString);
                            if ($dateTimeObject !== false) {
                            $departureDateTimeObjects[] = $dateTimeObject;
                            }
                            }

                            $currentDateTime = new \DateTime();
                            $nearestDateTime = null;
                            $nearestDiff = PHP_INT_MAX;
                            foreach ($departureDateTimeObjects as $dateTime) {
                            $diff = $dateTime->getTimestamp() - $currentDateTime->getTimestamp();
                            if ($diff > 0 && $diff < $nearestDiff) { $nearestDiff=$diff; $nearestDateTime=$dateTime; } }
                                $nearestDateString=$nearestDateTime ? $nearestDateTime->format('Y-m-d h:i A') :
                                '';
                                @endphp

                                <div class="single_product col-12 col-md-4 col-lg-4">
                                    <div class="product_card w-100">
                                        <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                            class="destination_img w-100 position-relative">
                                            <img src="{{$destination ? asset('storage/destinations/'.$destination->cover_img) : asset('website/images/logo.png') }}"
                                                class="w-100 h-100 object-fit-cover"
                                                alt="{{$destination->alt_text_cover_image}}" loading="lazy" decoding="async" width="350" height="250" />
                                            <div class="nearest_departure_date">
                                                {{customDate($nearestDateString,'F d, Y')}}
                                            </div>
                                        </a>
                                        <div class="product_content d-flex flex-column w-100">

                                            <div class="shop_single_package_rating d-flex">
                                                <i class="far fa-star"></i>
                                                <i class="far fa-star"></i>
                                                <i class="far fa-star"></i>
                                                <i class="far fa-star"></i>
                                                <i class="far fa-star"></i>
                                            </div>

                                            <div class="shop_package_location d-flex align-items-start flex-column">
                                                <span class="d-flex align-items-center"><i
                                                        class="far fa-calendar-check"></i>{{$destination->days}}
                                                    Days - {{$destination->nights}} Nights</span>
                                                <span class="d-flex align-items-center"><i
                                                        class="fas fa-map-marker-alt"></i>
                                                    {{$destination->destination_country}}</span>
                                            </div>
                                            <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                                class="product_name">{{$destination->title}}</a>
                                            <div class="shop_short_des">{!! $destination->short_description !!}
                                            </div>
                                            <div class="price_tags d-flex flex-column w-100">
                                                <div class="total_and_discount_price d-flex flex-column">
                                                    <span class="price_label">Per person</span>
                                                    <small class="orignal_price"><span
                                                            class="price_currency text-uppercase">{{$destination->currency}}</span>
                                                        {{$destination->final_price}}</small>

                                                </div>
                                                <div class="product_cart_btn">
                                                    <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                                        class="d-flex align-items-center justify-content-center">View
                                                        Detail</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach

                                <!-- <div class="pagination">
                                    {{ $destinations->links() }}
                                </div> -->
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>




@endsection