<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRemainingPayment{{$destinationBookingList->id}}"
    aria-labelledby="offcanvasRemainingPayment{{$destinationBookingList->id}}Label">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 id="offcanvasRemainingPayment{{$destinationBookingList->id}}Label" class="offcanvas-title">Advance Payment
        </h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="remaining_payment_{{$destinationBookingList->id}}">
            @csrf
            <input type="hidden" name="id" value="{{$destinationBookingList->id}}" />
            <div class="d-grid gap-3 w-100">

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="message">Message</label>
                    <textarea class="custom_form_field" name="message" id="message" rows="10"></textarea>
                </div>

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="total_price">Destination price (per person)</label>
                    <input type="number" min="0" value="{{$destinationBookingList->single_price}}"
                        class="custom_form_field" name="per_pserson_price" readonly />
                </div>
                
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="total_price">Destination Total price</label>
                    <input type="number" min="0" value="{{$destinationCountPrice}}"
                        class="custom_form_field" name="total_price" readonly />
                </div>
                <div class=" form_field_box d-grid gap-2 w-100">
                    <label for="extras_total_price">Extras Total price</label>
                    <input type="number" value="{{$extraTotalValue}}" min="0" class="custom_form_field "
                        name="extras_total_price" readonly />
                </div>
                
                  <div class=" form_field_box d-grid gap-2 w-100">
                    <label for="extras_total_price">Single Seperate Room Price</label>
                    <input type="number" value="{{$destinationBookingList->destination->single_room_price}}" min="0" class="custom_form_field "
                        name="single_room_price" readonly />
                </div>
                
                  <div class=" form_field_box d-grid gap-2 w-100">
                    <label for="extras_total_price">Seperate Room Total Price</label>
                    <input type="number" value="{{$roomAdonPrice}}" min="0" class="custom_form_field "
                        name="seperate_room_total_price" readonly />
                </div>
               
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="final_price_plus_extras">Final Pay Able Price</label>
                    <input type="number" value="{{$payAbleTotalPrice}}"
                        min="0" class="custom_form_field " name="final_payable_price" readonly />
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="advance_percentage">Advance in percentage</label>
                    <input type="number" min="0" value="20" max="100" class="custom_form_field"
                        name="advance_percentage" readonly />
                </div>
                <?php
$percentage = 20;
$advancePrice = $payAbleTotalPrice * ($percentage / 100);

?>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="advance_price">Advance in price (already paid)</label>
                    <input type="number" min="0" value="{{$advancePrice}}" class="custom_form_field"
                        name="advance_price" readonly />
                </div>

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="remaining_price">Remaining price</label>
                    <input type="number" min="0" value="{{$payAbleTotalPrice - $advancePrice}}"
                        class="custom_form_field" name="remaining_price" readonly />
                </div>


                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="destination_detail">Add pdf</label>
                    <input id="destination_detail" type="file" name="destination_detail" class="dropify"
                        data-max-file-size="2M" data-allowed-file-extensions="jpg jpeg png pdf webp"
                        data-default-file="{{asset('storage/destinationPdf/'.$destinationBookingList->destination->destination_pdf)}}" />
                </div>

                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button class="custom_btn_2">Submit
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>
        </form>
    </div>
</div>