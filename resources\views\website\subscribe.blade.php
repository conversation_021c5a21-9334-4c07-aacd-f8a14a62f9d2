@extends('website.include.layout')
@section('title', 'subscribe')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')


@section('content')


<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('/website/images/subscribeBanner.webp');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">

                <h2>Join Our Network of Tour Operators and Expand Your Reach</h2>
                <p>Unlock new opportunities for your tour operator business with our subscription form. Join our
                    platform to access a wide range of features and connect with travelers worldwide. Start your journey
                    with us today.</p>
            </div>
        </div>
    </div>
</section>

<section class="login_section display-block float-left w-100">

    <div class="custom_container">
        <div class="login_section_parent">

            <!-- banner slider -->
            <div id="bannerSlider" class="carousel login_slider slide w-100" data-bs-ride="carousel">
                <div class="carousel-indicators banner_dots">
                    <button type="button" data-bs-target="#bannerSlider" data-bs-slide-to="0" class="active"
                        aria-current="true" aria-label="Slide 1"></button>
                

                </div>
                <div class="carousel-inner">
                    <div class="carousel-item active">
                        <img src="{{asset('website/images/travelafrica.webp')}}" class="d-block w-100 object-fit-cover h-100" alt="slide1" width="477" height="955" decoding="async" loading="lazy" >
                    </div>
                   

                </div>
            </div>

            <div class="login_section_box d-grid w-100 align-items-center">
                <div class="login_tabs_main  d-flex w-100 align-items-center justify-content-center w-100 flex-column">
                    <div class="login_section_box_heading d-grid w-100">
                        <h3>Become a Member</h3>
                        <p>Unlock new opportunities: Subscribe to add your listing to our site!
                        </p>
                    </div>
                    @php($user = auth()->user())
                    <form method="POST" id="subscription_form" enctype="multipart/form-data" class="w-100">
                        @csrf
                        <input type="hidden" name="user_id" value="{{$user->id}}">
                        <div class="login_form_content_box">
                            <div class="login_form_field_box_parent">
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Name *</span>
                                        <input id="name" type="text" placeholder="Enter your name" name="name"
                                            value="{{$user->name}}" readonly>
                                        <label class="error" generated="true" for="name"></label>
                                    </div>

                                </div>
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Email *</span>
                                        <input type="email" placeholder="Enter your email" id="email" name="email"
                                            value="{{$user->email}}" readonly>

                                        <label class="error" generated="true" for="email"></label>
                                    </div>

                                </div>
                            </div>

                            <div class="login_form_field_box_parent">
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Phone Num *</span>
                                        <input id="phone" type="number" placeholder="Enter your mobile" name="phone">
                                        <label class="error" generated="true" for="phone"></label>
                                    </div>

                                </div>
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Subscription *</span>
                                        <input type="hidden" name="subscription" value="{{ $selectedPackage }}">
                                        <select placeholder="select your subscription" id="subscription"
                                            name="subscription" disabled>
                                            @foreach($subscriptions as $subscription)
                                            <option value="{{ $subscription->package_type }}"
                                                {{ $subscription->package_type == $selectedPackage ? 'selected' : '' }}>
                                                {{ $subscription->package_type }}</option>
                                            @endforeach
                                        </select>
                                        <label class="error" generated="true" for="subscription"></label>
                                    </div>

                                </div>

                            </div>
                            <div class="login_form_field_box_parent">
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Country *</span>
                                       <!-- <input type="text" placeholder="Enter your country" id="country" name="country"> -->
                                        <select  placeholder="Enter your country" id="country" name="country">
                                            <option selected disabled> Select your country</option>
                                          @foreach($country as $country)
                                            <option value="{{ $country->country }}"> {{ $country->country }}</option>
                                            @endforeach
                                        </select>    
                                        <label class="error" generated="true" for="country"></label>
                                    </div>

                                </div>
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Address *</span>
                                        <input type="text" placeholder="Enter your address" id="address" name="address">

                                        <label class="error" generated="true" for="address"></label>
                                    </div>

                                </div>
                            </div>


                            <div class="login_form_field_box_parent">

                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Registration Number (optional)</span>
                                        <input type="text" placeholder="Enter registration number " id="reg_number"
                                            name="reg_number">

                                    </div>
                                    <label class="error" generated="true" for="reg_number"></label>
                                </div>

                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>TIN Number (optional)</span>
                                        <input type="text" placeholder="Enter your tin number" name="tin_number"
                                            id="tin_number">

                                    </div>
                                    <label class="error" generated="true" for="tin_number"></label>
                                </div>
                            </div>
                            <div class="login_form_field_box_parent">
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Company Name *</span>
                                        <input type="text" placeholder="Enter your company name" name="company_name"
                                            id="company_name">

                                    </div>
                                    <label class="error" generated="true" for="company_name"></label>
                                </div>
                                <div class="login_form_field_box">
                                    <div class="login_single_field_box">
                                        <span>Website Link (optional)</span>
                                        <input type="text" placeholder="Enter your website link"
                                            name="company_website_link" id="company_website_link">

                                    </div>
                                    <span id="suggestion" style="
                                                font-style: normal;
                                                font-weight: 500;
                                                font-size: 14px;
                                                line-height: 16px;
                                                letter-spacing: 0.03em;
                                                color: #000;
                                                padding-top: 9px;
                                            ">Suggestion (https://example.com)</span>
                                    <label class="error" generated="true" for="company_website_link"></label>
                                </div>

                            </div>
                            <div class="login_form_field_box">
                                <div class="login_single_field_box">
                                    <span>Company Description *</span>
                                    <input type="text" placeholder="Enter your company description"
                                        id="company_description" name="company_description">

                                </div>
                                <label class="error" generated="true" for="company_description"></label>
                            </div>

                            <div class="login_form_field_box_parent">
                                <div for="licence_file" class="drag_and_drop_box" data-for="licence_file">
                                    <small>Select your licence files here *</small>


                                    <input id="licence_file" type="file" name="licence_file" class="dropify"
                                        data-max-file-size="2M" data-allowed-file-extensions="jpg jpeg png gif webp" />
                                    <label class="error" generated="true" for="licence_file"></label>
                                </div>
                                <div for="company_logo" class="drag_and_drop_box" data-for="company_logo">
                                    <small>Select your company logo files here </small>

                                    <input id="company_logo" type="file" name="company_logo" class="dropify"
                                        data-max-file-size="2M" data-allowed-file-extensions="jpg jpeg png gif webp" />
                                    <label class="error" generated="true" for="company_logo"></label>
                                </div>
                            </div>

                            <div class="login_form_signin_btn">
                                <button>Subscribe</button>
                            </div>
                        </div>
                    </form>

                </div>
            </div>

        </div>
    </div>
</section>

@endsection