<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Travel Africa | Verify Email</title>
    <meta name="description" content="Travel Africas">
    <meta name="keywords" content="travel,africas,keniya,uganda">
    <meta name="author" content="Travel Africas">
    <link rel="icon" type="image/x-icon" href="{{asset('website/images/favicon.png')}}">

    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css"
        integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css"
        integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.12.1/css/all.min.css">
    <link rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.15.3/css/all.css"
        integrity="sha384-iKbFRxucmOHIcpWdX9NTZ5WETOPm0Goy0WmfyNcl52qSYtc2Buk0NCe6jU1sWWNB" crossorigin="anonymous">

    <!-- font links -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Yesteryear&display=swap" rel="stylesheet">
    <style>
    * {
        -web-kit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        padding: 0px;
        margin: 0px;
        font-family: Poppins;
    }

    html {
        scroll-behavior: smooth;
    }

    body {
        float: left;
        width: 100%;
        margin: 0px;
        font-family: Poppins;
        background-color: #fff;
    }

    .verify-email-parent {
        float: left;
        width: 100%;
        display: block;
        padding: 20px 15px;
    }

    .email-container {
        max-width: 768px;
        margin: 0 auto;

    }

    .verify-email-logo {
        float: left;
        width: 100%;
        text-align: center;
        margin-bottom: 20px;
        padding: 20px 15px;
        background-color: #f5f5f5;
    }

    .verify-email-logo img {
        max-width: 300px;
    }

    .verify-email-main {
        display: block;
        width: 100%;
        background-color: #f5f5f5;
        float: left;
    }

    .verify-email-list {
        display: block;
        width: 100%;
        padding: 0px 15px 35px 15px;
        float: left;
    }

    .verify-email-list p {
        display: block;
        width: 100%;
        text-align: left;
        font-size: 16px;
        font-weight: 400;
        line-height: 26px;

        float: left;
    }

    .verify-email-list small {
        display: block;
        width: 100%;
        text-align: left;
        font-size: 16px;
        font-weight: 500;
        line-height: 26px;

        float: left;
        padding-top: 10px;
        padding-bottom: 10px;
        color: green;
    }

    .verify_btn {
        display: grid;
        grid-template-columns: 4fr 1fr;
        width: 100%;
        gap: 10px 20px;
    }

    .verify_btn button {
        display: block;
        width: 100%;
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        line-height: 28px;
        float: left;
        color: #fff;
        text-decoration: none;
        background-color: #823602;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        border-color: #823602;
    }

    @media(max-width:768px) {
        .verify-email-logo img {
            max-width: 160px;
        }

        .verify-email-list p {
            font-size: 14px;
            line-height: 20px;
        }

        .verify_btn {
            grid-template-columns: 1fr;
        }
    }
    </style>

</head>

<body>
    <div class="verify-email-parent">
        <div class="email-container">
            <div class="verify-email-main">
                <figure class="verify-email-logo"> <img src="{{asset('website/images/logo.png')}}" alt="logo">
                </figure>
                <div class="verify-email-list">
                    <p> {{ __('Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn\'t receive the email, we will gladly send you another.') }}
                    </p>
                    <small>
                        @if (session('status') == 'verification-link-sent')
                        {{ __('A new verification link has been sent to the email address you provided during registration.') }}
                        @endif
                    </small>
                    <div class="verify_btn">
                        <form method="POST" action="{{ route('verification.send') }}">
                            @csrf

                            <button>{{ __('Resend Verification Email') }}</button>
                        </form>

                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit">{{ __('Log Out') }}</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>

</body>

</html>
