   <!-- Offcanvas to add new package -->
   <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddDestinationType"
       aria-labelledby="offcanvasAddDestinationTypeLabel">
       <!-- Offcanvas Header -->
       <div class="offcanvas-header py-4 flex-column">
           <div class="d-flex align-content-center justify-content-between w-100">
               <h5 id="offcanvasAddDestinationTypeLabel" class="offcanvas-title">Add Type</h5>
               <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
                   aria-label="Close"></button>
           </div>
           <ul>
               <li>Upload Image must be less then 1 MB (10kb or 50 kb)</li>
               <li>Upload image of same dimension for all category and banners</li>
           </ul>
       </div>
       <!-- Offcanvas Body -->
       <div class="offcanvas-body border-top">
           <form class="pt-0" id="destination_add_type_form">
               @csrf


               <div class="d-grid gap-3 w-100">

                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="title">Destination Type</label>
                       <input class="custom_form_field" name="title" id="title" />
                   </div>

                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="cover_img">Cover Image (use dimension 400px by 400px)</label>
                       <input id="cover_img" type="file" name="cover_img" class="dropify" data-max-file-size="2M"
                           data-allowed-file-extensions="jpg jpeg png gif webp" />
                   </div>
                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="alt_text_cover_image">Alt Text of Cover Image</label>
                       <input class="custom_form_field" name="alt_text_cover_image" id="alt_text_cover_image" />
                   </div>

                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="banner_img">Banner Image (use dimension 2500px by 400px)</label>
                       <input id="banner_img" type="file" name="banner_img" class="dropify" data-max-file-size="2M"
                           data-allowed-file-extensions="jpg jpeg png gif webp" />
                   </div>
                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="alt_text_banner_image">Alt Text of Banner Image</label>
                       <input class="custom_form_field" name="alt_text_banner_image" id="alt_text_banner_image" />
                   </div>


                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="page_h1_heading">Seo H1 heading (55 to 60 charactor) </label>
                       <label>Total character (<span class="h1_headingCharCount"></span>) </label>
                       <textarea class="custom_form_field page_h1_heading" id="page_h1_heading" type="text"
                           name="page_h1_heading" rows="4"></textarea>
                   </div>

                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="meta_title">Meta Title (55 to 60 charactor)</label>
                       <label>Total character (<span class="meta_titleCharCount"></span>) </label>
                       <textarea class="custom_form_field meta_title" id="meta_title" type="text" name="meta_title"
                           rows="4"></textarea>
                   </div>
                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="meta_description">Meta Description (155 to 160 charactor)</label>
                       <label>Total character (<span class="meta_descriptionCharCount"></span>) </label>
                       <textarea class="custom_form_field meta_description" id="meta_description" type="text"
                           name="meta_description" rows="4"></textarea>
                   </div>
                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="meta_keywords">Meta keywords (comma seperated)</label>
                       <textarea class="custom_form_field" id="meta_keywords" type="text" name="meta_keywords"
                           rows="4"></textarea>
                   </div>
                   <!-- Submit and reset -->
                   <div class="d-grid gap-2 w-100">
                       <button type="submit" class="custom_btn_2">Create
                           <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                               style="background-color: var(--secondary-bg-color);">
                               <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                           </div>
                       </button>

                       <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                   </div>
               </div>


           </form>
       </div>
   </div>
