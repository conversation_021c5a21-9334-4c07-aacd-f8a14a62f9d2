@extends("dashboard.include.layout")


@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a> / Booking / Payment's Record</a>
</div>
<div class="content-section-box">




    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">

        </div>
        <table id="custom_trip_payment" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 60px;">Trip title</th>
                     <th style="min-width: 60px;">Payment ID</th>
                    <th style="min-width: 100px;">Name</th>
                    <th style="min-width: 100px;">Email</th>
                    <th style="min-width: 100px;">Payment Type</th>
                    <th style="min-width: 100px;">Price</th>
                    <th style="min-width: 300px;">Payment Slip</th>
                     <th style="min-width: 100px;">Received Payment</th>
                      <th style="min-width: 100px;">Charges</th>
                     <th style="min-width: 16px;">Action</th> 
                </tr>
            </thead>
            <tbody>
             
                @foreach($bookingTripPayments as $paymentList)
                <tr class="trip_row">
                    <td></td>

                    <td>{{ $paymentList->bookedDestination->destination->title }}</td>
                     <td>{{ $paymentList->payment_id }}</td>
                    <td>{{ $paymentList->user->name }}</td>
                    <td>{{ $paymentList->user->email }}</td>
                    <td>{{ $paymentList->payment_type }}</td>
                    <td>{{ $paymentList->price }}</td>
                    <td>
                        <a href="{{$paymentList->direct_slip ? asset('storage/directPaymentSlip/'.$paymentList->direct_slip) : '#'}}"
                            style="display:block;width: 80px;">

                            @if (!empty($paymentList->direct_slip))
                            <img src="{{ asset('storage/directPaymentSlip/' . $paymentList->direct_slip) }}"
                                class="w-100 h-100" />
                            @else
                            Strip Payment
                            @endif

                        </a>
                    </td>
                     <td>{{ $paymentList->receive_payment }}</td>
                      <td>{{ $paymentList->charges }}</td>
                      <td>
                    <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                            
                                <button class="update_btn edit_payment_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasUpdatePayment{{$paymentList->id}}"
                                    data-form-id="update_payment_{{$paymentList->id}}"
                                    data-id="{{$paymentList->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                            </div>
                        </div>
                      @include("dashboard.destinations.updatePayment")
                      </td>
                </tr>

                @endforeach
            </tbody>
        </table>
    </div>

</div>

<!-- @include("dashboard.customTripPayments.delete") -->

@endsection