@extends("dashboard.include.layout")
@section("wrapper")


<div class="pagination-list d-flex w-100">
    <a> / Custom Trip / Details</a>
</div>


<div class="card mb-4">

    <div class="user-profile-header d-flex flex-column flex-sm-row text-sm-start text-center mb-4 mt-4">
        <div class="flex-shrink-0 mt-n2 mx-sm-0 mx-auto">
            <img src="{{ asset('/storage/userImages/' . $customTrip->user->profile_image) }}" alt="user image"
                class="d-block h-auto ms-0 ms-sm-4 rounded user-profile-img">

        </div>
        <div class="flex-grow-1 mt-3 mt-sm-5">
            <div
                class="d-flex align-items-md-end align-items-sm-start align-items-center justify-content-md-between justify-content-start mx-4 flex-md-row flex-column gap-4">
                <div class="user-profile-info">

                    <h4 class="mb-2">{{ $customTrip->user->name }}</h4>
                    <ul class="list-inline mb-0 d-flex flex-wrap justify-content-sm-start gap-2 flex-column">

                        <li class="list-inline-item fw-medium text-lowercase">
                            {{ $customTrip->user->email }}
                        </li>
                        <li class="list-inline-item fw-medium text-lowercase">
                            {{ $customTrip->phone }}
                        </li>

                    </ul>
                </div>

                <a href="javascript:void(0)" type="button" class="btn btn-success">
                    {{ $customTrip->status}}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row g-4 mb-4 mb-4">
    <div class="col-xl-4 col-lg-6 col-md-6">
        <div class="card profile_subscription_details h-100">
            <div class="card-body">
                <small class="text-muted text-uppercase">About</small>
                <ul class="list-unstyled mb-4 mt-3">
                    <li class="d-flex  mb-3"><i class="bx bx-star"></i><span class="fw-medium mx-2">Total
                            Price:</span>
                        <span>{{ $customTrip->total_price }}</span>
                    </li>
                    <li class="d-flex  mb-3"><i class="bx bx-star"></i><span class="fw-medium mx-2">Advance
                            Price:</span>
                        <span>{{ $customTrip->advance_price }}</span>
                    </li>
                    <li class="d-flex  mb-3"><i class="bx bx-star"></i><span class="fw-medium mx-2">Total
                            Participant:</span>
                        <span>{{ $customTrip->total_participant }}</span>
                    </li>
                    <li class="d-flex  mb-3"><i class="bx bx-flag"></i><span class="fw-medium mx-2">Total Adults:</span>
                        <span>{{ $customTrip->total_adults }}</span>
                    </li>
                    <li class="d-flex  mb-3"><i class="bx bx-flag"></i><span class="fw-medium mx-2">Total
                            Children:</span> <span>{{ $customTrip->total_children }}</span>
                    </li>
                    <li class="d-flex  mb-3"><i class="bx bx-flag"></i><span class="fw-medium mx-2">Total Days:</span>
                        <span>{{ $customTrip->total_days }}</span>
                    </li>
                    <li class="d-flex  mb-3"><i class="bx bx-flag"></i><span class="fw-medium mx-2">Dates:</span>
                        <span>{{ $customTrip->dates }}</span>
                    </li>
                    <li class="d-flex  mb-3 flex-column"><i class="bx bx-phone"></i><span
                            class="fw-medium mx-2">message:</span>
                        <span>{{ $customTrip->message }}</span>
                    </li>
                </ul>

            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-6 col-md-6">
        <div class="card profile_subscription_details h-100">
            <div class="card-body">
                <small class="text-muted text-uppercase">Destinations Details</small>
                <ul class="list-unstyled mt-3">
                    <li class="d-flex  mb-3 flex-column"><span class="fw-medium mx-2">Country :</span>
                        @foreach (explode(',', $customTrip->country) as $country)
                        <span class=" m-2 p-2" style="background-color: var(--ternary-bg-color);">{{ $country }}</span>
                        @endforeach
                    </li>
                    <li class="d-flex  mb-3 flex-column"><span class="fw-medium mx-2">city :</span>

                        @foreach (explode(',', $customTrip->city) as $city)
                        <span class=" m-2 p-2" style="background-color: var(--ternary-bg-color);">{{ $city }}</span>
                        @endforeach
                    </li>
                    <li class="d-flex  mb-3 flex-column"><span class="fw-medium mx-2">Areas :</span>
                        @foreach (explode(',', $customTrip->areas) as $areas)
                        <span class=" m-2 p-2" style="background-color: var(--ternary-bg-color);">{{ $areas }}</span>
                        @endforeach
                    </li>


                </ul>

            </div>
        </div>
    </div>
    <div class="col-xl-4 col-lg-6 col-md-6">
        <div class="card profile_subscription_details h-100">
            <div class="card-body">
                <small class="text-muted text-uppercase">Attachments</small>

                <div class="row mt-3">
                    <div class="">
                        <small class="text-muted text-uppercase mb-3 d-block">Destination Details</small>
                        <a href="{{ asset('/storage/customTrip/' . $customTrip->destination_details) }}"><img
                                src="{{ asset('/storage/customTrip/' . $customTrip->destination_details) }}"
                                class="w-100" /></a>
                    </div>

                </div>
            </div>
        </div>
    </div>

</div>

<div class="datatable_parent">
    <div class="d-flex w-100 mb-4 justify-content-end">

    </div>
    <table id="custom_trip_detail" class="data_table display" style="width:100%">
        <thead>
            <tr>
                <th style="min-width: 40px;"></th>
                <th style="min-width: 100px;">Payment Type</th>
                <th style="min-width: 100px;">Price</th>
                <th style="min-width: 300px;">Payment Slip</th>
            </tr>
        </thead>
        <tbody>
            @foreach($paymentDetails as $paymentDetail)
            <tr class="trip_row">
                <td></td>
                <td>{{ $paymentDetail->payment_type }}</td>
                <td>{{ $paymentDetail->price }}</td>
                <td>
                    <a href="{{$paymentDetail ? asset('storage/directPaymentSlip/'.$paymentDetail->direct_slip) : ''}}"
                        style="display:block;width: 80px;">
                        <img src="{{$paymentDetail ? asset('storage/directPaymentSlip/'.$paymentDetail->direct_slip) : ''}}"
                            class="w-100 h-100">
                    </a>
                </td>

            </tr>

            @endforeach
        </tbody>
    </table>
</div>

@endsection