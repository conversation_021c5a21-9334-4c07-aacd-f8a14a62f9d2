<!-- Offcanvas to add new package -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="updateSubscriptionModal"
    aria-labelledby="updateSubscriptionModalLabel">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 class="offcanvas-title">Confirm update</h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="subscription-form-update">
            @csrf
            <input type="hidden" name="id" id="update_subscription_id">

            <div class="d-grid gap-3 w-100">
                <!-- Package Type -->
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="update_package_type">Package Type</label>
                    <select id="update_package_type" name="package_type" class="custom_form_field">
                        <option selected disabled>Select Package Type</option>
                        <option value="free">Free</option>
                        <option value="advance">Advance</option>
                        <option value="premium">Premium</option>
                    </select>
                </div>
                <!-- Package Duration -->
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="update_duration">Package Duration</label>
                    <select id="update_duration" name="duration" class="custom_form_field">
                        <option selected disabled>Select Package Duration</option>
                        <option value="month">Month</option>
                        <option value="year">Year</option>
                    </select>
                </div>
                <!-- Package Duration Count -->
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="update_duration_count">Package Duration Count</label>
                    <select id="update_duration_count" name="duration_count" class="custom_form_field">
                        <option selected disabled>Package Time</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="lifetime">lifetime</option>
                    </select>
                </div>
                <!-- price -->
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="update_price">Package Price</label>
                    <input type="text" id="update_price" class="custom_form_field" placeholder="100 ETB" name="price">
                </div>

                <!-- Package currency -->
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="update_currency">Package currency</label>
                    <select id="update_currency" name="currency" class="custom_form_field">
                        <option selected disabled>Package currency</option>
                       <option value="usd" selected>USD</option>
                    </select>
                </div>

                <!-- Package Features -->
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="update_features">Package Features</label>
                    <input class="custom_tags_input custom_form_field" name="features" id="update_features" value="" />
                </div>

                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button type="submit" class="custom_btn_2">Update
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>


        </form>
    </div>
</div>
