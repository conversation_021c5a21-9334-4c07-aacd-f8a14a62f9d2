ul#iti-0__country-listbox {
  width: 280px;
}
/* generic form responce css start */

#alertMessage {
  border: unset;
  border-radius: unset;
  position: fixed;
  bottom: 100px;
  z-index: 9999999999;
  left: 50%;
  right: auto;
  transform: translateX(-50%);
  transition: 0.3s ease-in-out;
  display: flex;
  justify-content: center;
  width: 100%;
  opacity: 0;
  visibility: hidden;
  background-color: unset !important;
  border: unset !important;
  box-shadow: unset !important;
}

#alertMessage.show {
  opacity: 1;
  visibility: visible;
  transition: 0.3s ease-in-out;
}

.alertContentBox {
  box-shadow: none;
  border-radius: 25px;
  padding: 14px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
}

.alertContentBox span {
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 16px;
}

.alertContentBox i {
  padding-left: 30px;

  font-size: 0.875rem;
  line-height: 16px;
}

#alertMessage.alert-success .alertContentBox {
  background-color: var(--success-bg-color);
  color: var(--success-text-color);
  box-shadow: var(--main-bg-shadow);
}

#alertMessage.alert-danger .alertContentBox {
  background-color: var(--failed-bg-color);
  color: var(--failed-text-color);
  box-shadow: var(--main-bg-shadow);
}

/* generic form responce css end */

.form_process_loader {
  position: fixed;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100%;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(0, 0, 0, 0.6);
}

.form_process_loader img {
  width: 130px;
}

.custom_container {
  max-width: 1200px;
  margin: 0 auto;
}

.featured_container {
  max-width: 1600px;
  margin: 0 auto;
}

.float-left {
  float: left;
}

.space {
  padding: 0px 15px;
  margin-bottom: 60px;
}

@media(max-width:1024px) {
  .space {
    margin-bottom: 40px;
  }
}

@media(max-width:540px) {
  .space {
    margin-bottom: 30px;
  }
}

.focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: unset !important;
}

/* header style */
.header_parent {
  z-index: 999;
  position: relative;
  background: var(--header-bg-color);
  box-shadow: var(--header-bg-shadow);
}


.header_ticker {
  background: var(--primary-bg-color);
  box-shadow: 0px 20px 35px 0px var(--primary-bg-shadow);
}

.ticker_text span {
  color: var(--primary-text-color);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  padding: 5px 10px;
}

.desk_header {
  padding: 15px 15px;
}

.header_logo {
  width: 160px;
}

.header_links_list a {
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  color: var(--header-text-color) !important;
  margin-right: 33px;
  font-family: Poppins;
}

.header_links_list a:last-child {
  margin-right: 0px;
}

.header_social_link a {
  font-size: 20px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0em;
  color: var(--header-text-color);
      cursor: pointer;
}
.header_bar_2 a{
     color:#fff !important; 
    margin-right: 25px !important;
}

a.header_trip_booking_btn {
  height: 44px;
  width: 133px;
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
color: #000000;
  border-radius: 5px;
  margin-left: 30px;
  border: 1px solid #000000;
  box-shadow: 2px 2px 0px 0px #000000;

}
.header_bar_2 .header_links_list a:last-child {
    margin-right: 0px !important;
}
.header_toggle_btn i {
  font-size: 22px;
  font-weight: 600;
  line-height: 25px;
  letter-spacing: 0em;
  color: var(--header-text-color);
}

.login_drop_down {
  position: absolute;
  top: 0px;
  left: -18px;
  padding: 24px 0px;
  visibility: hidden;
  opacity: 0;
  visibility: hidden;
  transition: 0.2s;
}

.logined_btn:hover .login_drop_down {
  opacity: 1;
  visibility: visible;
  transition: 0.2s;
}

.logined_arrow {
    padding-left: 6px;
    font-size: 14px;
    padding-top: 5px;
}

.login_drop_down_list {
  background: var(--main-bg-color);
  border-radius: 4px;
  width: 195px;
  padding: 20px 15px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 3px 10px var(--main-bg-shadow);
}

.login_drop_down_list a {
  display: flex;
  width: 100%;
  color: #000 !important;
  padding-bottom: 10px;
  margin-bottom: 15px;
  border-bottom: 1px dashed var(--main-border-color);
  cursor: pointer;
  text-decoration: none !important;
  border-right: unset !important;
  font-size: 14px;
}

.login_drop_down_list a:last-child {
  margin: 0px;
}

.listing_bar {
    padding: 18px 15px;
    background: #000;
}

a.header_shop_btn {
  box-shadow: 0px 20px 35px 0px var(--primary-bg-shadow);
  background: var(--primary-bg-color);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: var(--primary-text-color) !important;
  border-radius: 5px;
  padding: 5px 29px;
}

@media(max-width:768px) {
  .login_drop_down {
    top: 18px;
    right: 0px;
    left: unset;
  }


}


@media (max-width:991px) {


  .header_logo {
    width: 100px;
  }

  a.header_trip_booking_btn {
    width: 130px;
    margin-left: 10px;
  }


  .header_links {
    position: fixed;
    width: 100%;
    height: 100vh;
    background: var(--primary-bg-color);
    left: 0px;
    top: 0px;
    z-index: 99;
    flex-direction: column;
    align-items: flex-start;
    transform: translatex(-100%);
    transition: 0.3s ease-in-out;
    overflow-y: auto;
  }

  .header_links.active {
    transform: translatex(0%);
  }

  .header_links_list {
    margin-right: 0px;
    margin-bottom: 40px;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    display: flex;
    padding: 0px 20px 20px 20px;
  }

  .header_links_list a {
         margin-right: 0px;
        margin-bottom: 20px;
        width: 100%;
        font-family: "Ancizar Serif", serif !important;
        font-size: 24px;
        font-weight: 600;
        line-height: 27px;
        color: #fff !important;
        border-bottom: 1px dashed;
        border-color: #ffffffb5;
        padding-bottom: 20px;
  }

  .header_links_list a:last-child {
    margin-bottom: 0px;
  }

  .cross_btn {
    display: flex;
    width: 100%;
    justify-content: flex-start;
    margin-bottom: 40px;
  }

  .cross_btn i {
    font-size: 24px;
    color: var(---main-text-color);
    background: #fff !important;
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

@media(max-width:768px) {


  a.header_trip_booking_btn {
    display: none !important;
  }

  .header_ticker {
    background: var(--secondary-bg-color);
  }

  .ticker_text span {
    padding: 8px 10px;
    color: var(--secondary-text-color);

  }

  .res_header_trip_booking_btn {
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    background-color: var(--active-bg-color);
    color: var(--active-bg-text-color) !important;
    display: flex !important;
    padding: 8px 0px;

  }

  .res_header_trip_booking_btn small {
    font-family: Poppins;
  }

  .header_social_link a:nth-child(2) {
    margin-right: 0px;
  }

  .ticker_text span {
    font-size: 12px;
    line-height: 18px;
  }
}

/* page title start */
.page_title_section {
  margin-bottom: 50px;
}

.page_title {
  max-width: 1024px;
  margin: 0 auto;
}

.page_title small {
  font-size: 18px;
  font-weight: 500;
  line-height: 34px;
  letter-spacing: 0.1em;
  color: var(--home-banner-text-color);
}

.page_title h2 {
  font-family: Poppins;
  font-size: 78px;
  font-weight: 800;
  line-height: 100px;
  letter-spacing: 0em;
  color: var(--home-banner-text-color);
}

@media(max-width:1024px) {
  .page_title h2 {
    font-size: 44px;
    line-height: 52px;
  }

  .page_title small {
    font-size: 16px;
  }
}

@media(max-width:768px) {

  .page_title_section img {
    height: 170px !important;
  }

  .page_title_section {
    margin-bottom: 30px;
  }

  .page_title h2 {
    font-size: 32px;
    line-height: 38px;
  }

  .page_title small {
    font-size: 13px;
    line-height: 25px;
  }
}

/* page title end */


/* offer section css start  */
.offer_listing_parent {
  background: var(--secondary-bg-color);
  padding-top: 30px !important;
  padding-bottom: 30px !important;
}

.offer_listing_heading {
  padding-bottom: 15px;
  max-width: 768px;
  margin: 0 auto;
}

.offer_listing_heading small {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: var(--active-color);
  text-transform: uppercase;
}

.offer_listing_heading h3 {
  font-size: 32px;
  font-weight: 700;
  line-height: 36px;
  color: var(--secondary-text-color);
  font-family: Poppins;
}

.offer_listing ul {
  margin: 0px -15px;
}

.offer_listing ul li {
  list-style: none;
  width: 25%;
  padding: 15px 15px 15px 15px;
}

.offer_single_list {
  border-radius: 36px;
  padding: 15px 15px;
  background-color: var(--main-bg-color);
  box-shadow: var(--primary-box-shadow) 0px 7px 29px 0px;
}

.offer_single_list figure {
  padding-bottom: 20px;

}
.offer_single_list figure img {
    aspect-ratio: 1 / 1;
    border-radius: 30px;
}

.offer_single_list h3 {
  font-family: Poppins;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  color: var(--secondary-text-color);
  padding-bottom: 12px;
}

.offer_single_list p {
  font-size: 14px;
  font-weight: 500;
  line-height: 23px;
  color: var(--custom-text-color-one);
}

@media(max-width:1200px) {
  .offer_listing ul li {
    width: 33.33%;
  }

  .offer_listing_heading h3 {
    font-size: 26px;
    line-height: 38px;
  }
}

@media(max-width:1024px) {
  .offer_listing ul {
    margin: 0px -8px;
  }

  .offer_listing ul li {
    width: 33.33%;
    padding: 8px 8px;
  }
}

@media (max-width: 768px) {
  .offer_listing ul li {
    width: 50%;
  }

  .offer_listing_heading h3 {
    font-size: 26px;
    line-height: 27px;
  }

  .offer_listing_heading {
    padding-bottom: 20px;
  }


  .offer_single_list {
    padding: 20px 15px;
  }
}

@media (max-width: 540px) {


  .offer_single_list h3 {
    font-size: 18px;
    line-height: 20px;
  }

  .offer_single_list p {
    font-size: 11px;
    line-height: 18px;
  }
}

/* offer section css end */

/* custom section 1 css start */
.custom_sec_1 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px 59px;
}

.custom_sec_1_body {
  max-width: 580px;
}

.custom_sec_1_content small {
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  color: var(--active-color);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding-bottom: 8px;
}

.custom_sec_1_content h3 {
  font-size: 48px;
  font-weight: 700;
  line-height: 48px;
  color: var(--secondary-text-color);
  font-family: Poppins;
  padding-bottom: 20px;
}

.custom_sec_1_content p {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: var(--custom-text-color-one);
  padding-bottom: 30px;
}

.custom_sec_1_btn a {
  font-family: Poppins;
  font-size: 18px;
  font-weight: 400;
  line-height: 25px;
  color: var(--primary-text-color);
  box-shadow: 8px 8px 0px 0px var(--primary-bg-shadow);
  background: var(--primary-bg-color);
  border-radius: 6px;
  text-transform: capitalize;
  transition: 0.2s ease-in-out;
  height: 46px;
  width: 176px;
  cursor: pointer;
}

@media(max-width:1200px) {
  .custom_sec_1 {
    gap: 20px 40px;
  }

  .custom_sec_1_content h3 {
    font-size: 36px;
    line-height: 38px;
  }

  .custom_sec_1_content small {
    font-size: 16px;
    line-height: 22px;
  }
}

@media(max-width:1024px) {
  .custom_sec_1 {
    display: flex;
    flex-direction: column-reverse;
    gap: 0px;
  }

  .custom_sec_1 figure {
    max-width: 450px;
    margin: 0 auto;
  }

  .custom_sec_1_body {
    max-width: 100%;
    width: 100%;
  }

  .custom_sec_1_content {
    text-align: center;
  }

  .custom_sec_1_btn {
    justify-content: center;
  }

  .custom_sec_1_body_main {
    padding-bottom: 30px;
  }
  .custom_sec_1_btn a  {
    height: 40px;
    width: 163px;
    font-size: 14px;
  
    line-height: 20px;
}
}

@media (max-width: 768px) {
  .custom_sec_1_btn a {
    width: 130px;

  }

  .custom_sec_1_content h3 {
    font-size: 26px;
    line-height: 28px;
    padding-bottom: 12px;
  }

  .custom_sec_1_content small {
    font-size: 14px;
    line-height: 22px;
  }

  .custom_sec_1_content p {
    font-size: 14px;
    line-height: 24px;
  }
}

/* custom section 1 css end */


/* custom section 2 css start */
.custom_sec_2_parent{
        background: var(--secondary-bg-color);
    padding-top: 30px !important;
    padding-bottom: 30px !important;
}
.custom_sec_2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px 59px;
}

.custom_sec_2_body {
  max-width: 480px;
}

.custom_sec_2_content small {
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  color: var(--active-color);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding-bottom: 8px;
}

.custom_sec_2_content h3 {
  font-size: 36px;
  font-weight: 700;
  line-height: 40px;
  color: var(--secondary-text-color);
  font-family: Poppins;
  padding-bottom: 20px;
}

.custom_sec_2_content ul {
  display: grid;
  gap: 40px;
}

.custom_sec_2_content ul li {
  list-style: none;
}

.custom_sec_2_content ul li figure {
  width: 70px;
  margin-right: 21px;
}

.custom_sec_2_content ul li figcaption h3 {
  font-family: Poppins;
  font-size: 22px;
  font-weight: 700;
  line-height: 34px;
  color: var(--secondary-text-color);
  padding-bottom: 4px;
}

.custom_sec_2_content ul li figcaption p {
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  color: var(--custom-text-color-one);
}

@media(max-width:1024px) {
  .custom_sec_2 {
    grid-template-columns: 1fr;
    margin-bottom: 30px;
  }

  .custom_sec_2_body_main {
    align-items: flex-start !important;
  }

  .custom_sec_2_body {
    max-width: 100%;
    width: 100%;
  }

  .custom_sec_2_content ul li figure {
    min-width: 45px;
  }
}

@media (max-width: 768px) {
  .custom_sec_2_content h3 {
    font-size: 26px;
    line-height: 28px;
  }

  .custom_sec_2_content small {
    font-size: 14px;
    line-height: 22px;
  }

  .custom_sec_2_content ul li figcaption p {
    font-size: 14px;
  }

  .custom_sec_2_content ul li figcaption h3 {
    font-size: 20px;
    line-height: 25px;
  }
}

/* custom section 2 css end */


/* custom section 4 css start */
.custom_sec_4 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px 59px;
}

.custom_sec_4 figure {
  max-width: 500px;
}

.custom_sec_4_body {
  max-width: 550px;
}

.custom_sec_4_content small {
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  color: var(--active-color);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding-bottom: 8px;
}

.custom_sec_4_content h3 {
  font-size: 38px;
  font-weight: 700;
  line-height: 50px;
  color: var(--secondary-text-color);
  font-family: Poppins;
  padding-bottom: 20px;
}

.custom_sec_4_content p {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: var(--custom-text-color-one);
  padding-bottom: 30px;
}

.custom_sec_4_btn a {
  font-family: Poppins;
  font-size: 18px;
  font-weight: 400;
  line-height: 25px;
  color: var(--primary-text-color);
  box-shadow: 8px 8px 0px 0px var(--primary-bg-shadow);
  background: var(--primary-bg-color);
  border-radius: 6px;
  text-transform: capitalize;
  transition: 0.2s ease-in-out;
  height: 46px;
  width: 176px;
  cursor: pointer;
}

@media(max-width:1200px) {
  .custom_sec_4_content h3 {
    font-size: 26px;
    line-height: 38px;
  }

  .custom_sec_4_content small {
    font-size: 16px;
    line-height: 22px;
  }
}

@media(max-width:1024px) {
  .custom_sec_4 {
    grid-template-columns: 1fr;
    gap: 35px;
  }

  .custom_sec_4_body_main {
    align-items: flex-start !important;
  }

  .custom_sec_4_body {
    max-width: 100%;
    width: 100%;
  }

  .custom_sec_4_content {
    text-align: center;
  }

  .custom_sec_4_btn {
    justify-content: center;
  }

  .custom_sec_4 figure {
    margin: 0 auto;
  }
  .custom_sec_4_btn a {
    height: 40px;
    width: 163px;
    font-size: 14px;
    line-height: 20px;
}
}

@media (max-width: 768px) {
  .custom_sec_4_content h3 {
    font-size: 26px;
    line-height: 33px;
  }

  .custom_sec_4_content small {
    font-size: 16px;
    line-height: 22px;
  }

  .custom_sec_4_content p {
    font-size: 14px;
    line-height: 24px;
  }

  .custom_sec_4_btn a {
    width: 130px;
   
  }

  .custom_sec_4_content small {
    font-size: 14px;
    line-height: 19px;
  }

  .custom_sec_4_content h3 {
    font-size: 26px;
    line-height: 27px;
  }
}

/* custom section 4 css end */


/* video section css start */
.video_section {
  margin-bottom: 60px;
}

.video_section video {
  height: 500px;
}

@media(max-width:1024px) {
  .video_section video {
    height: 360px;
  }

  .video_section {
    margin-bottom: 40px;
  }
}

@media(max-width:540px) {
  .video_section {
    margin-bottom: 30px;
  }
}

/* video section css end */

/* custom section 3 css start */
.custom_sec_3_parent {
  margin-bottom: 60px;
}

.custom_sec_3 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  width: 100%;
}

.custom_sec_3_content {
  background: #0000004d;
}

.custom_sec_3_content small {
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  letter-spacing: 0.1em;
  color: var(--custom-color-one);
}

.custom_sec_3_content h4 {
  font-family: Poppins;
  font-size: 50px;
  font-weight: 700;
  line-height: 71px;
  letter-spacing: 0em;
  color: var(--custom-color-one);
  padding-bottom: 23px;
}

.custom_sec_3_content a {
    font-family: Poppins;
    letter-spacing: 0em;
    color: var(--primary-text-color);
    background: var(--active-color);
    text-transform: capitalize;
    transition: 0.2s ease-in-out;
    height: 46px;
    width: 176px;
    font-size: 16px;
    font-weight: 500;
    line-height: 18px;
    border-radius: 5px;
    box-shadow: 2px 2px 0px 0px #fff;
}

@media(max-width:1200px) {
  .custom_sec_3_content h4 {
    font-size: 36px;
    line-height: 42px;
  }
}

@media(max-width:1024px) {
  .custom_sec_3 {
    grid-template-columns: 1fr;
  }

  .custom_sec_3_parent {
    margin-bottom: 40px;
  }
}

@media(max-width:768px) {
  .custom_sec_3_parent {
    margin-bottom: 30px;
  }

  .custom_sec_3_content h4 {
    font-size: 24px;
    line-height: 30px;
  }

  .custom_sec_3_content small {
    font-size: 14px;
    line-height: 22px;
  }

  .custom_sec_3_content a {
    font-size: 14px;
    line-height: 25px;
    width: 148px;
    height: 45px;
  }
}

@media(max-width:375px) {
  .custom_sec_3 figure {
    height: 205px !important;
  }
}

/* custom section 3 css end */



/* custom section 5 css start */
.custom_sec_5 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px 59px;
}

.custom_sec_5_body {
  max-width: 550px;
}

.custom_sec_5_content small {
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  color: var(--active-color);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding-bottom: 8px;
}

.custom_sec_5_content h3 {
  font-size: 48px;
  font-weight: 700;
  line-height: 60px;
  color: var(--secondary-text-color);
  font-family: Poppins;
  padding-bottom: 20px;
}

.custom_sec_5_content p {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: var(--custom-text-color-one);
  padding-bottom: 30px;
}

.custom_sec_5_btn a {
    font-family: Poppins;
    font-size: 16px;
    font-weight: 400;
    line-height: 25px;
    color: var(--primary-text-color);
    box-shadow: 8px 8px 0px 0px var(--primary-bg-shadow);
    background: var(--primary-bg-color);
    border-radius: 6px;
    text-transform: capitalize;
    transition: 0.2s ease-in-out;
    height: 46px;
    width: 176px;
    cursor: pointer;
}

@media(max-width:1200px) {
  .custom_sec_5_content h3 {
    font-size: 36px;
    line-height: 44px;
  }

  .custom_sec_5_content small {
    font-size: 16px;
    line-height: 22px;
  }
}

@media(max-width:1024px) {
  .custom_sec_5 {
    grid-template-columns: 1fr;
    gap: 35px;
  }

  .custom_sec_5_body_main {
    align-items: flex-start !important;
  }

  .custom_sec_5_body {
    max-width: 100%;
    width: 100%;
  }

  .custom_sec_5_content {
    text-align: center;
  }

  .custom_sec_5_btn {
    justify-content: center;
  }

  .custom_sec_5 picture {
    max-width: 540px;
    margin: 0 auto;
  }
  .custom_sec_5 picture img {
    max-width: 540px;
    width: 100%;
    height: 100%;
}
  .custom_sec_5_btn a {
        height: 40px;
        width: 163px;
        font-size: 14px;
        line-height: 20px;
      }
}

@media (max-width: 768px) {
  .custom_sec_5_btn a {

                width: 130px;
  }

  .custom_sec_5_content h3 {
    font-size: 26px;
    line-height: 33px;
  }

  .custom_sec_5_content small {
    font-size: 16px;
    line-height: 22px;
  }

  .custom_sec_5_content p {
    font-size: 14px;
    line-height: 24px;
  }
}

/* custom section 5 css end */



/* custom section 6 css start */
.custom_sec_6 {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 30px 59px;
}

.custom_sec_6_content small {
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  color: var(--active-color);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  padding-bottom: 8px;
}

.custom_sec_6_content h3 {
  font-size: 48px;
  font-weight: 700;
  line-height: 60px;
  color: var(--secondary-text-color);
  font-family: Poppins;
  padding-bottom: 20px;
}

.custom_sec_6_content p {
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: var(--custom-text-color-one);
  padding-bottom: 30px;
}

.custom_sec_6_btn a {
  width: 176px;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  color: var(--primary-text-color);
  background-color: var(--primary-bg-color);
  box-shadow: 0px 20px 35px 0px var(--primary-bg-shadow);
  border-radius: 10px;
}

@media(max-width:1200px) {
  .custom_sec_6 {
    gap: 20px 40px;
  }

  .custom_sec_6_content h3 {
    font-size: 36px;
    line-height: 44px;
  }

  .custom_sec_6_content small {
    font-size: 16px;
    line-height: 22px;
  }
}

@media(max-width:1024px) {
  .custom_sec_6 {
    display: flex;
    flex-direction: column-reverse;
    gap: 0px;
  }

  .custom_sec_6 figure {
    max-width: 540px;
    margin: 0 auto;
  }

  .custom_sec_6_content {
    text-align: center;
    padding-bottom: 30px;
  }

  .custom_sec_6_btn {
    justify-content: center;
  }

}

@media (max-width: 768px) {

  .custom_sec_6_content h3 {
    font-size: 26px;
    line-height: 33px;
  }

  .custom_sec_6_content small {
    font-size: 16px;
    line-height: 22px;
  }

  .custom_sec_6_content p {
    font-size: 14px;
    line-height: 24px;
  }
}

/* custom section 6 css end */

/* packages section css start  */

.packages_listing_heading {
  
  max-width: 768px;
  margin: 0 auto;
}

.packages_listing_heading small {
font-size: 15px;
    font-weight: 500;
    line-height: 18.9px;
    letter-spacing: 0.07em;
    text-align: center;
    text-transform: uppercase;
    padding-bottom: 4px;
  color: var(--active-color);
}

.packages_listing_heading h3 {
     font-size: 36px;
    font-weight: 700;
    line-height: 46px;
    color: var(--secondary-text-color);
    font-family: Poppins;
    padding-bottom: 10px;
}
.packages_listing_heading p{
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.07em;
    text-align: center;
    color: #000000;
    max-width: 600px;
    margin: 0 auto;
    
}

.packages_listing ul {
  margin: 0px -10px;
}

.packages_listing ul li {
  list-style: none;
  width: 33.33%;
  padding: 25px 10px 25px 10px;
}

.packages_single_list {
  border-radius: 10px;
  padding: 20px 20px;
  background-color: var(--main-bg-color);
  box-shadow: var(--primary-box-shadow) 0px 7px 29px 0px;
}

.packages_single_list figure {
  padding-bottom: 30px;
}

.package_location {
  font-size: 17px;
  font-weight: 400;
  line-height: 25px;
  letter-spacing: 0em;
  color: var(--custom-text-color-one);
  padding-bottom: 4px;
}

.package_location span {
  font-size: 17px;
  color: var(--custom-text-color-one);
  font-weight: 500;
}

.package_location i {
  font-size: 17px;
  color: var(--custom-text-color-one);
  padding-right: 8px;
}

.packages_single_list_content h3 {
  font-family: Poppins;
  font-size: 28px;
  font-weight: 700;
  line-height: 44px;
  color: var(--secondary-text-color);
  padding-bottom: 12px;
}

.packages_price {
  padding-bottom: 4px;
}

.packages_price h6 {
  font-family: Poppins;
  font-size: 24px;
  font-weight: 700;
  line-height: 44px;
  color: var(--active-color);
}

.single_package_rating {
  padding-bottom: 20px;
}

.single_package_rating i {
  padding-right: 4px;
  font-size: 20px;
  color: #E7581D;
}

.packages_price h5 {
  padding-left: 15px;
  font-family: Poppins;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
  text-decoration: line-through;
  color: var(--custom-text-color-one);
}

.packages_single_list_content div.short_des {
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
  color: var(--custom-text-color-one);
  margin-bottom: 30px;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  width: 100%;
  max-width: 100%;
  height: 100px;
  text-overflow: ellipsis;
  word-wrap: break-word;
  overflow: hidden;
}

.packages_single_list_content div.short_des * {
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
  color: var(--custom-text-color-one);
}

.single_package_btn a {
  width: 176px;
  height: 56px;
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  color: var(--primary-text-color);
  background-color: var(--primary-bg-color);
  box-shadow: 0px 20px 35px 0px var(--primary-bg-shadow);
  border-radius: 10px;
}

.tour_package_slider ul.slick-dots {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    margin: 0px !important;
    bottom: 15px;
    position: absolute;
}

.tour_package_slider ul.slick-dots li {
list-style: none;
    width: 30px !important;
    height: 1px !important;
    padding: 0px 7px;
}

.tour_package_slider .slick-dots li button {
    width: 100%;
    height: 100%;
    padding: 1px 10px;
    cursor: pointer;
    color: var(--home-banner-text-color);
    border: 0;
    outline: none;
    background: var(--home-banner-text-color) !important;
    border-radius: 1px !important;
    border: 1px solid var(--home-banner-text-color) !important;
    position: relative;
    text-indent: -9999px;
}

.tour_package_slider li.slick-active button {
  box-shadow: 0px 2px 5px 0px var(--primary-bg-shadow);
  background: var(--primary-bg-color) !important;
  border-color: var(--primary-bg-color) !important;

}

.tour_package_slider .slick-dots li button:before {
  display: none !important;
}

@media(max-width:1200px) {
  .packages_listing ul li {
    width: 33.33%;
  }
}

@media(max-width:1024px) {
  .packages_listing ul {
    margin: 0px -8px;
  }

  .packages_listing ul li {
    width: 50%;
    padding: 25px 8px;
  }

  .packages_single_list_content p {
    margin-bottom: 25px;
    -webkit-line-clamp: 2;
    height: 48px;
  }
}

@media (max-width: 768px) {


  .packages_listing_heading h3 {
    font-size: 26px;
    line-height: 32px;
  }
  .packages_listing_heading p {
    font-size: 12px;
    line-height: 19px;
    letter-spacing: 0px;
}

  .packages_listing_heading small {
    font-size: 14px;
    line-height: 22px;
  }

  .packages_single_list {
    padding: 20px 15px;
  }

  .packages_single_list_content h3 {
    font-size: 20px;
    line-height: 34px;
  }

  .single_package_rating i {
    font-size: 14px;
  }

  .single_package_rating {
    padding-bottom: 9px;
  }

  .package_location span {
    font-size: 14px;
  }

  .package_location i {
    font-size: 15px;
  }

  .packages_price h6 {
    font-size: 20px;
    line-height: 34px;
  }

  .packages_price h5 {
    font-size: 16px;
    line-height: 24px;
  }

  .single_package_btn a {
    width: 140px;
    height: 43px;
    font-size: 15px;
  }

  .packages_single_list figure {
    padding-bottom: 20px;
  }
}

@media (max-width: 540px) {}

/* packages section  css end */

/* featured packages section  css start */
.feature_package_parent {
  margin-bottom: 60px;
}

.feature_package_list li {
  list-style: none;
  width: 25%;
}

.feature_package_list .swiper-slide-active {
  padding: 10px 0px 25px 0px;
}

.featured_package_slider_dots {
  bottom: -6px !important;
}

.featured_package_slider_dots .swiper-pagination-bullet {
  background: var(--ternary-bg-color);
  width: 23px;
  height: 2px;
  border-radius: 2px;
}

.featured_package_slider_dots .swiper-pagination-bullet-active {
  background: var(--active-bg-color);
}

.featured_package_slider_arrow {
  background: var(--ternary-bg-color);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  opacity: 0.3;
}

.featured_package_slider_arrow i {
  color: var(--ternary-text-color);
  font-size: 14px;
  font-weight: 600;
}

.featured_package_slider_arrow::after {
  display: none;
}

@media(max-width:1200px) {
  .feature_package_list li {
    width: 40%;
  }
}

@media(max-width:1024px) {
  .feature_package_parent {
    margin-bottom: 40px;
  }

  .feature_package_list li {
    width: 50%;
  }
}

@media(max-width:768px) {
  .feature_package_list li {
    width: 70%;
  }

  .feature_package_parent {
    margin-bottom: 30px;
  }
}

@media(max-width:540px) {
  .feature_package_list li {
    width: 80%;
  }
}

/* featured packages section  css end */

/* review section css start  */
.review_section {
  background: var(--secondary-bg-color);
  margin-top: 30px;
}

.review_ab_2 img {
  max-width: 300px;
}

.review_ab_3 {
  max-width: 300px;
  right: 0px;
  top: -80px;
}

.review_ab_4 {
  max-width: 150px;
  left: 0px;
  top: -50px;
}

.review_listing ul li figure {
position: absolute;
    top: -21px;
}
.review_listing ul li figure img {
    background: #fff;
    width: 70px;
    border-radius: 50%;
    height: 70px;
    display: flex;
 
}

.review_content {
  padding: 60px 15px;
  position: relative;
}
.review_btn a {
  font-family: Poppins;
  font-size: 18px;
  font-weight: 400;
  line-height: 25px;
  color: var(--primary-text-color);
  box-shadow: 8px 8px 0px 0px var(--primary-bg-shadow);
  background: var(--primary-bg-color);
  border-radius: 6px;
  text-transform: capitalize;
  transition: 0.2s ease-in-out;
  height: 46px;
  width: 176px;
  cursor: pointer;
}

.review_heading {
  padding-bottom: 30px;
  max-width: 560px;
  margin: 0 auto;

}

.review_heading small {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: var(--active-color);
  text-transform: uppercase;
}

.review_heading h3 {
  font-size: 36px;
  font-weight: 700;
  line-height: 46px;
  color: var(--secondary-text-color);
  font-family: Poppins;
}

.review_listing {
  max-width: 600px;
  margin: 0 auto;
  padding: 45px 0px;
}

.review_listing ul {
  display: flex;
}

.review_listing ul li {
  list-style: none;
  padding: 0px 20px;
}

.review_single_list {
  border-radius: 10px;
  padding: 60px 25px 33px 25px;
  background-color: var(--main-bg-color);
}

.review_ab_1 {
  left: 0px;
  top: -40px;
  width: 100px;
}

.review_box p {
  font-size: 12px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0.05em;
  margin-bottom: 13px;
  color: var(--secondary-text-color);
}

.review_box h4 {
  font-family: Poppins;
  font-size: 16px;
  font-weight: 700;
  line-height: 26px;
  color: var(--secondary-text-color);
}

.review_slider_dots .swiper-pagination-bullet {
  background: var(--ternary-bg-color);
  width: 23px;
  height: 2px;
  border-radius: 2px;
}

.review_slider_dots .swiper-pagination-bullet-active {
  background: var(--active-bg-color);
}

.review_slider_arrow {
  background: var(--ternary-bg-color);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  opacity: 0.3;
}

.review_slider_arrow i {
  color: var(--ternary-text-color);
  font-size: 14px;
  font-weight: 600;
}

.review_slider_arrow::after {
  display: none;
}

@media (max-width: 1024px) {
  .review_ab_3 {
    max-width: 190px;
    top: -65px;
  }
  .review_btn a {
    height: 40px;
    width: 163px;
    font-size: 14px;
    line-height: 20px;
}
}

@media (max-width: 768px) {
  .review_heading h3 {
    font-size: 26px;
    line-height: 28px;
  }

  .review_heading small {
    font-size: 14px;
  }

  .review_heading {
    max-width: 350px;
  }

  .review_ab_3 {
    max-width: 130px;
  }

  .review_ab_4 {
    max-width: 110px;
    top: -34px;
  }

  .review_ab_2 img {
    max-width: 150px;
  }

  .review_ab_1 {
    width: 55px;
    top: -24px;
  }
  .review_btn a {
 
    width: 130px;

}
}

/* review section css end  */


/* gallery section css start  */

.gallery_heading {
  padding-bottom: 15px;
  max-width: 768px;
  margin: 0 auto;
}

.gallery_heading small {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: var(--active-color);
  text-transform: uppercase;
}

.gallery_heading h3 {
  font-size: 36px;
  font-weight: 700;
  line-height: 46px;
  color: var(--secondary-text-color);
  font-family: Poppins;
}

.gallery_list {
  margin: 0px -10px;
}

.single_gallery_list a img{
   aspect-ratio: 1 / 1;
}
.single_gallery_list {
  padding: 10px 10px;
  flex: 0 0 25%;
  max-width: 100%;
}

figcaption.single_gallery_content {
  padding: 10px 10px;
      background: #000000a3;
}

.single_gallery_content span {
    font-size: 16px;
    font-weight: 600;
    line-height: 25px;
    color: var(--custom-color-one);

}

.single_gallery_content small {
  font-size: 22px;
  font-weight: 600;
  line-height: 33px;
  color: var(--custom-color-one);
}

@media (max-width:1024px) {

  .single_gallery_list {
    flex: 0 0 33.333%;
  }
}

@media (max-width: 768px) {
  .gallery_list {
    margin: 0px -6px;
  }

  .single_gallery_list {
    flex: 0 0 50%;
    padding: 6px 6px;
  }

  .gallery_heading {
    padding-bottom: 20px;
  }

  .gallery_heading h3 {
          font-size: 20px;
        line-height: 28px;
  }
.gallery_heading small {
    font-size: 14px;
    line-height: 24px;
    
}
  .single_gallery_content {
    flex-direction: column;
    justify-content: flex-end !important;
    align-items: center !important;
  }

  .single_gallery_content span {
          font-size: 14px;
        line-height: 21px;
  }

  .single_gallery_content small {
    font-size: 16px;
    line-height: 26px;
  }
}

/* gallery section css end  */
.login_slider .carousel-item img {
  border-radius: 12px;
}

/* login,register form css start */
.login_section {
  padding: 0px 15px 0px 15px;
}

.login_section_parent {
  display: grid;
  width: 100%;
  height: 100%;
  grid-template-columns: 1fr 1.2fr;
  gap: 30px 90px;
  align-items: center;
}

.login_tabs_main {
  margin-bottom: 25px;
}

.login_section_box_heading {
  padding-bottom: 24px;
}

.login_section_box_heading h3 {
  font-size: 24px;
  font-weight: 700;
  line-height: 34px;
  color: var(--main-text-color);
  padding-bottom: 15px;
  font-family: Poppins;
}

.login_section_box_heading h4 {
  font-size: 24px;
  font-weight: 700;
  line-height: 34px;
  color: var(--main-text-color);
  font-family: Poppins;
}

.login_section_box_heading p {
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0em;
  color: var(--custom-text-color-one);
}

.login_tabs {
  background: var(--secondary-bg-color);
  border-radius: 8px;
  display: grid;
  align-items: center;
  margin-bottom: 20px;
  grid-template-columns: 1fr 1fr;
  width: 100%;
}

.login_tabs a {
  padding: 8px 0px;
  font-size: 16px;
  font-weight: 500;
  line-height: 29px;
  text-align: center;
  color: var(--secondary-text-color);
  text-decoration: unset;
  cursor: pointer;
}

.login_tabs a:hover {
  text-decoration: none;
  color: inherit;

}

.login_tabs a.active {
  background: var(--primary-bg-color);
  color: var(--primary-text-color);
  text-decoration: unset;
  font-weight: 600;
  border-radius: 8px;
}

.login_tab_content {
  display: inline-block;
  width: 100%;
}

.login_tab_data {
  display: flex;
  width: 100%;
}

.login_tab_data form {
  width: 100%;
}

.login_form_content_box {
  width: 100%;
  display: grid;
  gap: 28px 0px;
}

.login_form_field_box_parent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px 20px;
  width: 100%;
}

.login_form_field_box {
  display: grid;
  position: relative;
}

.login_single_field_box {
  display: grid;
  width: 100%;
  position: relative;
 gap: 10px;
}


.login_form_field_box label {
  position: absolute;
  left: 3px;
  bottom: -27px;
  color: red;
  font-size: 14px;
}

.login_single_field_box.textarea_field {
  height: unset;
}

.login_single_field_box > span {
  font-style: normal;
  font-weight: 600;
  font-size: 17px;
  line-height: 19px;
  letter-spacing: 0.03em;
  color: var(--secondary-text-color);
  font-family: "Ancizar Serif", serif !important;
  text-transform: capitalize;
}

.login_single_field_box input {
  height: 100%;
  padding: 20px 25px 20px 15px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: var(--secondary-text-color);
  width: 100%;
  background-color: var(--secondary-bg-color);
  border: unset;
  border-radius: 4px;
}

.login_single_field_box select {
  height: 100%;
  padding: 20px 25px 20px 15px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: var(--secondary-text-color);
  width: 100%;
  background-color: var(--secondary-bg-color);
  border: unset;
  border-radius: 4px;
}

.login_single_field_box textarea {
  height: 100%;
  padding: 20px 25px 20px 15px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: var(--secondary-text-color);
  width: 100%;
  background-color: var(--secondary-bg-color);
  border: unset;
  border-radius: 4px;
}

.login_single_field_box i {
  position: absolute;
  bottom: 19px;
  align-items: center;
  right: 16px;
  color: var(--secondary-text-color);
  cursor: pointer;
}

.login_form_signin_btn button {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--primary-text-color);
  background: var(--primary-bg-color);
  border-radius: 4px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  border: unset;
}

.login_form_signin_btn input {
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--primary-text-color);
  background: var(--primary-bg-color);
  border-radius: 4px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  border: unset;
}

/* drag drop file input css start */
.drag_and_drop_box {
  height: 100%;
  padding: 20px 15px 20px 15px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: var(--secondary-text-color);
  width: 100%;
  background: transparent;
  border-radius: 10px;
  border: 2px dashed var(--primary-bg-color);
  position: relative;
  cursor: pointer;
  transition: background .2s ease-in-out, border .2s ease-in-out;
  display: grid;
  gap: 10px;
}

.drag_and_drop_box small {
  transition: color .2s ease-in-out;
  font-style: normal;
  font-weight: 500;
  font-size: 15px;
  line-height: 24px;
  letter-spacing: 0.03em;
  color: var(--secondary-text-color);
}

.drag_and_drop_box span {
  font-style: normal;
  font-weight: 600;
  font-size: 17px;
  line-height: 22px;
  letter-spacing: 0.03em;
  color: var(--secondary-text-color);
}

.drag_and_drop_box input[type=file] {
  width: 100%;
  max-width: 100%;
  color: var(--secondary-text-color);
  padding: 5px;
  background-color: var(--secondary-bg-color);
  border: none;
  border-radius: 6px;
}

.drag_and_drop_box input[type=file]::file-selector-button {
  margin-right: 20px;
  border: none;
  background: var(--secondary-bg-color);
  padding: 10px 20px;
  border-radius: 10px;
  color: #000;
  cursor: pointer;
  transition: background .2s ease-in-out;
}

.drag_and_drop_box:hover input[type=file] {
  background-color: var(--secondary-bg-color);
  transition: background .2s ease-in-out;
}

.drag_and_drop_box:hover input[type=file]::file-selector-button {
  background-color: var(--secondary-bg-color);
  transition: background .2s ease-in-out;
}

.drag_and_drop_box label {
  color: var(--failed-bg-color);
  font-size: 14px;
}

/* drag drop file input css end */

@media (max-width: 1024px) {
  .login_section_parent {
    gap: 0px;
    display: flex;
    flex-direction: column-reverse;
  }

  .login_form_field_box_parent {
    grid-template-columns: 1fr;
  }

  .login_section_parent figure {
    margin-top: 40px;
  }

  .login_slider {
    padding-top: 30px;
  }
}

/* login,register css end */

/* forgot password popup css start */
.forget_password_btn a {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 17px;
  text-align: center;
  color: var(--primary-text-color);
  cursor: pointer;
  text-decoration: underline;
}

.forget_password_container {
  max-width: 560px;
  margin: 20px auto;
  padding: 0px 20px;
}

.forget_password_popup {
  background-color: var(--main-bg-color);
  box-shadow: var(--primary-box-shadow) 0px 7px 29px 0px;
  border-radius: 4px;
  padding: 32px 15px 38px 15px;
  pointer-events: auto;
}

.forget_password_popup_des {
  padding-bottom: 25px;
}

.forget_password_popup_des h5 {
  font-style: normal;
  font-weight: 700;
  font-size: 21px;
  line-height: 25px;
  color: var(--secondary-text-color);
  margin: 0px;
  font-family: Poppins;
}

.forget_password_popup_btn {
  display: grid;
  align-items: center;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  width: 100%;
}

.forget_password_popup_btn a {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--primary-text-color) !important;
  background: var(--primary-bg-color);
  height: 44px;
  cursor: pointer;
}

.forget_password_popup_btn input {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--primary-text-color);
  background: var(--primary-bg-color);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 44px;
  cursor: pointer;
  border: unset;

}

/* forgot password popup css end */

/* contact page css  start*/

.contact_list_heading {
  display: flex;
  width: 100%;
  padding-bottom: 20px;
}

.contact_list_heading h3 {
  font-size: 34px;
  font-weight: bold;
  border-bottom: 2px dashed #000;
  padding-bottom: 6px;
}

.contact_section_detail_parent {
  background: var(--secondary-bg-color);
  border-radius: 0px 4px 4px 0px;
  padding: 50px 45px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.contact_section_details {
  display: grid;
  width: 100%;
  grid-template-columns: auto;
  gap: 30px 0px;
  padding-bottom: 40px;
}

.contact_section_single_list {
  width: 100%;
  display: flex;
  align-items: center;
}

.contact_section_single_list i {
  padding-right: 10px;
  font-size: 16px;
  color: var(--secondary-text-color);
}

.contact_section_single_list small {
  font-size: 14px;
  font-weight: normal;
  margin: 0px;
  color: var(--secondary-text-color);
  text-transform: capitalize;
  line-height: 18px;
}

.contact_section_single_list a {
  font-size: 14px;
  font-weight: normal;
  margin: 0px;
  color: var(--secondary-text-color);
  line-height: 18px;
  text-decoration: none;
}

.contact_section_socail_link {

  display: flex;
  align-content: center;
  width: 100%;
}

.contact_section_socail_link a {
  color: var(--primary-text-color) !important;
  margin-right: 20px;
  cursor: pointer;
  padding: 8px 12px 5px 12px;
  background: var(--primary-bg-color);
  border-radius: 8px;
}

.contact_section_socail_link a i {
  color: var(--primary-text-color);
  text-decoration: none;
  cursor: pointer;
  font-size: 16px;
}

.contact_section_parent {
  display: grid;
  width: 100%;
  height: 100%;
  grid-template-columns: 1.8fr 1fr;
  gap: 30px 60px;
  align-items: center;
}

@media (max-width: 1024px) {
  .contact_section_parent {
    gap: 0px;
    display: flex;
    flex-direction: column-reverse;
  }
.contact_section_detail_parent {
    padding: 30px 20px;
    margin-bottom: 20px;
            width: 100%;

}
.contact_section_socail_link a {
    margin-right: 10px;
    
}
}

/*  contact page css  end  */

/* agent plan section css start */
.agent_plan_heading {
  max-width: 540px;
  margin: 0 auto;
  padding-bottom: 30px;
}

.agent_plan_heading small {
  font-size: 20px;
  font-weight: 600;
  line-height: 24px;
  color: var(--active-color);
  border-bottom: 1px solid var(--main-border-color);
  margin-bottom: 10px;
}

.agent_plan_heading h3 {
  font-size: 36px;
  font-weight: 700;
  line-height: 46px;
  color: var(--main-text-color);
  padding-bottom: 20px;
  font-family: 'Poppins', sans-serif;
}

.agent_plan_heading p {
  font-size: 16px;
  font-weight: 500;
  line-height: 19px;
  color: var(--custom-text-color-one);
}

.agent_plan_list_box {
  margin: 0 -10px;
}

.single_plan_box {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 15px 10px;
}

.bg_box {
  background: var(--main-bg-color);
  box-shadow: var(--primary-box-shadow) 0px 7px 29px 0px;
  border-radius: 16px;
  padding: 20px 15px 20px 15px;
}

.single_plan_box_data_header {
  margin-bottom: 25px;
}

.single_plan_box_data_header h3 {
  font-style: normal;
  font-weight: 600;
  font-size: 22px;
  line-height: 40px;
  color: var(--main-text-color);
  padding-bottom: 8px;
  text-transform: capitalize;
  font-family: 'Poppins', sans-serif;
}

.single_plan_box_data_header span {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 18px;
  letter-spacing: 0.08em;
  color: var(--main-text-color);
}

.single_plan_features_list {
  padding: 0px 0px 30px 0px;
  display: grid;
  gap: 17px;
}

.single_plan_features_list li {
  display: flex;
  padding-bottom: 17px;
  list-style: none;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--main-border-color);
}

.single_plan_features_list li:last-child {
  padding-bottom: 0px;
  border-bottom: unset;
}

.single_plan_features_list li i {
  padding-left: 7px;
  color: var(--main-text-color);
  font-size: 13px;
}

.single_plan_features_list li small {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  color: var(--main-text-color);
}

.single_plan_btn {
  margin-top: auto;
}

.single_plan_btn a {
  height: 48px;
  border-radius: 5px;
  font-style: normal;
  font-weight: 700;
  font-size: 14px;
  line-height: 18px;
  background-color: var(--ternary-bg-color);
  color: var(--ternary-text-color);
}

.single_plan_btn a:hover {
  color: var(--ternary-text-color);
}

@media (max-width: 1200px) {
  .single_plan_box {
    flex: 0 0 33.33%;
    max-width: 33.33%;
  }
}

@media (max-width: 1024px) {
  .agent_plan_list_box {
    margin: 0 -8px;
  }

  .single_plan_box {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 10px 8px;
  }
}

@media (max-width: 768px) {
  .agent_plan_heading small {
    font-size: 18px;
  }

  .agent_plan_heading h3 {
    font-size: 26px;
    line-height: 33px;
  }

  .agent_plan_heading p {
    font-size: 14px;
  }

  .single_plan_box {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* agent_plan section css end */

/* custom section 7 css start  */

.custom_sec_7_box {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px 50px;
}

.custom_sec_7_content h3 {
    font-style: normal;
    font-weight: 700;
    font-size: 36px;
    line-height: 40px;
    color: var(--main-text-color);
    padding-bottom: 12px;
    margin: 0px;
  font-family: Poppins;
}

.custom_sec_7_content p {
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 24px;
  color: var(--main-text-color);
  padding-bottom: 15px;
}

.custom_sec_7_content h5 {
    font-size: 28px;
    font-weight: 600;
    line-height: 36px;
    letter-spacing: 0px;
    padding-bottom: 10px;
    color: var(--main-text-color);
  font-family: Poppins;
}

.custom_sec_7_content ul {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px 15px;
  align-items: center;
}

.custom_sec_7_content ul li {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;
  color: var(--main-text-color);
}

.custom_sec_7_content ul li i {
  font-size: 16px;
  margin-right: 9px;
  line-height: 24px;
}

@media (max-width: 1024px) {
  .custom_sec_7_box {
    display: flex;
    flex-direction: column-reverse;
    gap: 0px;
  }

  .custom_sec_7_content {
    padding-bottom: 40px;
  }
}

@media (max-width:768px) {
  .custom_sec_7_content ul {
    grid-template-columns: 1fr;
  }
}

/* custom section 7 css end  */

/* custom section 8 css start  */

.custom_sec_8_box {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px 50px;
  align-items: center;
  width: 100%;
  height: 100%;
}

.custom_sec_8_content h3 {
font-style: normal;
    font-weight: 700;
    font-size: 36px;
    line-height: 41px;
    color: var(--main-text-color);
    padding-bottom: 12px;
    margin: 0px;
  font-family: Poppins;
}

.custom_sec_8_content p {
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: var(--main-text-color);
  padding-bottom: 15px;
}

.custom_sec_8_content h5 {
    font-size: 28px;
    font-weight: 600;
    line-height: 36px;
    letter-spacing: 0px;
    padding-bottom: 10px;
    color: var(--main-text-color);
  font-family: Poppins;
}

.custom_sec_8_content ul {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px 15px;
  align-items: center;
}

.custom_sec_8_content ul li {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: 0px;
  color: var(--main-text-color);
}

.custom_sec_8_content ul li i {
  font-size: 16px;
  margin-right: 9px;
  line-height: 24px;
}

@media (max-width: 1024px) {
  .custom_sec_8_box {
    grid-template-columns: 1fr;
  }
}

@media (max-width:768px) {
  .custom_sec_8_content ul {
    grid-template-columns: 1fr;
  }
  .custom_sec_7_content h3 {
    font-size: 26px;
    line-height: 30px;
  }
  .custom_sec_7_content h5 {
    font-size: 26px;
    line-height: 28px;
  }
.custom_sec_8_content h3 {
   font-size: 26px;
    line-height: 30px;
}
.custom_sec_8_content h5 {
    font-size: 26px;
    line-height: 32px;
  }
}

/* custom section 8 css end  */

/* packages shop page style start */
.packages_shop_section {
  margin: 0px -20px;
}

.packages_shop_filter_list {
  padding: 0px 20px;
  flex: 0 0 30%;
}

.packages_shop_box {
  padding: 0px 20px;
  flex: 0 0 70%;
}

.packages_shop_list {
  margin: 0px -8px;
}

.packages_shop_list li {
  padding: 10px 8px;
  flex: 0 0 50%;
  list-style: none;
}

/* packages shop page style end */



/* blog page css start */
.single_article_parent {
  padding: 0px 10px 60px 10px;
}

.blog_listing {
  margin: 0px -10px;
  padding: 0px;
}

.single_blog_box_main {
  flex: 0 0 33.33%;
  width: 33.33%;
  padding: 10px 10px 10px 10px;
  max-width: 100%;
}

.single_blog_box_main:hover {
  text-decoration: none;
}

.single_blog_box_figure img {

  aspect-ratio: 1/1;
}

.single_blog_box {
  padding: 16px 5px 0px 5px;
}

.single_blog_box_des_tags {
  padding-bottom: 15px;
}

.single_blog_box_des_tags span {
  font-style: normal;
  font-weight: 500;
  font-size: 13px;
  line-height: 12px;
  border-right: 1px solid var(--custom-text-color-one);
  color: var(--main-text-color);
  padding-right: 8px;
  margin-right: 8px;
}


.single_blog_box_des_tags small {
  font-style: normal;
  font-weight: 500;
  font-size: 13px;
  line-height: 12px;
  color: var(--main-text-color);
}

.single_blog_box h3 {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: var(--main-text-color);
  margin-bottom: 9px;
  display: -webkit-box !important;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 44px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
}

.single_blog_box p {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: var(--custom-text-color-one);
  margin-bottom: 14px;
  width: 100%;
  word-break: break-all;
  display: -webkit-box !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  height: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.single_blog_box strong {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 26px;
  text-decoration-line: underline;
  color: var(--active-color);
}





.single_article_box {
  display: grid;
  grid-template-columns: 70% 30%;
  margin: 0px -10px;
}

.single_article_data {
  padding: 0px 10px;
}

.single_article_sidebar {
  padding: 0px 10px;
  width: 100%;
}

.single_article_data {
  display: flex;
  flex-direction: column;
}

.single_article_bread_crums {
  margin-bottom: 10px;
}

.single_article_bread_crums a {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  text-transform: capitalize;
  color: var(--custom-text-color-one);
  text-decoration: none;
  padding: 5px 5px;
}

.single_article_bread_crums a:hover {
  color: var(--main-text-color);
  text-decoration: none;
}

.single_article_bread_crums i {
  font-size: 14px;
  color: var(--custom-text-color-one);
  padding: 5px 5px;
}

.single_article_tags {
  margin-bottom: 20px;
}

.single_article_tags_heading {
  display: flex;
  background: var(--ternary-bg-color);
  border-radius: 2px;
  color: var(--ternary-text-color);
  padding: 4px 8px;
  font-size: 11px;
  font-weight: bold;
  margin-right: 10px;
  margin-bottom: 10px;
}

.single_article_tags span {
  margin-right: 10px;
  background: var(--secondary-bg-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  line-height: 18px;
  text-transform: capitalize;
  margin-bottom: 10px;
}

.single_article_category {
  margin-bottom: 20px;
}

.single_article_category span {
  margin-right: 10px;
  background: var(--secondary-bg-color);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  line-height: 18px;
  text-transform: capitalize;
  margin-bottom: 10px;
  color: var(--secondary-text-color);
}

.single_article_heading {
  margin-bottom: 30px;
}


.single_article_heading h3 {
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: var(--main-text-color);
  margin: 0px;
}

.single_article_short_detail {
  margin-bottom: 20px;
}


.single_article_short_detial_left figure {
  margin-right: 14px;
  margin-bottom: 0px;
}

.single_article_short_detial_left figure img {
  width: 40px;
  height: 40px;
  display: flex;
  border-radius: 50%;
  margin-right: 15px;
  background: var(--secondary-bg-color);
  padding: 5px;
}

.single_article_short_detial_left figure span {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: var(--custom-text-color-one);
}

.single_article_short_detial_left small {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: var(--custom-text-color-one);
  white-space: nowrap;
}


.single_article_short_detial_right i {
  font-size: 14px;
  color: var(--custom-text-color-one);
  margin-right: 10px;
}

.single_article_short_detial_right small {
  font-style: normal;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  color: var(--main-text-color);
}

.article_share_button {
  margin-bottom: 20px;
}

.article_share_button a {
  margin-right: 10px;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 8px;
  text-decoration: none;
}

.article_share_button a:hover {
  text-decoration: none;
}

.article_share_button a.fb {
  background: #425893;
}

.article_share_button a.insta {
  background: #e95950;
}

.article_share_button a.twitter {
  background: #4D9EEA;
}

.article_share_button a.whatsapp {
  background: #65D072;
}

.article_share_button a:last-child {
  margin-right: 0px;
}

.article_share_button a i {
  font-size: 16px;
  color: #fff;
}

.single_article_main_img {
  margin-bottom: 20px;
}

.single_article_main_img figure img {
  object-fit: cover;
}

.single_article_main_detail {
  margin-bottom: 30px;
}

/* article comment form css start */


.article_comment_heading {
  margin-bottom: 25px;
}

.article_comment_heading h4 {
  font-size: 20px;
  color: var(--main-text-color);
  text-transform: capitalize;
  font-weight: 600;
}

.blog_section_one {
  padding: 20px 15px 20px 15px;
  background-color: var(--secondary-bg-color);
  border-radius: 4px;
}

.blog_page_form_content {
  display: grid;
  width: 100%;
  grid-template-columns: auto;
  grid-template-rows: auto;
}

.blog_form_fields {
  display: grid;
  width: 100%;
  grid-template-columns: auto;
  grid-template-rows: auto;
  gap: 7px 0px;
  padding-bottom: 10px;
}

.blog_form_fields span {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 26px;
  color: var(--main-text-color);
}

.blog_form_fields input {
  background: var(--footer-bg-color);
  border: 1px solid var(--footer-bg-color);
  border-radius: 4px;
  height: 52px;
  padding: 0px 15px 0px 15px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 26px;
  color: var(--footer-text-color);
  width: 100%;

}

.blog_form_fields label {
  color: red;
  font-size: 14px;

}

.blog_form_fields textarea {
  background: var(--footer-bg-color);
  border: 1px solid var(--footer-bg-color);
  border-radius: 4px;
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 26px;
  color: var(--footer-text-color);
  width: 100%;
  padding: 13px;
}

.blog_form_submit_btn {
  padding-top: 20px;
}

.blog_form_submit_btn button {
  border: 1px solid var(--primary-bg-color);
  background: var(--primary-bg-color);
  border-radius: 4px;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 21px;
  color: var(--primary-text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 250px;
  height: 56px;
  cursor: pointer;
}

/* article comment form css end */


/* article comment list css start */
.single_article_comments_main {
  margin-bottom: 40px;
}

.single_article_comments_list {
  border: 1px solid var(--custom-text-color-one);
  border-radius: 2px;
  padding: 20px;
}

.single_article_comment {
  margin-bottom: 20px;
}

.single_article_comment:last-child {
  margin-bottom: 0px;
}

.single_article_comment figure {
  margin-bottom: 12px;
}

.single_article_comment figure img {
  width: 36px;
  border-radius: 50%;
  height: 36px;
  object-fit: cover;
  background: var(--secondary-bg-color);
}

.single_article_comment figure figcaption {
  margin-left: 11px;
}

.single_article_comment figure figcaption small {
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  padding-bottom: 2px;
  line-height: 22px;
  color: var(--main-text-color);
}

.single_article_comment figure figcaption span {
  color: var(--custom-text-color-one);
  font-size: 14px;
  line-height: 16px;
  font-weight: normal;
}

.single_article_comment p {
  font-size: 14px;
  color: var(--main-text-color);
  line-height: 25px;
  font-weight: normal;
  margin: 0px;
}

/* article comment list css end */

/* popular article css start */


.popular_article_heading {
  border-bottom: 1px solid var(--custom-text-color-one);
  margin-bottom: 20px;
  padding-bottom: 5px;
}

.popular_article_heading strong {
  font-size: 17px;
  color: var(--active-bg-text-color);
  padding: 10px 14px;
  background-color: var(--active-bg-color);
  font-weight: 600;
  border-radius: 4px;
  text-transform: uppercase;
}

.popular_article_list {
  display: grid;
  width: 100%;
  gap: 20px;
}

.popular_single_article {
  display: grid;
  width: 100%;
  gap: 20px;
  grid-template-columns: 1fr 2fr;
  text-decoration: none;
}

.popular_single_article:hover {
  text-decoration: none;
}

.popular_single_article_details h3 {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: var(--main-text-color);
  margin-bottom: 9px;
  word-break: break-all;
}


.popular_single_article_details strong {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 26px;
  text-decoration-line: underline;
  color: var(--active-color);
}

.popular_single_article_inner_details {
  margin-bottom: 10px;
}

.popular_single_article_inner_details span {
  font-style: normal;
  font-weight: 400;
  font-size: 13px;
  line-height: 12px;
  border-right: 1px solid var(--custom-text-color-one);
  color: var(--main-text-color);
  padding-right: 6px;
  margin-right: 6px;
}


.popular_single_article_inner_details small {
  font-style: normal;
  font-weight: 400;
  font-size: 11px;
  line-height: 12px;
  color: var(--main-text-color);
}

/* popular article css end */
.related_blog_box {
  padding: 40px 15px;
  background: var(--secondary-bg-color);
}

.related_blog_header {
  padding-bottom: 32px;
}

.related_blog_header h4 {
  font-size: 32px;
  line-height: 48px;
  font-weight: 600;
  text-transform: capitalize;
}

.related_blog_list {
  margin: 0px -10px;
  padding: 0px;
  -webkit-mask-image: linear-gradient(to left, transparent, black 20%, black 100%, transparent 100%);
}

.single_related_blog {
  padding: 0px 10px 0px 10px;
}

@media(max-width:1024px) {
  .single_article_box {
    grid-template-columns: 100%;
    margin: 0px 0px;
  }

  .single_article_data {
    padding: 0px 0px;
    margin-bottom: 30px;
  }

  .single_article_sidebar {
    padding: 0px 0px;
  }

  .popular_article_list {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px 16px;
  }

  .popular_single_article figure img {
    height: 150px;
    object-fit: cover;
  }

  .popular_single_article {
    gap: 15px;
    grid-template-columns: 1fr;
  }

  .popular_single_article_details h3 {
    display: -webkit-box !important;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    height: 42px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }
}

@media(max-width:768px) {
  .single_article_category {
    margin-bottom: 10px;
  }

  .popular_article_list {
    grid-template-columns: 1fr 1fr;
  }

  .blog_listing {
    margin: 0px -8px;
  }

  .single_blog_box_main {
    flex: 0 0 50%;
    width: 50%;
    padding: 8px 8px 8px 8px;
  }

  .article_comment_heading h4 {
    font-size: 16px;
  }

  .blog_form_submit_btn button {
    font-size: 14px;
    width: 156px;
    height: 40px;

  }

  .popular_article_heading strong {
    font-size: 14px;
  }

  .single_article_parent {
    padding: 0px 10px 40px 10px;
  }

  .related_blog_header h4 {
    font-size: 24px;
    line-height: 26px;
  }
}

@media(max-width:540px) {
  .single_blog_box_des_tags {
    flex-direction: column;
    border-bottom: 1px solid var(--custom-text-color-one);
    margin-bottom: 8px;
  }

  .single_blog_box_des_tags span {
    border-right: unset;
    padding-right: 0px;
    margin-right: 0px;
    padding-bottom: 6px;
  }

  .single_blog_box h3 {
    text-align: center;
  }

  .single_blog_box p {
    text-align: center;
  }

  .single_blog_box strong {
    text-align: center;
  }

  .popular_single_article {
    text-align: center;
  }

  .popular_single_article_inner_details {
    flex-direction: column;
    border-bottom: 1px solid var(--custom-text-color-one);
    padding-bottom: 8px;
    width: 100%;
    margin-bottom: 8px;
  }

  .popular_single_article_inner_details span {
    border-right: unset;
    padding-bottom: 5px;
    margin: 0px;
    padding-right: 0px;
  }

  .popular_single_article_details h3 {
    text-align: center;
  }
}

/* blog page css end */

/* pagination style start */

ul.pagination {
  padding: 15px 0px;
}

ul.pagination li.page-item a.page-link {
  background: var(--main-bg-color);
  color: var(---main-text-color);
}

ul.pagination li.page-item.active span.page-link {
  background: var(--active-bg-color);
  border-color: var(--active-bg-color);
  color: var(--active-bg-text-color);
}

/* pagination style end  */


/* newsletter css start */
.newsletter_section {
  z-index: 99;
  position: relative;
  padding:30px 0px 0px 0px;
}

.newsletter_parent_box {
  background: var(--secondary-bg-color);
  border-radius: 12px;
  display: grid;
  gap: 20px 90px;
  /* align-items: end; */
  /* grid-template-columns: 2fr 1fr; */
  padding: 30px 25px 0px 25px;
}

/* .newsletter_content_box {
  padding: 0px 0px 30px 0px;
} */

.newsletter_content_box h3 {
  font-size: 44px;
  font-weight: 700;
  line-height: 54px;
  letter-spacing: 0em;
  color: var(--footer-text-color);
  padding-bottom: 24px;
  font-family: Poppins;
}

.newsletter_content_box p {
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0em;
  color: var(--footer-text-color);
  padding-bottom: 16px;
}

 .newsletter_content_box form {
  /* padding-top: 57px; */

    display: grid;
    width: 100%;
    gap: 20px;
    grid-template-columns: 70% 300px;
} 

.newsletter_content_box form .g-recaptcha {
    transform: scale(0.75);
  transform-origin: 0 0;
  /* position: absolute;
  top: 0px; */
  z-index: 999;
      max-width: 200px;
    width: 200px;
}

.newsletter_form_field input {
  border: 1px solid var(--footer-bg-color);
  box-shadow: 0px 0px 24px var(--footer-bg-shadow);
  box-sizing: border-box;
  border-radius: 4px;
  display: flex;
  align-items: center;
  height: 56px;
  width: 100%;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 20px;
  color: var(--footer-text-color);
  padding: 0px 160px 0px 20px;
  background-color: var(var(--footer-bg-color));
}

.newsletter_form_field input:focus-visible {
  outline: none !important;
}

.newsletter_form_field button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  width: 153px;
  background: var(--active-bg-color);
  box-shadow: 0px 0px 24px var(--footer-bg-shadow);
  border-radius: 4px;
  font-style: normal;
  font-weight: 600;
  font-size: 15px;
  line-height: 19px;
  text-transform: capitalize;
  color: var(--active-bg-text-color);
  position: absolute;
  content: "";
  top: 0px;
  right: 0px;
  border: unset !important;
}

.newsletter_form_field button i {
  display: none;
  font-size: 15px;
  line-height: 19px;
  color: var(--active-bg-text-color);
}

.newsletter_form_field label.error {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: var(--failed-bg-color);
  margin: 0px !important;
  position: absolute;
  bottom: -30px;
  left: 10px;
}

.newsletter_form_field button:hover {
  color: var(--active-bg-text-color);
}

.newsletter_form_field button:focus {
  outline: 0;
  box-shadow: unset !important;
}

@media (max-width:1024px) {
  .newsletter_content_box h3 {
    font-size: 26px;
    line-height: 28px;
    padding-bottom: 15px;
  }

  .newsletter_content_box p {
    font-size: 14px;
    font-weight: 400;
    padding-bottom: 14px;
  }
  .newsletter_content_box form {
    gap: 12px;
    grid-template-columns: 1fr;
}
.newsletter_content_box form .g-recaptcha {
    transform: scale(0.6);
    max-width: 200px;
    width: 200px;
}

}

@media (max-width:768px) {
  .newsletter_parent_box figure {
    display: none;
  }

  .newsletter_parent_box {
    grid-template-columns: 1fr;
  }
  .newsletter_form_field button,.newsletter_form_field input {
  height: 40px;
}
}

@media (max-width:540px) {
  .newsletter_form_field button {
    width: 95px;
    font-size: 12px;
  }

  .newsletter_form_field input {
    font-size: 14px;
    line-height: 18px;
    padding: 0px 100px 0px 20px;
  }

  .newsletter_content_box h3 {
    font-size: 26px;
    line-height: 28px;
    padding-bottom: 12px;
  }

  .newsletter_content_box p {
    font-size: 12px;
    line-height: 18px;
  }

  .newsletter_parent_box {
    padding: 25px 20px 0px 20px;
  }

  /* .newsletter_content_box {
    padding: 0px 0px 25px 0px;
  } */
}

/* newsletter css end */

/* shop page content body css  start */
.sop_page_parent {
  padding: 0px 15px;
}

.shop_content_body {
  margin: 0px -15px;
}

.filter_parent {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0px 15px;
}

.product_list_parent {
  flex: 0 0 75%;
  max-width: 75%;
  padding: 0px 15px;
}

/* filter css  */
.filters_heading {
  background: var(--primary-bg-color);
  border-radius: 10px;
  padding: 14px 10px;
  margin-bottom: 29px;
}

.filters_heading span {
  font-size: 14px;
  font-weight: 600;
  line-height: 16.8px;
  color: var(--primary-text-color);
}

.filters_heading span i {
  font-size: 18px;
  line-height: 16.8px;
  padding-left: 12px;
  color: var(--primary-text-color);
}

/* filter accordian design 
.filters_accordion_parent {
  gap: 24px;
}
*/
.single_filter_section {
  gap: 20px;
  border-bottom: 1px solid #e5e5e5;
  padding-bottom: 24px;
      margin-bottom: 20px;
}

.filter_header .accordion-button:not(.collapsed) i {
  transform: rotate(-180deg);
  transition: transform 0.4s ease;
}

.filter_header .accordion-button::after {
  display: none;
}

.filter_header_btn {
  padding: 0px;
  background: transparent !important;
  border: unset !important;
  box-shadow: unset !important;
}

.filter_header_btn h5 {
  font-size: 20px;
  font-weight: 500;
  line-height: 26.66px;
  padding-right: 15px;
}

.filter_header_btn i {
  position: absolute;
  right: 0px;
  top: 7px;
}

.filters_listing {
  gap: 20px;
}

/* custom checkbox style for filter  */

.filter_checkbox input {
  padding: 0;
  height: initial;
  width: initial;
  margin-bottom: 0;
  display: none;
  cursor: pointer;
}

.filter_checkbox label {
  position: relative;
  cursor: pointer;
  text-align: center;
  height: 20px;
  width: 20px;
}

.filter_checkbox label:before {
  content: "";
  -webkit-appearance: none;
  background-color: transparent;
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  border-radius: 3px;
  border: 1px solid #cccccc;
  background: #fff;
}

.filter_checkbox input:checked+label:before {
  background-color: var(--primary-bg-color);
  border-color: var(--primary-bg-color);
}

.filter_checkbox input:checked+label:after {
  content: "";
  display: block;
  position: absolute;
  top: 3px;
  left: 7.5px;
  width: 5px;
  height: 11px;
  border: solid var(--primary-text-color);
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.filter_list_detail {
  margin-left: 8px;
}

.filter_list_detail span {
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  color: #1a1a1a;
}

.filter_list_detail small {
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  color: #808080;
  padding-left: 6px;
}

/* rating star filter css */
.rating_filter_list_detail {
  margin-left: 8px;
}

.rating_filter_data i {
  padding: 0px 2px;
  font-size: 14px;
}

.rating_filter_data i.fas.fa-star {
  color: #FF8A00;
}

.rating_filter_data i.far.fa-star {
  color: #CCCCCC;
}

.rating_filter_list_detail span {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  padding-left: 2px;
}

/* price range filter css */

.filter_range_value {
  margin: 0;
  margin-top: 17px;
}

.price_range_slider input {
  width: 100%;
  background: none;
  color: #4D4D4D;
  font-size: 14px;
  font-weight: 600;
  line-height: 21px;
  box-shadow: none;
  border: none;
}

.filter_range_value span {
  color: #4D4D4D;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  padding-right: 4px;
}

.price_filter_range_bar {
  background: #E6E6E6;
  height: 6px;
  width: 100%;
  margin-left: 0px;
  border: unset !important;
}

.price_filter_range_bar .ui-slider-range {
  background: var(--primary-bg-color);
}


.price_filter_range_bar .ui-slider-handle {
  border-radius: 50%;
  background: #fff !important;
  border: 2px solid var(--primary-bg-color) !important;
  height: 18px !important;
  width: 18px !important;
  top: -0.4em !important;
  cursor: pointer !important;
}

.price_filter_range_bar .ui-slider-handle:focus-visible {
  outline: unset !important;
}

.ui-slider-handle+span {
  background: var(--primary-bg-color);
}

.price_range_filter {
  width: 100%;
  height: 34px;
  background-color: var(--primary-bg-color);
  border-radius: 5px;
  color: var(--primary-text-color);
  font-size: 14px;
  border: unset;
  margin-top: 15px;
}

/* sort filter section css */
.shop_sort_filter_parent {
  margin-bottom: 10px;
}

.shop_sort_filter_parent h5 {
  font-size: 20px;
  font-weight: 500;
  line-height: 26.66px;
  color: #1a1a1a;
}

.shop_sort_filter label {
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  padding-right: 8px;
  color: #808080;
}

.shop_sort_filter select {
  border: 1px solid #a9a5a5;
  border-radius: 10px;
  padding: 12px 16px;
  width: 166px;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  color: #4d4d4d;
}

/* product listing style  */
.product_list_heading {
  margin: 12px 0px;
  background-color: #f7f7f7;
  padding: 10px 10px;
  border-radius: 4px;
}

.product_list_heading h6 {
  font-size: 20px;
  font-weight: 600;
  line-height: 26.66px;
}

.product_listing_section {
  margin: 0px -8px;
}

.single_product {
  padding: 8px 8px;
}

.single_product .product_card {
  display: grid;
  grid-template-columns: 1fr;
  border-radius: 10px;
  padding: 15px 15px;
  background-color: var(--main-bg-color);
  box-shadow: var(--primary-box-shadow) 0px 7px 29px 0px;
}

.product_card a.destination_img {
  margin-bottom: 15px;
  border-radius: 5px;

  background: #CCD8D8;
}
.product_card a.destination_img img{
  aspect-ratio: 1/1;
    border-radius: 5px;
}
.nearest_departure_date {
  position: absolute;
  bottom: 0px;
  right: 0px;
  background-color: var(--primary-bg-color);
  color: var(--primary-text-color);
  font-size: 14px;
  border-radius: 4px;
  font-weight: 500;
  padding: 5px 10px;
}


.shop_single_package_rating {
  padding-bottom: 12px;
  display: none !important;
}

.shop_single_package_rating i {
  padding-right: 4px;
  font-size: 14px;
  color: #E7581D;
}

.shop_package_location {
  padding-bottom: 12px;
}

.shop_package_location span:first-child {
  margin-bottom: 10px;
}

.shop_package_location span {
  font-size: 13px;
  color: var(--custom-text-color-one);
  font-weight: 500
}

.shop_package_location span i {
  font-size: 13px;
  color: var(--custom-text-color-one);
  padding-right: 8px;
}

.single_product .product_content .product_name {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  width: 100%;
  text-overflow: ellipsis;
  word-wrap: break-word;
  overflow: hidden;
  height: 40.64px;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  font-family: Poppins;
  color: var(--secondary-text-color);
  margin-bottom: 8px;
}

.shop_short_des {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    width: 100%;
    text-overflow: ellipsis;
    word-wrap: break-word;
    overflow: hidden;
    height: 44px;
    margin-bottom: 15px;
}

.shop_short_des * {
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  color: var(--custom-text-color-one) !important;
  display: inline !important;  
}

.total_and_discount_price {
  margin-bottom: 15px;
}

.total_and_discount_price .price_label {
  font-size: 14px;
  font-weight: 400;
  line-height: 15.12px;
  padding-bottom: 10px;
  font-family: Poppins;
  color: var(--custom-text-color-one);
}

.total_and_discount_price .orignal_price {
  font-size: 16px;
  font-weight: 700;
  line-height: 19.16px;
  letter-spacing: -0.4000000059604645px;
  color: var(--primary-bg-color);
}

.total_and_discount_price .discount_price {
  font-size: 10px;
  font-weight: 300;
  line-height: 12.6px;
  letter-spacing: -0.4000000059604645px;
  color: #929292;
  text-decoration: line-through;
}

.product_cart_btn a {
  width: 100%;
  height: 34px;
  background-color: var(--primary-bg-color);
  border-radius: 5px;
  color: var(--primary-text-color);
  font-size: 14px;
}


@media (max-width: 991px) {
  .shop_content_body {
    flex-direction: column;
  }

  .filter_parent {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .product_list_parent {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .filters_list {
    position: fixed;
    top: 0px;
    left: 0px;
    height: 100vh;
    overflow-y: auto;
    background: #fff;
    padding: 20px 20px;
    width: 320px;
    box-shadow: -10px 0px 10px #00000045;
    transform: translatex(-100%);
    transition: 0.5s ease-in-out;
    z-index: 9999;
  }

  .filters_list.active {
    transform: translatex(0%);
    transition: 0.5s ease-in-out;
    box-shadow: 0 0px 52px rgb(0 0 0 / 0.3);
  }

  .filter_responsive_btn {
    border-bottom: 1px solid #e5e5e5;
    margin-bottom: 20px;
    padding-bottom: 20px;
  }

  .filter_responsive_btn span {
    font-size: 20px;
    font-weight: 500;
    line-height: 26.66px;
    color: #272B2C;
  }

  .filter_responsive_btn a {
    background-color: var(--primary-bg-color);
    border-radius: 10px;
    padding: 7px 12px 7px 12px;
  }

  .filter_responsive_btn a i {
    color: var(--primary-text-color);
    font-size: 16px;
    font-weight: 600;
    line-height: 16.8px;
  }
}

@media (max-width: 768px) {
  .shop_product_listing {
    margin: 0px -10px;
  }


  .shop_sort_filter_parent h5 {
    font-size: 16px;
  }

  .shop_sort_filter label {
    font-size: 12px;
  }

  .shop_sort_filter select {
    font-size: 12px;

    padding: 8px 6px;

  }
  .single_product .product_content .product_name {
    font-size: 13px;
    line-height: 19px;
      
  }
  .shop_short_des * {
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 24px;
    text-align: left !important;
}

}

@media (max-width: 540px) {

  .shop_sort_filter_parent {
    flex-direction: column;
  }

  .shop_sort_filter {
    width: 100%;
  }

  .shop_sort_filter_parent h5 {
    margin-bottom: 10px;
  }

  .shop_sort_filter select {
    width: 100% !important;
  }
}

/* shop page content body css end */


/* category section css start */
.tour_category_section {
  background: var(--secondary-bg-color);
  padding-top: 40px !important;
  padding-bottom: 40px !important;
}

.product_category_section {
  padding: 0px 15px 0px 15px;
  margin-bottom: 40px;
}

.feature_section_header {
  padding-bottom: 20px;
}
.feature_section_header small {
font-size: 14px;
    font-weight: 500;
    line-height: 18.9px;
    letter-spacing: 0.07em;
    text-transform: uppercase;
    padding-bottom: 4px;
    color: var(--active-color);
    }
.feature_section_header h5 {
    font-size: 36px;
    font-weight: 700;
    line-height: 46px;
    color: var(--secondary-text-color);
    font-family: Poppins;
        text-align: center;
}
.tour_category_btn {
    padding-top: 25px;
}
.tour_category_btn a {
    font-family: Poppins;
    font-size: 18px;
    font-weight: 400;
    line-height: 25px;
    color: var(--primary-text-color);
    box-shadow: 8px 8px 0px 0px var(--primary-bg-shadow);
    background: var(--primary-bg-color);
    border-radius: 6px;
    text-transform: capitalize;
    transition: 0.2s ease-in-out;
    height: 46px;
    width: 176px;
    display: flex;
    align-items: center;
    justify-content: center;
}


.product_category_list .category_arrow_box .arrow_box,
.type_arrow_box .arrow_box {
  position: absolute;
  height: 100%;
  top: 0px;
  z-index: 99;
  align-items: center;
  display: flex;
}

.category_left_arrow {
  left: -10px;
}

.category_right_arrow {
  right: -10px;
}

.category_arrow_box .arrow_box a,
.type_arrow_box .arrow_box a {
  cursor: pointer;
  width: 25px;
  height: 48px;
  background: var(--active-bg-color);
  z-index: 99;
  border-radius: 5px;
  color: var(--active-bg-text-color-color);
}

.category_arrow_box .arrow_box a i,
.type_arrow_box .arrow_box a i {
  color: var(--active-bg-text-color) !important;
  font-size: 20px;


}

.product_category_list ul {
  margin: 0px -10px;
}

.product_category_list ul li {
  width: 25%;
  padding: 10px 10px;
  list-style: none;
}


.product_category_list ul li a img {
  border-radius: 6px;
  aspect-ratio: 1/1;
}

.product_category_list ul li a figcaption {
  padding: 10px 10px;
    background: #000000a3;
}

.product_category_list ul li a figcaption span {
  font-size: 18px;
  font-weight: 500;
  line-height: 22px;
  color: var(--custom-color-one);
  font-family: Poppins;
  text-transform: capitalize;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@media (max-width: 991px) {
  .tour_category_btn a {
    height: 40px;
    width: 163px;
    font-size: 14px;
    line-height: 20px;
  }
}

@media (max-width: 768px) {
  .product_category_section {
    margin-bottom: 30px;
  }
  .tour_category_btn a {
 
    width: 130px;

  }

  .product_category_list ul li a figcaption span {
    font-size: 14px;
  }

  .feature_section_header h5 {
    font-size: 26px;
    line-height: 27px;
  }

  .category_arrow_box .arrow_box a {
    width: 20px;
    height: 35px;
  }

  .category_arrow_box .arrow_box a i {
    font-size: 18px;

  }

  .product_category_list ul {

    margin: 0px -6px;

  }

  .product_category_list ul li {

    padding: 6px 6px;

  }
}

/* category section css end */

/* custom package section css start */
.custom_package {
    background: var(--secondary-bg-color);
    padding: 15px 15px 15px 15px;
    margin-bottom: 40px;
}

.custom_package_heading small {
    font-size: 14px;
    font-weight: 600;
    line-height: 27px;
    color: var(--active-color);
    text-transform: uppercase;
}

.custom_package_heading h3 {
    font-size: 22px;
    font-weight: 600;
    line-height: 31px;
    color: var(--secondary-text-color);
    font-family: Poppins;
}

.custom_package_btn {
 font-family: Poppins;
    font-size: 14px;
    font-weight: 500;
    line-height: 15px;
    color: var(--primary-text-color);
    box-shadow: 8px 8px 0px 0px var(--primary-bg-shadow);
    background: var(--primary-bg-color);
    border-radius: 6px;
    text-transform: capitalize;
    transition: 0.2s ease-in-out;
    height: 46px;
    width: 185px;
        cursor: pointer;
}

.custom_package_close_btn {
  position: absolute;
  top: -10px;
  right: -13px;
  color: var(--primary-text-color);
  background: var(--primary-bg-color);
  padding: 7px 10px 13px 10px;
  opacity: 1;
}

.custom_package_close_btn:hover {
  opacity: 1;
  color: var(--primary-text-color);
}

.custom_package_close_btn i {
  font-size: 18px;
  line-height: 20px;
}

.modal.fade.zoom:not(.show) .modal-dialog {
  transform: scale(0.8);
}


.custom_package_container {
  max-width: 560px;
  margin: 20px auto;
  padding: 0px 20px;
}

.custom_package_popup {
  background-color: var(--main-bg-color);
  box-shadow: var(--primary-box-shadow) 0px 7px 29px 0px;
  border-radius: 4px;
  padding: 32px 15px 38px 15px;
  pointer-events: auto;
}

.custom_package_popup_des {
  padding-bottom: 25px;
}

.custom_package_popup_des h5 {
  font-style: normal;
  font-weight: 700;
  font-size: 21px;
  line-height: 25px;
  color: var(--secondary-text-color);
  margin: 0px;
  font-family: Poppins;
  padding-bottom: 11px;
}

.custom_package_popup_des p {
  font-size: 14px;
  font-weight: 500;
  line-height: 23px;
  color: var(--custom-text-color-one);
}

.custom_package_popup_btn {
  display: grid;
  align-items: center;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  width: 100%;
}

.custom_package_popup_btn a {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--primary-text-color) !important;
  background: var(--primary-bg-color);
  height: 44px;
  cursor: pointer;
}

.custom_package_popup_btn input {
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--primary-text-color);
  background: var(--primary-bg-color);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 44px;
  cursor: pointer;
  border: unset;

}

@media(max-width:991px) {


  .custom_package_btn {
    height: 40px;

    font-size: 14px;
 
  }


  .custom_package_heading small {
    font-size: 14px;
    line-height: 22px;
  }
}

@media(max-width:540px) {
  .custom_package {
    margin-bottom: 30px;
  }

  .custom_package_box {
    flex-direction: column;
    text-align: center;

  }

  .custom_package_heading {
    padding-bottom: 15px;
  }


    .custom_package_heading small {
        font-size: 12px;
        line-height: 15px;
    }
  .custom_package_heading h3 {
    font-size: 20px;
    line-height: 30px;
  }

  .custom_package {
    background: var(--secondary-bg-color);
    padding: 20px 15px 20px 15px;
  }
}

/* custom package section css end */

/* footer css start */
.footer_section_parent {
  background: var(--footer-bg-color);
border-top: 1px solid var(--footer-border-color);
}

.footer_img_layer {
  max-width: 500px;
  right: 0px;
  bottom: 0px;
}

.footer_section {
  padding: 60px 15px 25px 15px;
}

.footer_data_box {
  display: grid;
  grid-template-columns: 1.6fr 1fr 1fr 1fr 1fr;
  gap: 30px 20px;
}

.footer_detail_box_one figure {
  width: 160px;
  padding-bottom: 24px;
}

.footer_detail_box_one p {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: -0.02em;
  text-align: left;
  color: var(--footer-text-color);
  padding-right: 23px;
  padding-bottom: 24px;
}

.footer_socail_links {
  padding-bottom: 20px;
}

.footer_socail_links a {
  background: var(--active-bg-color);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 10px;
}

.footer_socail_links a:last-child {
  margin-right: 0px;
}

.footer_socail_links a i {
  color: var(--active-bg-text-color);
  font-size: 16px;
}

.footer_detail_box_heading {
  padding-bottom: 24px;
}

.footer_detail_box_heading h3 {
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  color: var(--footer-text-color);
  text-transform: capitalize;
  font-family: Poppins;
}
.footer_detail_box_content_list {
  position: relative;
  z-index: 1;
}
.footer_detail_box_content_list li {
  list-style: none;
  padding-bottom: 18px;
}

.footer_detail_box_content_list li:last-child {
  padding-bottom: 0px;
}

.footer_detail_box_content_list li i {
  font-size: 14px;
  line-height: 18px;
  color: var(--footer-text-color);
  padding-right: 8px;
}

.footer_detail_box_content_list li a {
  font-size: 14px;
  font-weight: 500;
  line-height: 18px;
  letter-spacing: 0em;
  text-align: left;
  color: var(--footer-text-color);
  text-decoration: none;
}

.footer_detail_box_content_list li a:hover {
  color: var(--footer-text-color);
}

.copy_right_section {
  border-top: 1px solid var(--footer-border-color);
  padding: 20px 10px;
}

.copy_right_section small {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  color: var(--footer-text-color);
}

@media(max-width: 1024px) {
  .footer_data_box {
    grid-template-columns: 1.6fr 1fr 1fr;
    grid-template-areas: ". . .";
  }

  .footer_section {
    padding: 60px 15px 25px 15px;
  }
}

@media(max-width: 768px) {
  .footer_data_box {
    grid-template-columns: 1.6fr 1fr;
    grid-template-areas: ". .";
  }
}

@media(max-width: 540px) {
  .footer_data_box {
    grid-template-columns: 1fr;
    grid-template-areas: ".";
    gap: 30px 0px;
  }

  .footer_detail_box_one {
    justify-content: center;
    align-items: center;
  }

  .footer_detail_box_one p {
    padding-right: 0px;
    text-align: center;
  }

  .footer_detail_box_heading {
    padding-bottom: 0px;
  }

  .footer_detail_box_content_list {
    padding-top: 15px !important;
  }

  .footer_socail_links {
    justify-content: center;
    align-items: center;
  }

  .panel {
    display: none;
    overflow: hidden;
    transition: 0.5s ease-out;
    text-align: left;
  }


  .footer_detail_box_heading.accordion:after {
    content: '\002B';
    color: var(--footer-text-color);
    font-family: "revicons";
    font-size: 26px;
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;
    margin: 0px;
    top: 0px;
    right: 0px;
  }

  .footer_detail_box_heading.active:after {
    content: "\2212" !important;
    color: var(--footer-text-color);
    font-family: "revicons";
    font-size: 21px;
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;
    margin: 0px;
    top: 0px;
    right: 0px;
  }


}

@media(min-width:540px) {
  .modcontent.panel {
    display: block !important;
  }
}

/* footer css end */


.inner_banner_text_section {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  padding: 70px 10px 70px 10px;
  margin-bottom: 30px;
}

.inner_banner_text_section:before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  background: #00000099;
}

.inner_banner_container {
  max-width: 1200px;
  margin: 0 auto;
}

.inner_banner_slider_text {
  max-width: 840px;
  position: relative;
}

.inner_banner_slider_text h2 {
  font-family: Poppins;
  font-size: 38px;
  font-weight: 700;
  line-height: 46px;
  letter-spacing: 0em;
  color: var(--home-banner-text-color);
  padding-bottom: 22px;
}

.inner_banner_slider_text p {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: var(--home-banner-text-color);
  text-align: justify;
}

@media (max-width: 1024px) {
  .inner_banner_slider_text {
    max-width: 750px;
  }
}

@media (max-width: 768px) {
  .inner_banner_text_section {
    padding: 50px 10px 50px 10px;
  }

  .inner_banner_slider_text h2 {
    font-size: 24px;
    line-height: 29px;
    padding-bottom: 10px;
  }

  .inner_banner_slider_text p {
    font-size: 14px;
    line-height: 20px;
  }

}

.shop_filter_header_btn {
  border-radius: 6px;
  background: var(--secondary-bg-color) !important;
  padding: 18px 10px !important;
}

.shop_filter_header_btn:focus {
  outline: unset !important;
  box-shadow: unset !important;
}

.shop_filter_header_data span {
  color: var(--secondary-text-color);
  font-size: 15.375px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px;
  letter-spacing: -0.029px;
}

.shop_filter_accordion_parent {
  display: grid;
  gap: 15px 0px;

}

.shop_filter_accordion_parent .accordion-button i {
  transition: transform 0.4s ease;
}

.shop_filter_accordion_parent .accordion-button:not(.collapsed) i {
  transform: rotate(-180deg);
  transition: transform 0.4s ease;
}

.shop_filter_accordion_parent .accordion-button::after {
  display: none;
}

.filter_listing {
  padding: 16px 0px;
}

.filter_listing li {
  list-style: none;
  padding-bottom: 10px;
}

.filter_listing li:last-child {
  padding-bottom: 0px;
}

.filter_listing li label {
  position: relative;
  width: 100%;
  padding-left: 28px;
}

.filter_listing label:before {
  content: " ";
  display: block;
  border-radius: 4px;
  border: 1px solid var(--main-border-color);
  position: absolute;
  width: 15px;
  height: 15px;
  text-align: center;
  line-height: 28px;
  transition-duration: 0.4s;
  left: 0px;
  top: 5px;
}

.filter_listing li input[type="checkbox"]:checked+label:before {
  background-color: var(--secondary-bg-color);
  transform: scale(1.3);
}

.filter_listing li input[type="checkbox"]:checked+label:after {
  content: "✓";
  position: absolute;
  color: var(--secondary-text-color);
  top: -10px;
  left: 1px;
  font-size: 24px;
}

.filter_listing li input[type="checkbox"][id^="Checkbox"] {
  display: none;
}

.filter_listing li span {
  color: var(--secondary-text-color);
  font-size: 15.375px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px;
  letter-spacing: -0.029px;
}

/* select2 custom css start  */
.custom_select_field span.label {
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.03em;
  color: var(--secondary-text-color);
  padding-bottom: 10px;
}

.login_single_field_box label {
  position: absolute;
  left: 3px;
  bottom: -27px;
  color: red;
  font-size: 14px;
}
.login_single_field_box .invalid-feedback{
    position: absolute;
  left: 3px;
bottom: -22px;
  color: red;
  font-size: 14px;
}
input.select2-search__field {
  width: 100% !important;
}

span.select2.select2-container.select2-container--default {
  width: 100% !important;
}

span.select2-selection.select2-selection--multiple {
  padding: 10px 10px;
  border-radius: 4px;
  font-size: 15px;
  background-color: var(--secondary-bg-color) !important;
  border: 1px solid var(--secondary-bg-color) !important;
  color: var(--main-text-color);
}

.destination_booking span.select2-selection.select2-selection--multiple {

  background-color: var(--main-bg-color) !important;
  border: 1px solid var(--main-bg-color) !important;
  color: var(--secondary-text-color) !important;
}

ul.select2-selection__rendered {
  padding: 0px !important;
}

ul.select2-selection__rendered li.select2-selection__choice {
  font-weight: 500 !important;
  padding: 4px 30px 4px 15px !important;
  background-color: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
  border-radius: 2px !important;
  margin: 6px 6px;
  border: unset !important;
  font-size: 14px !important;
}

ul.select2-selection__rendered {
  display: flex !important;
  flex-wrap: wrap;
}

ul.select2-selection__rendered li.select2-selection__choice span.select2-selection__choice__remove {
  border: unset !important;
  right: 0px;
  left: unset !important;
  height: 100%;
  display: flex;
  align-items: center;
  font-size: 22px;
  color: var(--primary-text-color) !important;
}

ul.select2-selection__rendered li.select2-selection__choice span.select2-selection__choice__remove:hover {
  background-color: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
}

span.select2-dropdown.select2-dropdown--below {
  border: unset !important;
  background-color: var(--main-bg-color);
  border-radius: 2px;
  box-shadow: var(--primary-box-shadow) 0px 7px 29px 0px;
  padding: 10px 10px;
}

ul.select2-results__options .select2-results__option {
  border-bottom: 1px solid var(--main-text-color) !important;
  color: var(--main-text-color) !important;

}

ul.select2-results__options .select2-results__option:last-child {
  border-bottom: unset !important;

}

ul.select2-results__options .select2-results__option--highlighted {
  color: var(--main-text-color) !important;
}

span.select2-container.select2-container--default.select2-container--open {
  z-index: 99999 !important;
}

/*  */

/* related destinations */
.related_destination_box {
  padding: 40px 15px;
  background: var(--secondary-bg-color);
  margin-top: 30px;
}

.related_destination_header {
  padding-bottom: 25px;
}

.related_destination_header h4 {
  font-size: 26px;
  line-height: 36px;
  font-weight: 600;
  text-transform: capitalize;
  font-family: Poppins;
}

.related_destination_list {
  margin: 0px -10px;
  padding: 0px;
  -webkit-mask-image: linear-gradient(to left, transparent, black 20%, black 100%, transparent 100%);
}

.related_destination_list li {
  padding: 0px 10px 0px 10px;
}

@media(max-width:768px) {
  .related_destination_header {
    padding-bottom: 10px;
  }

  .related_destination_list {
    margin: 0px -8px;
  }

  .related_destination_list li {
    padding: 8px 8px 8px 8px;
  }

  .related_destination_header h4 {
    font-size: 24px;
    line-height: 26px;
  }
}
   .privacy_policy_page{
            display: block;
            width: 100%;
            padding: 0px 15px 0px 15px;
        }
        .custom_container{
        max-width:1140px;
        margin:0 auto;
        }
        .privacy_policy_detail{
           display: grid;
            gap: 20px 0px;
            width: 100%;
        }
        .privacy_policy_detail_description{
           display: grid;
            gap: 10px 0px;
            width: 100%;
        }
        .privacy_policy_detail_description p{
         font-style: normal;
            font-weight: normal;
            font-size: 18px;
            line-height: 28px;
            color: rgba(0, 0, 0, 0.7);
            width: 100%;
        }
        .privacy_policy_detail_two p a {
            color: rgba(0, 0, 0, 0.7);
            text-decoration: none;
            font-weight: bold;
        }
        .privacy_policy_detail_two{
           display: grid;
            gap: 10px 0px;
            width: 100%;
        }
        .privacy_policy_detail_two h2{
            font-style: normal;
            font-weight: 600;
            font-size: 20px;
            line-height: 24px;
            letter-spacing: 0.1em;
            text-transform: uppercase;
            color: #000000;
            margin: 0px !important;
        }
        .privacy_policy_detail_two p{
         font-style: normal;
            font-weight: normal;
            font-size: 18px;
            line-height: 28px;
            color: rgba(0, 0, 0, 0.7);
            width: 100%;
        }
        .privacy_policy_detail_two ul{
           display: grid;
            gap: 10px 0px;
            width: 100%;
            padding: 0px 0px 0px 17px;
        }
        .privacy_policy_detail_two ul li{
         font-style: normal;
            font-weight: normal;
            font-size: 18px;
            line-height: 28px;
            color: rgba(0, 0, 0, 0.7);
            width: 100%;
            list-style: square;
        }
        .privacy_policy_detail_two ul li::marker{
            color:  var(--primary-bg-color) !important;

        }
        .privacy_policy_detail_two ol{
           display: grid;
            gap: 10px 0px;
            width: 100%;
            padding: 0px 0px 0px 17px;
        }
        .privacy_policy_detail_two ol li{
         font-style: normal;
            font-weight: normal;
            font-size: 18px;
            line-height: 28px;
            color: rgba(0, 0, 0, 0.7);
            width: 100%;
        }
        @media (max-width: 1024px){
      
        }
        
        @media (max-width: 768px){
       
        }
        @media (max-width: 540px){
       
        .privacy_policy_detail_description {
            text-align: center;
        }
        .privacy_policy_detail_two {
            text-align: center;
        }
        }
        @media (max-width: 450px){
       
        }
        
         .newsletter-error.invalid-feedback {
        background: var(--bs-form-invalid-color);
        color: #fff;
        padding: 2px 2px;
        margin-bottom: 0px;
        display: block;
        float: left;
        position: absolute;
        bottom: -147px;
    }

    .watts_app_chat_bot {
        position: fixed;
        bottom: 20px;
        right: 8px;
        transition: 0.5s;
        z-index: 99;
    }

    .watts_app_chat_bot a {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .watts_app_chat_bot a span {
          color: #000 !important;
    font-size: 10px !important;
    background: #fff;
    padding: 2px 10px;
    border-radius: 6px;
    margin-top: 8px;
    box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    font-weight: 700;
    text-transform: uppercase;
    }

    @media(max-width:1024px) {
.watts_app_chat_bot a img {
    width: 30px;
    height: 30px;
}
         .watts_app_chat_bot a span {
        font-size: 9px !important;
    }
    }