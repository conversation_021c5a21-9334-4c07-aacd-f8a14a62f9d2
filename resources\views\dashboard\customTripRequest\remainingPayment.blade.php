<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRemainingPayment{{$tripList->id}}"
    aria-labelledby="offcanvasRemainingPayment{{$tripList->id}}Label">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 id="offcanvasRemainingPayment{{$tripList->id}}Label" class="offcanvas-title">Remaining Payment Email</h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="remaining_payment_{{$tripList->id}}">
            @csrf
            <input type="hidden" name="id" value="{{$tripList->id}}" />
            <div class="d-grid gap-3 w-100">

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="message">Message</label>
                    <textarea class="custom_form_field" name="message" id="message" rows="10"></textarea>
                </div>

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="total_price">Total price</label>
                    <input type="number" min="0" class="custom_form_field total_price" name="total_price"
                        value="{{$tripList->total_price}}" readonly />
                </div>

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="advance_price">Advance in price (already paid)</label>
                    <input type="number" min="0" class="custom_form_field advance_price" name="advance_price"
                        value="{{$tripList->advance_price}}" readonly />
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="remaining_price">Remaining price </label>
                    <input type="number" min="0" class="custom_form_field remaining_price" name="remaining_price"
                        value="{{$tripList->total_price - $tripList->advance_price}}" readonly />
                </div>

                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button class="custom_btn_2">Submit
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>
        </form>
    </div>
</div>
