<section class="product_category_section w-100 d-inline-block">
    <div class="custom_container">
        <div class="feature_section_header d-flex align-items-center justify-content-center w-100 flex-wrap flex-column">
            <small>Destination's Types</small>
            <h5>Adventure Awaits: African Travel Styles</h5>

        </div>
        <!-- Skeleton Loader (show during loading) -->
        <div id="type-skeleton" class="skeleton-wrapper">
            @for ($i = 0; $i < 4; $i++)
                <div class="skeleton-box">
                <div class="skeleton-item"> </div>
        </div>
        @endfor
    </div>
    <div class="product_category_list product_type_list_show w-100 position-relative" style="display: none;">
        <div class="type_arrow_box"></div>
        <ul class="product_type_slider ">
            @foreach($destinationType as $types)
            <li class="h-100">
                <a href="{{ route('website.destinationType.destinationTypesDetail', $types->slug) }}"
                    class="position-relative w-100 float-left">

                    <img width="300" height="300" src="{{$types ? asset('storage/destinations/'.$types->cover_image) : asset('website/images/logo.webp') }}"
                     data-src="{{$types ? asset('storage/destinations/'.$types->cover_image) : asset('website/images/logo.webp') }}"
                        class="lazyload w-100 h-100 object-fit-cover" alt="{{$types->alt_text_cover_image}}"
                        loading="lazy" decoding="async"  />

                    <figcaption
                        class="position-absolute w-100 bottom-0 left-0 d-flex justify-content-between align-items-end">
                        <span>{{$types->title}}</span>
                    </figcaption>
                </a>
            </li>
            @endforeach
        </ul>
    </div>
    <div class="tour_category_btn d-flex align-items-center justify-content-center w-100"> <a href="{{route('website.destinationType.destinationTypes')}}">View All</a></div>
    </div>
</section>