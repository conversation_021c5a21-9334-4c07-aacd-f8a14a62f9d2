<?php

namespace App\Http\Controllers;

use App\Models\bookingPayments;
use App\Models\DestinationBooking;
use App\Models\Destinations;
use App\Models\Subscribers;
use App\Models\Commissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class reportsController extends Controller
{
    public function BookingReport()
    {
        $user = Auth::user();
         $commission = Commissions::where('country',$user->subscriber->country)->first();
        $destinations = Destinations::where('user_id', $user->id)->get();
        if ($user->role == 'admin') {
            $agents = Subscribers::with('user')->get();
            return view('dashboard.destinations.reports', ['destinations' => $destinations, 'agents' => $agents]);
        } else {
            return view('dashboard.destinations.reports', ['destinations' => $destinations,'commission'=>$commission]);
        }

    }

    public function getDestination($id)
    {
        $destinationResult = Destinations::where('user_id', $id)->get();

        return response()->json(['destinationResult' => $destinationResult]);
    }

    public function getDestinationDates($id)
    {
        $destination = Destinations::find($id);
        $data = json_decode($destination->departure_date_time);
        return response()->json(['dates' => explode(', ', $data)]);
    }

    public function filterBookingReport(Request $request)
    {
        $user = Auth::user();
         $commission = Commissions::where('country',$user->subscriber->country)->first();
        $destinations = Destinations::where('user_id', $user->id)->get();

        $destinationId = $request->destination;
        $destinationDate = $request->dates;

        $bookings = DestinationBooking::with('bookedBy')->with('destination')
            ->where('destination_id', $destinationId)->where('date', $destinationDate)
            ->orderBy('created_at', 'desc')
            ->get();
        $totalPayment = "";
        $charges = "";
        $totalPaymentAfterCharges = "";
        $payments = collect(); // Initialize an empty collection for payments
        foreach ($bookings as $booking) {
            $bookingPayments = bookingPayments::where('booking_id', $booking->id)->with('bookedBy')
                ->orderBy('created_at', 'desc')
                ->get();
            $totalPayment = $bookingPayments->sum('price'); // Total payment for the booking
            $charges = $bookingPayments->sum('charges'); // Total charges for the booking
            $totalPaymentAfterCharges = $totalPayment - $charges; // Total payment after subtracting charges

            $payments = $payments->merge($bookingPayments); // Merge the payments into the collection
        }
       // dd($totalPayment);
      //  dd($totalPaymentAfterCharges);
        if ($user->role == 'admin') {
            $agents = Subscribers::with('user')->get();
            return view('dashboard.destinations.reports', ['bookings' => $bookings, 'payments' => $payments, 'destinations' => $destinations, 'totalPayment' => $totalPayment, 'charges' => $charges, 'totalPaymentAfterCharges' => $totalPaymentAfterCharges, 'agents' => $agents]);
        } else {

            return view('dashboard.destinations.reports', ['bookings' => $bookings, 'payments' => $payments, 'destinations' => $destinations, 'totalPayment' => $totalPayment, 'charges' => $charges, 'totalPaymentAfterCharges' => $totalPaymentAfterCharges,'commission'=>$commission]);
        }
    }

}
