$("#multi_image_picker").spartanMultiImagePicker({
    fieldName: "image_gallery[]",
});

// Initialize the spartanMultiImagePicker with the starting index
$("#update_multi_image_picker").spartanMultiImagePicker({
    fieldName: "update_image_gallery[]",
});

$(document).on("click", ".delete-image", function (e) {
    e.preventDefault();

    var id = $(this).data("id");
    var destinationId = $("#destinationId").data("value");

    deleteGalleryImage(id, destinationId);
});

function deleteGalleryImage(id, destinationId) {
    let baseURL = window.location.origin;
    let apiEndpoint = "/dashboard/destination/gallery-image/delete";
    let apiUrl = baseURL + apiEndpoint;

    // Get the CSRF token from the head of the document
    let token = document
        .querySelector('meta[name="csrf-token"]')
        .getAttribute("content");
    $(".form_process_loader").removeClass("d-none");
    $.ajax({
        url: apiUrl,
        type: "DELETE",
        data: {
            destinationId: destinationId,
            id: id,
        },
        headers: {
            "X-CSRF-TOKEN": token,
        },
        success: function (data) {
            $(".form_process_loader").addClass("d-none");
            showAlert("image deleted successfull !", "success");
            if (data.success) {
                setTimeout(function () {
                    $('[data-id="' + id + '"]').remove();
                }, 2000);
                // Remove the image div from the view
            } else {
                showAlert("Error deleting image !", "error");
                console.log("Error deleting image:", data.message);
            }
        },
        error: function (xhr, status, error) {
            $(".form_process_loader").addClass("d-none");
            showAlert("Error deleting image !", "error");
            console.log("Error deleting image:", error);
        },
    });
}

flatpickr("#multiple-date-picker", {
    mode: "multiple",
    enableTime: true,
    dateFormat: "Y-m-d H:i K",
    minDate: "today",
    //  defaultDate: "today",
    time_24hr: false,
});

$(document).ready(function () {
    $("#destination_short_des").summernote({
        height: "300px",
    });
});

// old script
// $(document).ready(function () {
//     var editorCount = 0;

//     $("#add-editor-btn").on("click", function () {
//         editorCount++;
//         var editorId = "custom-section-editor" + editorCount;
//         var labelId = "label" + editorCount;

//         var editorContainer = $("<div>", { class: "card-body editor_body" });
//         var label = $("<label>", { for: editorId, text: "Day " + editorCount });
//         var textarea = $("<textarea>", {
//             id: editorId,
//             name: "section[" + editorCount + "]",
//             style: "display: none;",
//             "aria-hidden": "true",
//         });
//         var script = $("<script>").text(`$("#${editorId}").summernote({
//             height: 300,

//         });`);

//         editorContainer.append(label, textarea, script);
//         $("#editors-container").append(editorContainer);
//     });
// });

$(document).ready(function () {
    var editorCount = 0;

    $("#add-editor-btn").on("click", function () {
        editorCount++;
        var editorId = "custom-section-editor" + editorCount;
        var inputId = "input_" + editorId;
        var deleteBtnId = "delete-btn-" + editorCount;

        var editorContainer = $("<div>", {
            class: "card-body editor_body",
            id: "editor-container-" + editorCount,
        });
        var input = $("<input>", {
            type: "text",
            id: inputId,
            name: "day[" + editorCount + "]",
            value: "Day " + editorCount,
        });
        var img_input = $("<input>", {
            type: "file",
            name: "main_image[" + editorCount + "]",
            class: "dropify",
            "data-max-file-size": "2M",
            "data-allowed-file-extensions": "jpg jpeg png webp",
        });
        var textarea = $("<textarea>", {
            id: editorId,
            name: "section[" + editorCount + "]",
            style: "display: none;",
            "aria-hidden": "true",
        });
        var deleteBtn = $("<button>", {
            type: "button",
            class: "custom_btn_1",
            id: deleteBtnId,
            text: "Delete",
            click: function () {
                $(this).closest(".editor_body").remove();
            },
        });
        var script = $("<script>").text(`$("#${editorId}").summernote({
            height: 300,
          
        });`);

        editorContainer.append(input, img_input, textarea, script, deleteBtn);
        $("#editors-container").append(editorContainer);
        img_input.dropify();
    });
});

$(document).ready(function () {
    var accomodationEditorCount = 0;

    $("#add-accomodation-editor-btn").on("click", function () {
        accomodationEditorCount++;
        var accomodationEditorId =
            "custom-section-accomodation-editor" + accomodationEditorCount;
        var accomodationInputId = "input_" + accomodationEditorId;
        var accomodationDeleteBtnId =
            "accomodation-delete-btn-" + accomodationEditorCount;

        var accomodationEditorContainer = $("<div>", {
            class: "accomodation-card-body accomodation_editor_body",
            id: "accomodation-editor-container-" + accomodationEditorCount,
        });
        var accomodationinput = $("<input>", {
            type: "text",
            id: accomodationInputId,
            name: "day[" + accomodationEditorCount + "]",
            value: "Day " + accomodationEditorCount,
        });
        var acc_img_input = $("<input>", {
            type: "file",
            name: "acc_main_image[" + accomodationEditorCount + "]",
            class: "dropify",
            "data-max-file-size": "2M",
            "data-allowed-file-extensions": "jpg jpeg png webp",
        });
        var accomodationtextarea = $("<textarea>", {
            id: accomodationEditorId,
            name: "section[" + accomodationEditorCount + "]",
            style: "display: none;",
            "aria-hidden": "true",
        });
        var accomodationdeleteBtn = $("<button>", {
            type: "button",
            class: "custom_btn_1",
            id: accomodationDeleteBtnId,
            text: "Delete",
            click: function () {
                $(this).closest(".accomodation_editor_body").remove();
            },
        });
        var accomodationscript = $("<script>")
            .text(`$("#${accomodationEditorId}").summernote({
            height: 300,
          
        });`);

        accomodationEditorContainer.append(
            accomodationinput,
            acc_img_input,
            accomodationtextarea,
            accomodationscript,
            accomodationdeleteBtn
        );
        $("#accomodation_editors_container").append(
            accomodationEditorContainer
        );
        acc_img_input.dropify();
    });
});

$(document).ready(function () {
    $("#add_destination_form").validate({
        ignore: [],
        rules: {
            title: {
                required: true,
            },
            cover_img: {
                required: true,
            },
            banner_img: {
                required: true,
            },
            short_description: {
                required: true,
            },
            type: {
                required: true,
            },
            category: {
                required: true,
            },

            departure_location: {
                required: true,
            },
            departure_date_time: {
                required: true,
            },

            // destination_list: {
            //     required: true,
            // },
            single_room_price: {
                required: true,
            },

            include_in: {
                required: true,
            },
            not_include: {
                required: true,
            },
            days: {
                required: true,
            },
            nights: {
                required: true,
            },
            destination_country: {
                required: true,
            },
            final_price: {
                required: true,
            },
            two_final_price: {
                required: true,
            },
            three_to_four_final_price: {
                required: true,
            },
            five_to_six_final_price: {
                required: true,
            },
            six_plus_final_price: {
                required: true,
            },

            // "city[]": {
            //     required: true,
            // },
            // "destination_list[]": {
            //     required: true,
            // },

            alt_text_cover_image: {
                required: true,
            },
            alt_text_banner_image: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            // console.log("work");

            //old script

            // let editorData = [];
            // $(".note-editor").each(function (index) {
            //     let editorId = $(this).attr("id");
            //     let editorContent = $(
            //         "#" + editorId + " .note-editable"
            //     ).html();
            //     editorData.push({ id: editorId, content: editorContent });
            // });

            let editorData = [];
            $("#editors-container .editor_body").each(function () {
                var dayInput = $(this).find("input[type='text']");
                // var fileInput = $(this).find("input[type='file']");
                var textarea = $(this).find("textarea");

                var day = dayInput.val();
                // var fileName = fileInput.val().split("\\").pop(); // get filename only
                var content = textarea.summernote("code");

                editorData.push({
                    day: day,
                    content: content,
                });
            });

            // Convert the array of editor data to JSON
            let jsonData = JSON.stringify(editorData);

            // Store the JSON data in the hidden input field
            $("#editor-data").val(jsonData);

            let accomodationAddEditorData = [];
            $("#accomodation_editors_container .accomodation_editor_body").each(
                function () {
                    var accomodation_input = $(this).find("input[type='text']");
                    var accomodation_textarea = $(this).find("textarea");

                    var accomodation_day = accomodation_input.val();
                    var accomodation_content =
                        accomodation_textarea.summernote("code");

                    accomodationAddEditorData.push({
                        accomodation_day: accomodation_day,
                        accomodation_content: accomodation_content,
                    });
                }
            );

            // Convert the array of editor data to JSON
            let accomodationAddJsonData = JSON.stringify(
                accomodationAddEditorData
            );

            // Store the JSON data in the hidden input field
            $("#accomodation-editor-data").val(accomodationAddJsonData);

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();

                    $(".form_process_loader").addClass("d-none");
                    showAlert("Destination added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    showAlert(
                        "There is an error.contact your support team !",
                        "error"
                    );
                },
            });
        },
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_destination")) {
        const destinationId = event.target.getAttribute("data-id");
        console.log(destinationId);
        document
            .querySelector("#destination_confirmDeleteBtn")
            .setAttribute("data-id", destinationId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#destination_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Destination deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_destination")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".destination_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting Destination.",
                    "error"
                );
            },
        });
    });
});

document.addEventListener("change", function (event) {
    if (event.target.classList.contains("destination_switch_input")) {
        const destinationId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/status/${destinationId}`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { status: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});

document.addEventListener("change", function (event) {
    if (event.target.classList.contains("destination_popular_switch_input")) {
        const popularDestinationId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/popular/${popularDestinationId}`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { popular: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});

document.addEventListener("change", function (event) {
    if (event.target.classList.contains("destination_trending_switch_input")) {
        const trendingDestinationId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/trending/${trendingDestinationId}`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { trending: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});

document.addEventListener("change", function (event) {
    if (event.target.classList.contains("destination_private_switch_input")) {
        const privateDestinationId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/private/${privateDestinationId}`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            data: { trending: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});

// old data

// $(document).ready(function () {
//     // Parse the existing editor data from the hidden input
//     var existingEditorData = JSON.parse($("#update-editor-data").val());

//     // Initialize the editorCount based on the existing editor count
//     var editorCount = existingEditorData.length + 1;

//     // Create editors based on existing data
//     existingEditorData.forEach(function (data, index) {
//         var editorId = "custom-section-editor" + (index + 1);

//         var editorContainer = $("<div>", {
//             class: "card-body editor_body",
//         });
//         var label = $("<label>", {
//             for: editorId,
//             text: "Day " + (index + 1),
//         });
//         var textarea = $("<textarea>", {
//             id: editorId,
//             name: "section[" + (index + 1) + "]",
//             style: "display: none;",
//             "aria-hidden": "true",
//         });
//         var script = $("<script>").text(`$("#${editorId}").summernote({
//             height: 300,

//         });`);

//         editorContainer.append(label, textarea, script);
//         $("#update-editors-container").append(editorContainer);

//         // Set the content of the editor
//         $("#" + editorId).summernote("code", data.content);
//     });

//     // Add functionality to add more editors
//     $("#add-editor-btn-update").on("click", function () {
//         var editorId = "custom-section-editor" + editorCount;

//         var editorContainer = $("<div>", {
//             class: "card-body",
//         });
//         var label = $("<label>", {
//             for: editorId,
//             text: "Day " + editorCount,
//         });
//         var textarea = $("<textarea>", {
//             id: editorId,
//             name: "section[" + editorCount + "]",
//             style: "display: none;",
//             "aria-hidden": "true",
//         });
//         var script = $("<script>").text(`$("#${editorId}").summernote({
//             height: 300,

//         });`);

//         editorContainer.append(label, textarea, script);
//         $("#update-editors-container").append(editorContainer);

//         // Increment the editor count for the next editor
//         editorCount++;
//     });
// });

// new data

$(document).ready(function () {
    // Parse the existing editor data from the hidden input
    var existingEditorData = JSON.parse($("#update-editor-data").val());

    // Initialize the editorCount based on the existing editor count
    var editorCount = existingEditorData.length + 1;

    // Create editors based on existing data
    existingEditorData.forEach(function (data, index) {
        var editorId = "custom-section-editor" + (index + 1);

        var editorContainer = $("<div>", {
            class: "card-body editor_body",
            id: "editor-container-" + (index + 1),
        });
        var input = $("<input>", {
            type: "text", // or "hidden" if you want it hidden
            id: "input_" + editorId,
            name: "day[" + (index + 1) + "]",
            value: data["day"],
        });
        var previewImageBox = $("<div>", {
            class: "image-preview-box mb-2",
        }).append(
            $("<img>", {
                src: "/storage/destinations/" + data["main_img"],
                alt: "Current Image",
                style: "max-width: 200px; display: block; border-radius: 5px; box-shadow: 0 0 5px rgba(0,0,0,0.1);",
            })
        );
        var img_input = $("<input>", {
            type: "file",
            name: "main_image[" + (index + 1) + "]",
            class: "dropify",
            "data-max-file-size": "2M",
            "data-allowed-file-extensions": "jpg jpeg png webp",
            value: data["main_img"],
        });
        var textarea = $("<textarea>", {
            id: editorId,
            name: "section[" + (index + 1) + "]",
            style: "display: none;",
            "aria-hidden": "true",
        });
        var updateDeleteBtn = $("<button>", {
            type: "button",
            class: "custom_btn_1",
            id: "delete-btn-" + (index + 1),
            text: "Delete",
            click: function () {
                $(this).closest(".editor_body").remove();
            },
        });
        var script = $("<script>").text(`$("#${editorId}").summernote({
            height: 300,
           
        });`);

        editorContainer.append(
            input,
            previewImageBox,
            img_input,
            textarea,
            script,
            updateDeleteBtn
        );
        $("#update-editors-container").append(editorContainer);

        // Set the content of the editor
        $("#" + editorId).summernote("code", data.content);
        $(".dropify").dropify();
    });

    // Add functionality to add more editors
    $("#add-editor-btn-update").on("click", function () {
        var editorId = "custom-section-editor" + editorCount;
        var updateDeleteBtnId = "delete-btn-" + editorCount;
        var editorContainer = $("<div>", {
            class: "card-body editor_body",
            id: "editor-container-" + editorCount,
        });
        var input = $("<input>", {
            type: "text", // or "hidden" if you want it hidden
            id: "input_" + editorId,
            name: "day[" + editorCount + "]",
            value: "Day " + editorCount,
        });
        var img_input = $("<input>", {
            type: "file",
            name: "main_image[" + editorCount + "]",
            class: "dropify",
            "data-max-file-size": "2M",
            "data-allowed-file-extensions": "jpg jpeg png webp",
            value: "",
        });
        var textarea = $("<textarea>", {
            id: editorId,
            name: "section[" + editorCount + "]",
            style: "display: none;",
            "aria-hidden": "true",
        });
        var updateDeleteBtn = $("<button>", {
            type: "button",
            class: "custom_btn_1",
            id: updateDeleteBtnId,
            text: "Delete",
            click: function () {
                $(this).closest(".editor_body").remove();
            },
        });
        var script = $("<script>").text(`$("#${editorId}").summernote({
            height: 300,
           
        });`);

        editorContainer.append(
            input,
            img_input,
            textarea,
            script,
            updateDeleteBtn
        );
        $("#update-editors-container").append(editorContainer);
        img_input.dropify();
        // Increment the editor count for the next editor
        editorCount++;
    });
});

$(document).ready(function () {
    // Parse the existing editor data from the hidden input
    var rawData = $("#update_accomodation_editor_data").val();
    if (rawData !== null && rawData !== "") {
        var existingAccomodationEditorData = JSON.parse(
            $("#update_accomodation_editor_data").val()
        );

        // Initialize the accomodationEditorCount based on the existing editor count
        var accomodationEditorCount = existingAccomodationEditorData.length + 1;

        // Create editors based on existing data
        existingAccomodationEditorData.forEach(function (data, index) {
            var accomodationEditorId =
                "custom-section-accomodation-editor" + (index + 1);

            var accomodationEditorContainer = $("<div>", {
                class: "accomodation-card-body accomodation_editor_body",
                id: "accomodation-editor-container-" + (index + 1),
            });
            var accomodation_input = $("<input>", {
                type: "text", // or "hidden" if you want it hidden
                id: "input_" + accomodationEditorId,
                name: "day[" + (index + 1) + "]",
                value: data["day"],
            });
            var previewImageBox = $("<div>", {
                class: "image-preview-box mb-2",
            }).append(
                $("<img>", {
                    src: "/storage/destinations/" + data["main_img"],
                    alt: "Current Image",
                    style: "max-width: 200px; display: block; border-radius: 5px; box-shadow: 0 0 5px rgba(0,0,0,0.1);",
                })
            );
            var acc_img_input = $("<input>", {
                type: "file",
                name: "acc_main_image[" + (index + 1) + "]",
                class: "dropify",
                "data-max-file-size": "2M",
                "data-allowed-file-extensions": "jpg jpeg png webp",
                "data-default-file":
                    "{{ $destination ? asset('storage/destinations/') : '' }}/" +
                    data["main_img"],
            });
            var accomodation_textarea = $("<textarea>", {
                id: accomodationEditorId,
                name: "accomodation_section[" + (index + 1) + "]",
                style: "display: none;",
                "aria-hidden": "true",
            });
            var accomodationUpdateDeleteBtn = $("<button>", {
                type: "button",
                class: "custom_btn_1",
                id: "accomodation-delete-btn-" + (index + 1),
                text: "Delete",
                click: function () {
                    $(this).closest(".accomodation_editor_body").remove();
                },
            });
            var accomodation_script = $("<script>")
                .text(`$("#${accomodationEditorId}").summernote({
            height: 300,
           
        });`);

            accomodationEditorContainer.append(
                accomodation_input,
                previewImageBox,
                acc_img_input,
                accomodation_textarea,
                accomodation_script,
                accomodationUpdateDeleteBtn
            );
            $("#update_accomodation_editors_container").append(
                accomodationEditorContainer
            );

            // Set the content of the editor
            $("#" + accomodationEditorId).summernote("code", data.content);
            $(".dropify").dropify();
        });
    } else {
        var accomodationEditorCount = 1;
    }
    // Add functionality to add more editors
    $("#add-accomodation-editor-btn-update").on("click", function () {
        var accomodationEditorId =
            "custom-section-accomodation-editor" + accomodationEditorCount;
        var accomodationUpdateDeleteBtnId =
            "accomodation-delete-btn-" + accomodationEditorCount;
        var accomodationEditorContainer = $("<div>", {
            class: "accomodation-card-body accomodation_editor_body",
            id: "accomodation-editor-container-" + accomodationEditorCount,
        });
        var accomodation_input = $("<input>", {
            type: "text", // or "hidden" if you want it hidden
            id: "input_" + accomodationEditorId,
            name: "day[" + accomodationEditorCount + "]",
            value: "Day " + accomodationEditorCount,
        });
        var acc_img_input = $("<input>", {
            type: "file",
            name: "acc_main_image[" + accomodationEditorCount + "]",
            class: "dropify",
            "data-max-file-size": "2M",
            "data-allowed-file-extensions": "jpg jpeg png webp",
        });
        var accomodation_textarea = $("<textarea>", {
            id: accomodationEditorId,
            name: "accomodation_section[" + accomodationEditorCount + "]",
            style: "display: none;",
            "aria-hidden": "true",
        });
        var accomodationUpdateDeleteBtn = $("<button>", {
            type: "button",
            class: "custom_btn_1",
            id: accomodationUpdateDeleteBtnId,
            text: "Delete",
            click: function () {
                $(this).closest(".accomodation_editor_body").remove();
            },
        });
        var accomodation_script = $("<script>")
            .text(`$("#${accomodationEditorId}").summernote({
            height: 300,
           
        });`);

        accomodationEditorContainer.append(
            accomodation_input,
            acc_img_input,
            accomodation_textarea,
            accomodation_script,
            accomodationUpdateDeleteBtn
        );
        $("#update_accomodation_editors_container").append(
            accomodationEditorContainer
        );
        acc_img_input.dropify();
        // Increment the editor count for the next editor
        accomodationEditorCount++;
    });
});

$(document).ready(function () {
    $("#update_destination_form").validate({
        ignore: [],
        rules: {
            title: {
                required: true,
            },

            short_description: {
                required: true,
            },
            departure_location: {
                required: true,
            },
            departure_date_time: {
                required: true,
            },
            return_date_time: {
                required: true,
            },

            single_room_price: {
                required: true,
            },

            include_in: {
                required: true,
            },
            not_include: {
                required: true,
            },
            days: {
                required: true,
            },
            nights: {
                required: true,
            },
            destination_country: {
                required: true,
            },
            final_price: {
                required: true,
            },
            two_final_price: {
                required: true,
            },
            three_to_four_final_price: {
                required: true,
            },
            five_to_six_final_price: {
                required: true,
            },
            six_plus_final_price: {
                required: true,
            },

            // "city[]": {
            //     required: true,
            // },
            // "destination_list[]": {
            //     required: true,
            // },

            alt_text_cover_image: {
                required: true,
            },
            alt_text_banner_image: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
          //  console.log(form);

            let editorData = [];
            $("#update-editors-container .card-body").each(function () {
                var dayInput = $(this).find("input[type='text']");
                //   var fileInput = $(this).find("input[type='file']");
                var textarea = $(this).find("textarea");

                var day = dayInput.val();
                //  var fileName = fileInput.val().split("\\").pop(); // get filename only
                var content = textarea.summernote("code");

                editorData.push({
                    day: day,
                    content: content,
                    //    main_image: fileName, // optional
                });
            });

            // Convert the array of editor data to JSON
            let jsonData = JSON.stringify(editorData);

            // Store the JSON data in the hidden input field
            $("#update-editor-data").val(jsonData);

            let accomodationEditorData = [];
            $(
                "#update_accomodation_editors_container .accomodation-card-body"
            ).each(function () {
                var accomodation_input = $(this).find("input");
                var accomodation_textarea = $(this).find("textarea");

                var accomodation_day = accomodation_input.val();
                var accomodation_content =
                    accomodation_textarea.summernote("code");

                accomodationEditorData.push({
                    accomodation_day: accomodation_day,
                    accomodation_content: accomodation_content,
                });
            });

            // Convert the array of editor data to JSON
            let accomodationJsonData = JSON.stringify(accomodationEditorData);

            // Store the JSON data in the hidden input field
            $("#update_accomodation_editor_data").val(accomodationJsonData);

            let baseURL = window.location.origin;
            let id = $(form).find('[name="id"]').val();
            let apiEndpoint = "/api/destination/update/" + id;
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();

                    $(".form_process_loader").addClass("d-none");
                    showAlert("Destination updated successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    showAlert(
                        "There is an error.contact your support team !",
                        "error"
                    );
                },
            });
        },
    });
});

$(document).ready(function () {
    $("#destination_add_category_form").validate({
        rules: {
            title: {
                required: true,
            },
            cover_img: {
                required: true,
            },
            banner_img: {
                required: true,
            },
            page_h1_heading: {
                required: true,
            },
            meta_title: {
                required: true,
            },
            meta_description: {
                required: true,
            },
            meta_keywords: {
                required: true,
            },
            alt_text_cover_image: {
                required: true,
            },
            alt_text_banner_image: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/category/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById(
                            "offcanvasAddDestinationCategory"
                        )
                    );
                    offcanvas.hide();
                    showAlert("Category added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasAddDestinationCategory"
                            )
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("Category already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    }
                },
            });
        },
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_destination_category")) {
        const destinationCategoryId = event.target.getAttribute("data-id");
        console.log(destinationCategoryId);
        document
            .querySelector("#destination_cat_confirmDeleteBtn")
            .setAttribute("data-id", destinationCategoryId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#destination_cat_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/category/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Category deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_destination_category")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".category_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $(".edit_destination_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
       // console.log(formId);

        $("#" + formId).validate({
            rules: {
                title: {
                    required: true,
                },
                page_h1_heading: {
                    required: true,
                },
                meta_title: {
                    required: true,
                },
                meta_description: {
                    required: true,
                },
                meta_keywords: {
                    required: true,
                },
                alt_text_cover_image: {
                    required: true,
                },
                alt_text_banner_image: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/destination/category/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasUpdateCategory" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("Category updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateCategory" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("Category already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        }
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $("#destination_add_type_form").validate({
        rules: {
            title: {
                required: true,
            },
            cover_img: {
                required: true,
            },
            banner_img: {
                required: true,
            },
            page_h1_heading: {
                required: true,
            },
            meta_title: {
                required: true,
            },
            meta_description: {
                required: true,
            },
            meta_keywords: {
                required: true,
            },
            alt_text_cover_image: {
                required: true,
            },
            alt_text_banner_image: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/type/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasAddDestinationType")
                    );
                    offcanvas.hide();
                    showAlert("Type added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasAddDestinationType"
                            )
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("Type already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    }
                },
            });
        },
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_destination_type")) {
        const destinationTypeId = event.target.getAttribute("data-id");
        console.log(destinationTypeId);
        document
            .querySelector("#destination_type_confirmDeleteBtn")
            .setAttribute("data-id", destinationTypeId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#destination_type_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/type/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Type deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_destination_type")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".type_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $(".edit_destination_type_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
      //  console.log(formId);

        $("#" + formId).validate({
            rules: {
                title: {
                    required: true,
                },
                page_h1_heading: {
                    required: true,
                },
                meta_title: {
                    required: true,
                },
                meta_description: {
                    required: true,
                },
                meta_keywords: {
                    required: true,
                },
                alt_text_cover_image: {
                    required: true,
                },
                alt_text_banner_image: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/destination/type/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasUpdateType" + id)
                        );
                        offcanvas.hide();
                        showAlert("Type updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateCategory" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("Type already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $("#destination_add_country_location_form").validate({
        rules: {
            country: {
                required: true,
            },
            city: {
                required: true,
            },
            locations: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/country&locations/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById(
                            "offcanvasAddDestinationCountryLocation"
                        )
                    );
                    offcanvas.hide();
                    showAlert(
                        "contry & locations added successfull !",
                        "success"
                    );
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasAddDestinationCountryLocation"
                            )
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("country already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    $(".edit_country_location_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
       // console.log(formId);

        $("#" + formId).validate({
            rules: {
                country: {
                    required: true,
                },
                city: {
                    required: true,
                },
                locations: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint =
                    "/api/destination/country&locations/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasUpdateCountryAndLocations" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("data updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateCountryAndLocations" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("country already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_country_location")) {
        const destinationCountryLocationId =
            event.target.getAttribute("data-id");
    //    console.log(destinationCountryLocationId);
        document
            .querySelector("#country_location_confirmDeleteBtn")
            .setAttribute("data-id", destinationCountryLocationId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#country_location_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/country&locations/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert(
                    "Country & locations deleted successfully!",
                    "success"
                );
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_country_location")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".country_location_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $("#destination_add_include_form").validate({
        rules: {
            title: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/includes/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasAddIncludes")
                    );
                    offcanvas.hide();
                    showAlert("added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasAddIncludes")
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    $(".edit_include_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
    //    console.log(formId);

        $("#" + formId).validate({
            rules: {
                title: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/destination/includes/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasUpdateIncludes" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("data updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateIncludes" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_include")) {
        const destinationIncludeId = event.target.getAttribute("data-id");
   //     console.log(destinationIncludeId);
        document
            .querySelector("#include_confirmDeleteBtn")
            .setAttribute("data-id", destinationIncludeId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#include_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/includes/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_include")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".include_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $("#destination_add_not_include_form").validate({
        rules: {
            title: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/notincludes/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasAddNotIncludes")
                    );
                    offcanvas.hide();
                    showAlert("added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasAddNotIncludes")
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    $(".edit_not_include_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
    //    console.log(formId);

        $("#" + formId).validate({
            rules: {
                title: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/destination/notincludes/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasUpdateNotInclude" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("data updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateNotInclude" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_not_include")) {
        const destinationNotIncludeId = event.target.getAttribute("data-id");
    //    console.log(destinationNotIncludeId);
        document
            .querySelector("#not_include_confirmDeleteBtn")
            .setAttribute("data-id", destinationNotIncludeId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#not_include_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/notincludes/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_not_include")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".not_include_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $("#destination_add_extras_form").validate({
        rules: {
            title: {
                required: true,
            },
            price: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/extras/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasAddExtras")
                    );
                    offcanvas.hide();
                    showAlert("added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasAddExtras")
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("already exists.", "error");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    $(".edit_extras_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
   //     console.log(formId);

        $("#" + formId).validate({
            rules: {
                title: {
                    required: true,
                },
                price: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/destination/extras/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasUpdateExtras" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("data updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdateExtras" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_extras")) {
        const destinationExtraId = event.target.getAttribute("data-id");
     //   console.log(destinationExtraId);
        document
            .querySelector("#extras_confirmDeleteBtn")
            .setAttribute("data-id", destinationExtraId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector("#extras_confirmDeleteBtn");
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/extras/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_extras")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".extras_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $("#store_slots").validate({
        rules: {
            date: {
                required: true,
            },
            slots: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let apiEndpoint = "/api/destination/slot/store";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasStoreSlots")
                    );
                    offcanvas.hide();
                    showAlert("added successfull !", "success");
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);

                    if (xhr.status === 422) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasStoreSlots")
                        );
                        offcanvas.hide();
                        $(".form_process_loader").addClass("d-none");
                        showAlert("slots updated.", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "There is an error.contact your support team !",
                            "error"
                        );
                        // setTimeout(function () {
                        //     window.location.reload();
                        // }, 2000);
                    }
                },
            });
        },
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_slot")) {
        const destinationSlotsId = event.target.getAttribute("data-id");
    //    console.log(destinationSlotsId);
        document
            .querySelector("#destination_slots_confirmDeleteBtn")
            .setAttribute("data-id", destinationSlotsId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector(
        "#destination_slots_confirmDeleteBtn"
    );
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/destination/slot/delete/${getId}`;
        let apiUrl = baseURL + apiEndpoint;
        $(".form_process_loader").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            headers: {
                "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr("content"),
            },
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("deleted successfully!", "success");
                $(".form_process_loader").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_slot")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".destination_row").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                $(".form_process_loader").addClass("d-none");
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting category.",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $("#update_booking_detail").validate({
        rules: {
            tickets: {
                required: true,
            },

            adultes: {
                required: true,
            },

            children: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();

            let baseURL = window.location.origin;
            let id = $(form).find('[name="id"]').val();
         //   console.log(id);
            let apiEndpoint = "/api/destination/booking/detail/update/" + id;
            let apiUrl = baseURL + apiEndpoint;

            var formData = new FormData(form); // Create FormData object from the form
            //  console.log(formData);
            $(".form_process_loader").removeClass("d-none");

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    // console.log(response);
                    form.reset();

                    $(".form_process_loader").addClass("d-none");
                    showAlert(
                        "booking detail updated successfull !",
                        "success"
                    );
                    setTimeout(function () {
                        window.location.reload();
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    showAlert(
                        "There is an error.contact your support team !",
                        "error"
                    );
                },
            });
        },
    });
});

$(document).ready(function () {
    $(".edit_payment_btn").click(function () {
        // Get the form ID from the clicked button
        var formId = $(this).data("form-id");
        let id = $(this).data("id");
   //     console.log(formId);

        $("#" + formId).validate({
            rules: {
                receive_amount: {
                    required: true,
                },
                charges: {
                    required: true,
                },
            },

            submitHandler: function (form, e) {
                e.preventDefault();

                let baseURL = window.location.origin;

                let apiEndpoint = "/api/payment/update/" + id;
                let apiUrl = baseURL + apiEndpoint;
                var formData = new FormData(form); // Create FormData object from the form
                //  console.log(formData);
                // Update FormData with the current form values
                formData.append("id", id);
                $(".form_process_loader").removeClass("d-none");

                $.ajax({
                    url: apiUrl,
                    type: "POST",
                    data: formData,
                    processData: false, // Important! Don't process the data
                    contentType: false, // Important! Set content type to false
                    dataType: "json",
                    success: function (response) {
                        // Handle success response
                        // console.log(response);
                        form.reset();
                        $(".form_process_loader").addClass("d-none");
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById(
                                "offcanvasUpdatePayment" + id
                            )
                        );
                        offcanvas.hide();
                        showAlert("data updated successfull !", "success");
                        setTimeout(function () {
                            window.location.reload();
                        }, 2000);
                    },
                    error: function (xhr, status, error) {
                        console.error(xhr);
                        if (xhr.status === 422) {
                            form.reset();

                            // Close the offcanvas
                            var offcanvas = bootstrap.Offcanvas.getInstance(
                                document.getElementById(
                                    "offcanvasUpdatePayment" + id
                                )
                            );
                            offcanvas.hide();
                            $(".form_process_loader").addClass("d-none");
                            showAlert("already exists.", "error");
                            setTimeout(function () {
                                window.location.reload();
                            }, 2000);
                        } else {
                            $(".form_process_loader").addClass("d-none");
                            showAlert(
                                "There is an error.contact your support team !",
                                "error"
                            );
                            // setTimeout(function () {
                            //     window.location.reload();
                            // }, 2000);
                        }
                    },
                });
            },
        });
    });
});

$(document).ready(function () {
    $(document).on("click", "#duplicate_record", function (e) {
        e.preventDefault();

        let id = $(this).data("id");
        let baseURL = window.location.origin;
        let apiEndpoint = "/api/destination/duplicate/" + id;
        let apiUrl = baseURL + apiEndpoint;

        $(".form_process_loader").removeClass("d-none");

        $.ajax({
            url: apiUrl,
            type: "POST",
            data: {
                _token: $('meta[name="csrf-token"]').attr("content"),
                id: id,
            },
            dataType: "json",
            success: function (response) {
                $(".form_process_loader").addClass("d-none");
                showAlert("Record duplicated successfully!", "success");
                setTimeout(function () {
                    window.location.reload();
                }, 2000);
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                $(".form_process_loader").addClass("d-none");
                showAlert(
                    "There was an error. Please contact your support team!",
                    "error"
                );
            },
        });
    });
});

$(document).ready(function () {
    $("#pricing_flag").on("change", function () {
        var isActive = $(this).val() == "1";

        if (isActive) {
            $(".group_pricing_fields").hide();
        } else {
            $(".group_pricing_fields").show();
        }
    });

    // Optional: Trigger change on load to apply correct visibility
    $("#pricing_flag").trigger("change");
});
