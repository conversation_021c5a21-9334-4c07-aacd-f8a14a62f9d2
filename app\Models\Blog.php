<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Blog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'title',
        'slug',
        'tags',
        'seo_keywords',
        'short_description',
        'description',
        'author_name',
        'status',
        'popular',
        'cover_image',
        'type',
        'author_image',
        'banner_image',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'page_h1_heading',
        'alt_text_cover_image',
        'alt_text_banner_image',
        'alt_text_author_image',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function category()
    {
        return $this->belongsTo(BlogCategory::class, 'category_id');
    }

    public function comments()
    {
        return $this->hasMany(BlogComment::class, 'blog_id')->where('status', 1);
    }

}
