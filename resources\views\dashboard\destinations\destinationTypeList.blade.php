@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / destinations / type / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddDestinationType"><span>Add
                    Type</span></button>
        </div>
        <table id="destination_type_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">Image</th>
                    <th style="min-width: 330px;">Title</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($destinationTypeList as $destinationType)
                <tr class="type_row">
                    <td></td>

                    <td>
                        <div style="width: 2.375rem;
                      height: 2.375rem;">
                            <img src="{{$destinationType ? asset('storage/destinations/'.$destinationType->cover_image) : ''}}"
                                class="w-100 h-100">
                        </div>
                    </td>

                    <td>{{ $destinationType->title }}</td>

                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $destinationType->id }}" type="button"
                                    class="delete_btn delete_destination_type"><i
                                        class="fas fa-trash-alt"></i>Delete</button>

                                <button class="update_btn edit_destination_type_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasUpdateType{{$destinationType->id}}"
                                    data-form-id="update_destination_type_{{$destinationType->id}}"
                                    data-id="{{$destinationType->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                            </div>
                        </div>
                        @include("dashboard.destinations.updateType")

                    </td>
                </tr>

                @endforeach
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.destinations.addType")
@include("dashboard.destinations.deleteType")

@endsection
