<div class="offcanvas offcanvas-end" tabindex="-1"
    id="offcanvasUpdateCountryAndLocations{{$destinationCountryAndLocations->id}}"
    aria-labelledby="offcanvasUpdateCountryAndLocations{{$destinationCountryAndLocations->id}}Label">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 id="offcanvasUpdateCountryAndLocations{{$destinationCountryAndLocations->id}}Label" class="offcanvas-title">
            Update Country and Locations
        </h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="update_country_locations_{{$destinationCountryAndLocations->id}}"
            enctype="multipart/form-data">
            @csrf

            <div class="d-grid gap-3 w-100">

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="country">Destination Country</label>
                    <input class="custom_form_field" name="country" id="country"
                        value="{{$destinationCountryAndLocations->country}}" />
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="city">Destination city or area</label>
                    <input class="custom_form_field" placeholder="Enter city name" name="city" id="city"
                        value="{{$destinationCountryAndLocations->city}}" />
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="locations">Destination locations</label>
                    <input class="custom_tags_input custom_form_field" name="locations" id="locations"
                        value="{{$destinationCountryAndLocations->locations}}" />
                </div>
                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button type="submit" class="custom_btn_2">Update
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>
        </form>
    </div>
</div>
