@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / destinations / not includes / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddNotIncludes"><span>Add
                </span></button>
        </div>
        <table id="destination_not_includes_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 120px;">Title</th>
                    <th style="min-width: 16px; text-align:right;">Action</th>
                </tr>
            </thead>
            <tbody>
            @if($destinationNotIncludes && $destinationNotIncludes->isNotEmpty())
                @foreach($destinationNotIncludes as $destinationNotIncludeData)
                <tr class="not_include_row">
                    <td></td>
                    <td>{{ $destinationNotIncludeData->title }}</td>

                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $destinationNotIncludeData->id }}" type="button"
                                    class="delete_btn delete_not_include"><i
                                        class="fas fa-trash-alt"></i>Delete</button>

                                <button class="update_btn edit_not_include_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasUpdateNotInclude{{$destinationNotIncludeData->id}}"
                                    data-form-id="update_not_includes_{{$destinationNotIncludeData->id}}"
                                    data-id="{{$destinationNotIncludeData->id}}">
                                    <i class="far fa-edit"></i> Update
                            </div>
                            </button>
                            @include("dashboard.destinations.destinationNotIncludes.update")
                        </div>
                    </td>
                </tr>

                @endforeach
                @endif
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.destinations.destinationNotIncludes.add")
@include("dashboard.destinations.destinationNotIncludes.delete")

@endsection
