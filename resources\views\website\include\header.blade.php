<header class="header_parent">
  <div class="header_ticker d-none">

    <div class="ticker_text d-flex align-items-center justify-content-center flex-wrap"><span class="text-center">Your
        Destination Awaits, Our Service Elevates Your Journey</span>
    </div>
    <a href="{{route('destinations')}}"
      class="d-none res_header_trip_booking_btn w-100 align-items-center justify-content-center">
      <small class="d-flex w-100 align-items-center justify-content-center">Book Your Trip</small>
    </a>

  </div>
  <section class="desk_header d-lg-block">

    <div class="custom_container">
        <div class="d-flex align-items-center justify-content-between">
          <div class="d-lg-flex d-none header_links_list align-items-center ">
            <a href="/"> Home</a>
            <a href="{{route('destinations')}}"> Destinations</a>
            <a href="/about"> About </a>
            <a href="/contact"> Contact </a>
          </div>

          <div class="d-lg-none d-flex">
            <a class="header_toggle_btn"><i class="far fa-bars"></i></a>
          </div>
          <a href="/" class="header_logo">
            <img src="{{asset('website/images/logo.webp')}}" width="160" height="55" class="object-fit-contain w-100" alt="Logo"
            decoding="async"
            fetchpriority="high" />
          </a>
          <div class="header_social_link d-flex align-items-center">
            <a class="d-flex align-items-center" style="margin-right: 20px;" data-bs-toggle="offcanvas"
              data-bs-target="#offcanvasTop" aria-controls="offcanvasTop">
              <i class="fas fa-search"></i>
            </a>

            @php($user = auth()->user())

            @if(!$user)
            <a href="{{route('login')}}" class="d-flex align-items-center">
              <i class="fa-regular fa-user"></i>
            </a>
            @else
            <a class="logined_btn d-flex align-items-center position-relative">
              <i class="fa-regular fa-user"></i>
              <i class="fas fa-caret-down logined_arrow"></i>
              <object class="login_drop_down">
                <div class="login_drop_down_list">
                  @if($user->role == 'admin' || ($user->subscriber && $user->subscriber->status == 1))
                  <a href="/dashboard/home">Dashboard</a>
                  @endif
                  <a href="{{route('website.profile.profile')}}">Profile</a>
                  <a id="logout_btn">Logout</a>
                </div>
              </object>
            </a>
            @endif

            <a href="{{route('destinations')}}"
              class="header_trip_booking_btn d-flex align-items-center justify-content-center">
              <small>Book Now</small>
            </a>
          </div>

        </div>
     
    </div>
  </section>

  <section class="listing_bar header_bar_2 d-lg-block d-none">
    <div class="custom_container">
      <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex header_links_list align-items-center">
          <a href="/subscription">Subscription</a>
          <a href="/blog">Blog </a>
          <a href="/destination/categories"> Destination Categories</a>
          <a href="/destination/types"> Destination Types</a>
          <a href="{{url('/home/<USER>')}}"> Trending Packages</a>
        </div>
        <div class="d-flex header_links_list align-items-center">
          <a href="tel:+****************"> <i class="fas fa-phone"></i> +****************</a>
          <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> <EMAIL></a>
        </div>
      </div>
    </div>
  </section>

  <div class="header_links d-lg-none d-flex">
    <div class="cross_btn"><i class="res_toggle_button fas fa-times"></i></div>
    <div class="header_links_list">

      <a href="/"> Home</a>
      <a href="{{route('destinations')}}"> Destinations</a>
      <a href="/destination/categories"> Destination Categories</a>
      <a href="/destination/types"> Destination Types</a>
      <a href="{{url('/home/<USER>')}}"> Trending Packages</a>
      <a href="/subscription"> Subscription</a>
      <a href="/about"> About </a>
      <a href="/contact"> Contact </a>
      <a href="/blog"> Blog </a>

    </div>
  </div>

</header>

<div class="offcanvas offcanvas-top" tabindex="-1" id="offcanvasTop" aria-labelledby="offcanvasTopLabel">
  <div class="offcanvas-header">
    <h5 id="offcanvasTopLabel">Search Your Destination</h5>
    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
  </div>
  <div class="offcanvas-body">
    <div class="search">
      <i class="fa fa-search"></i>
      <input id="search" type="text" class="form-control" placeholder="Search by destination name or country">

      <div class="destination_search" id="product-list"> </div>

    </div>
  </div>
</div>

<style>
  div#offcanvasTop {
    height: 400px;
  }

  .destination_search {
    display: grid;
    gap: 15px;
    width: 100%;
    padding: 15px 10px;
    position: absolute;
    top: 64px;
    background: #fff;
    box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
    height: 225px;
    overflow: scroll;
  }

  .search_listing {
    display: grid;
    gap: 5px;
    border-bottom: 1px solid #d9d9d9;
    padding: 0px 10px 10px 10px;
  }

  .search_listing h2 {
    font-size: 16px;
    font-weight: 500;
  }

  .search {
    position: relative;
    box-shadow: 0 0 40px rgba(51, 51, 51, .1);

  }


  .search input {

    height: 60px;
    text-indent: 25px;
    border: 2px solid #d6d4d4;


  }


  .search input:focus {

    box-shadow: none;
    border: 2px solid blue;


  }

  .search .fa-search {

    position: absolute;
    top: 20px;
    left: 16px;

  }

  .search button {

    position: absolute;
    top: 5px;
    right: 5px;
    height: 50px;
    width: 110px;
    background: blue;

  }
</style>