@extends("dashboard.include.layout")

@section("wrapper")


<div class="pagination-list d-flex w-100">
    <a>/ dashboard / booking / reports</a>
</div>
<div class="d-grid gap-4">
    <div class="content-section-box">

        <form action="{{ route('destination.booking.report') }}" method="get" novalidate="novalidate">

            <div class="d-flex mb-3 justify-content-end">
                <button type="submit" class="custom_btn_1">Apply</button>
            </div>
            <div class="d-flex flex-column">
                <div class="destination_single_box">
                    <div class="d-grid gap-3 w-100 bg_box">
                        <div class="card_heading">
                            <h5>Filter</h5>
                        </div>
                        <div class="d-grid gap-3 w-100">

                            @if(Auth::check() && Auth::user()->role == 'admin')
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="agents">Agent's</label>

                                <select name="agents" id="get_destination" class="custom_form_field">
                                    <option selected disabled>Select agent</option>
                                    @foreach($agents as $agent)
                                    <option value="{{ $agent->user->id }}">{{ $agent->user->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @endif

                            @if(Auth::check() && Auth::user()->role == 'admin')
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="destination">Destination</label>
                                <select name="destination" id="destinationResult"
                                    class="custom_form_field get_destination_dates">
                                    <option selected disabled>Select destination</option>
                                </select>
                            </div>
                            @else
                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="destination">Destination</label>

                                <select name="destination" class="custom_form_field get_destination_dates">
                                    <option selected disabled>Select destination</option>
                                    @foreach($destinations as $destination)
                                    <option value="{{ $destination->id }}">{{ $destination->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @endif

                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="date">Date</label>
                                <select name="dates" id="dates" class="custom_form_field">
                                    <option selected disabled>Select date</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>

    </div>


    <div class="row g-4 mb-4">

        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Number of Booking</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">@if(isset($bookings)){{count($bookings)}}@endif</h3>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Number of Payments</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">@if(isset($payments)){{count($payments)}}@endif</h3>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Total Payments</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">@if(isset($totalPayment)){{$totalPayment}}@endif</h3>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Strip Charges</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">@if(isset($charges)){{$charges}}@endif</h3>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Final Received payment</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">
                                    @if(isset($totalPaymentAfterCharges)){{$totalPaymentAfterCharges}}@endif</h3>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
          @if(Auth::check() && !(Auth::user()->role == 'admin'))
        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Admin Commision</span>
                            <div class="d-flex align-items-end mt-2">
                                <h3 class="mb-0 me-2">
                                    {{$commission->commission}} %
                                </h3>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
      
        
        
        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Commision Price</span>
                            <div class="d-flex align-items-end mt-2">
                                @if(isset($totalPaymentAfterCharges))
                                <h3 class="mb-0 me-2">
                                    <?php
  $commissionPercentage = floatval($commission->commission); // Commission percentage
 $totalPaymentAfterCharges = floatval($totalPaymentAfterCharges);
      
$commissionAmount = ($totalPaymentAfterCharges * $commissionPercentage) / 100;

?>
                                    {{$commissionAmount}}
                                   </h3>
                                @endif
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
         
        <div class="col-sm-6 col-xl-4">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span>Final payment after Commision</span>
                            <div class="d-flex align-items-end mt-2">
                                @if(isset($totalPaymentAfterCharges))
                                <h3 class="mb-0 me-2">
                                    <?php
  $commissionPercentage = floatval($globalData['commission_percentage']); // Commission percentage
 $totalPaymentAfterCharges = floatval($totalPaymentAfterCharges);
$commissionAmount = ($totalPaymentAfterCharges * $commissionPercentage) / 100;
$remainingPayment = $totalPaymentAfterCharges - $commissionAmount;
?>
                                    {{$remainingPayment}}</h3>
                                @endif
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
         @endif
    </div>
    
    
    <div class="datatable_parent">
        @if(isset($bookings))
        <table id="destination_category_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">Destination Title</th>
                    <th style="min-width: 100px;">Booked User</th>
                    <th style="min-width: 100px;">Booked Date</th>
                    <th style="min-width: 100px;">Tickets</th>
                    <th style="min-width: 100px;">Total Price</th>

                    <th style="min-width: 100px;">Paid price</th>
                    <th style="min-width: 100px;">Booking Date</th>
                    <!-- Add more table headers as needed -->
                </tr>
            </thead>
            <tbody>
                @foreach($bookings as $booking)
                <tr class="category_row">
                    <td></td>
                    <td>{{ $booking->destination->title }}</td>
                    <td>{{ $booking->bookedBy->name }}</td>
                    <td>{{ $booking->date }}</td>
                    <td>{{ $booking->tickets }}</td>
                    <td>{{ $booking->destination->final_price }}</td>
                    <td>{{ $booking->destination->price }}</td>
                    <td>{{ $booking->created_at->format('Y-m-d') }}</td>
                    <!-- Add more table cells to display other booking data -->
                </tr>
                @endforeach
            </tbody>
        </table>
        @else
        <p>No bookings found.</p>
        @endif
    </div>

    <div class="datatable_parent">
        @if(isset($payments))
        <table id="destination_category_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">paid User</th>
                    <th style="min-width: 100px;">Payment Type</th>
                    <th style="min-width: 100px;">Price</th>
                    <th style="min-width: 100px;">Direct Payment slip</th>

                    <!-- Add more table headers as needed -->
                </tr>
            </thead>
            <tbody>
                @foreach($payments as $paymentsDetail)
                <tr class="category_row">
                    <td></td>
                    <td>{{ $paymentsDetail->bookedBy->name }}</td>
                    <td>{{ $paymentsDetail->payment_type }}</td>
                    <td>{{ $paymentsDetail->price }}</td>
                    <td>
                        <a href="{{$paymentsDetail ? asset('storage/directPaymentSlip/'.$paymentsDetail->direct_slip) : ''}}"
                            style="display:block;width: 30px;">

                            @if (!empty($paymentsDetail->direct_slip))
                            <img src="{{ asset('storage/directPaymentSlip/' . $paymentsDetail->direct_slip) }}"
                                class="w-100 h-100" />
                            @else
                            Strip Payment
                            @endif

                        </a>

                    </td>

                    <!-- Add more table cells to display other booking data -->
                </tr>
                @endforeach
            </tbody>
        </table>
        @else
        <p>No payment found.</p>
        @endif
    </div>
</div>

@endsection