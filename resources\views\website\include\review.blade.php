<section class="review_section d-inline-block w-100 float-left position-relative">
  <div class="review_ab_2 position-absolute bottom-0 left-0">
    <img src="{{asset('website/images/review_ab_1.webp')}}" width="300" height="300" class="w-100 float-left h-100 object-fit-cover" alt="reviews" loading="lazy" decoding="async" />
  </div>

  <div class="custom_container">
    <div class="position-relative">
      <div class="review_ab_3 position-absolute">
        <img src="{{asset('website/images/review_ab_2.webp')}}" width="300" height="300" class="w-100 float-left h-100 object-fit-cover" alt="reviews" loading="lazy" decoding="async" />
      </div>
      <div class="review_ab_4 position-absolute">
        <img src="{{asset('website/images/review_ab_3.webp')}}" width="150" height="150" class="w-100 float-left h-100 object-fit-cover" alt="reviews" loading="lazy" decoding="async" />
      </div>
      <div class="review_content">
        <div class="review_heading d-flex flex-column w-100 align-items-center text-center">
          <small>Promotion</small>
          <h3>See What Our Clients Say About Us</h3>
        </div>


        <div class="review_listing swiper review_slider">
          <ul class="swiper-wrapper">
            @foreach($reviews as $reviews)
            <li class="swiper-slide">
              <div class="review_single_list position-relative d-flex flex-column w-100 align-items-center text-center">
                <div class="review_ab_1 position-absolute">
                  <img src="{{asset('website/images/review.webp')}}" width="100" height="100" class="w-100 float-left h-100 object-fit-cover" alt="homebanner1" loading="lazy" decoding="async" />
                </div>
                <figure>
                  <img src="{{ $reviews->image ? asset('storage/reviewImages/' . $reviews->image) : asset('storage/userImages/1713906046.png') }}" class="float-left object-fit-cover" alt="homebanner1" loading="lazy" decoding="async" />

                </figure>
                <div class="review_box d-flex flex-column w-100 h-100">
                  <p>{{$reviews->message}}</p>
                  <h4>{{$reviews->name}}</h4>
                </div>
              </div>
            </li>
            @endforeach


          </ul>
          <div class="swiper-pagination review_slider_dots"></div>
          <span class="swiper-button-prev review_slider_arrow"><i class="far fa-long-arrow-alt-left"></i></span>
          <span class="swiper-button-next review_slider_arrow"><i class="far fa-long-arrow-alt-right"></i></span>
        </div>
        <div class="review_btn d-flex w-100 justify-content-center">
          <a class="d-flex align-items-center justify-content-center" data-bs-toggle="modal" data-bs-target="#reviewModel">Write a review</a>
        </div>
      </div>
    </div>
  </div>
</section>


<div class="modal fade zoom" id="reviewModel" tabindex="-1" aria-labelledby="reviewModelLabel"
  aria-hidden="true">
  <div class="modal-dialog custom_package_container modal-dialog-scrollable">

    <div
      class="custom_package_popup d-flex w-100 flex-column align-items-center justify-content-center position-relative">
      <button class="btn-close custom_package_close_btn" data-bs-dismiss="modal"><i
          class="fas fa-times"></i></button>
      <div
        class="custom_package_popup_des d-flex w-100 flex-column align-items-center justify-content-center text-center">
        <h5>Write a review</h5>

      </div>
      <form method="post" class="w-100" id="review_submit">
        @csrf
        <div class="login_form_content_box">

          <div for="profile_img" class="drag_and_drop_box" data-for="profile_img" style="max-width: 300px;
    margin: 0 auto;">
            <small>select your image (optional)</small>
            <input id="profile_img" type="file" accept="image/*" name="profile_img"
              class="dropify" data-max-file-size="2M"
              data-allowed-file-extensions="jpg jpeg png webp" />
            <label class="error" generated="true" for="profile_img"></label>
          </div>

          <div class="login_single_field_box">
            <span>Name</span>
            <input type="text" placeholder="Name" id="name" name="name">
            <label class="error" generated="true" for="name"></label>
          </div>

          <div class="login_single_field_box" style="height:120px;">
            <span>Review</span>
            <textarea type="text" placeholder="Write your review" id="message" name="message"></textarea>

            <label class="error" generated="true" for="message"></label>
          </div>
          <div class="custom_package_popup_btn">
            <input type="submit" value="Submit">
            <a data-bs-dismiss="modal"
              class="d-flex w-100 align-items-center justify-content-center text-center">close</a>
          </div>
        </div>
      </form>


    </div>

  </div>
</div>