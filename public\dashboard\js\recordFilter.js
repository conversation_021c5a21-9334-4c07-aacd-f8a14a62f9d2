$(document).ready(function () {
    $(".get_destination_dates").change(function (event) {
        event.preventDefault();
        $(".form_process_loader").removeClass("d-none");
        var destinationId = $(this).val();
        let baseURL = window.location.origin;
        // API endpoint path
        let apiEndpoint = "/api/destination/dates/" + destinationId;
        // Concatenate base URL with API endpoint path
        let apiUrl = baseURL + apiEndpoint;
        $.ajax({
            url: apiUrl,
            type: "GET",
            success: function (response) {
                $(".form_process_loader").addClass("d-none");
                showAlert(
                    "select the date now and click on filter!",
                    "success"
                );
                $("#dates").empty(); // Clear the current options
                response.dates.forEach(function (dateStr) {
                    let date = new Date(dateStr);
                    let formattedDate = new Intl.DateTimeFormat("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                    }).format(date);
                    $("#dates").append(
                        '<option value="' +
                            formattedDate +
                            '">' +
                            formattedDate +
                            "</option>"
                    );
                });
            },
            error: function (xhr, status, error) {
                console.error(error);

                $(".form_process_loader").addClass("d-none");

                showAlert("An error occurred while submitting.", "error");
                // Handle the error appropriately
            },
        });
    });
});

$(document).ready(function () {
    $("#get_destination").change(function (event) {
        event.preventDefault();
        $(".form_process_loader").removeClass("d-none");
        var userId = $(this).val();
        let baseURL = window.location.origin;
        // API endpoint path
        let apiEndpoint = "/api/destination/get/" + userId;
        // Concatenate base URL with API endpoint path
        let apiUrl = baseURL + apiEndpoint;
        $.ajax({
            url: apiUrl,
            type: "GET",
            success: function (response) {
                $(".form_process_loader").addClass("d-none");
                showAlert("select the destination now!", "success");
                $("#destinationResult").empty(); // Clear the current options
                if (Array.isArray(response.destinationResult)) {
                    response.destinationResult.forEach(function (
                        destinationResult
                    ) {
                        $("#destinationResult").append(
                            '<option value="' +
                                destinationResult.id +
                                '">' +
                                destinationResult.title +
                                "</option>"
                        );
                    });
                } else {
                    // Handle the case where destinationResult is not an array
                    console.error(
                        "Destination result is not an array:",
                        response.destinationResult
                    );
                }
            },
            error: function (xhr, status, error) {
                console.error(error);

                $(".form_process_loader").addClass("d-none");

                showAlert("An error occurred while submitting.", "error");
                // Handle the error appropriately
            },
        });
    });
});
