<?php

namespace App\Http\Controllers\website\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class EmailVerificationPromptController extends Controller
{
    /**
     * Display the email verification prompt.
     */
         /**
    public function __invoke(Request $request): View
    {
        return $request->user()->hasVerifiedEmail()
                    ? redirect()->intended(RouteServiceProvider::HOME)
                    : view('auth.verify-email');
    }
    
     **/

public function show(Request $request)
{
    $user = $request->user();

    // Check if the user is logged in and email is verified
    if ($user && $user->hasVerifiedEmail()) {
        return redirect()->intended(RouteServiceProvider::HOME);
    }

    // If not verified or not logged in, show the verification page
    return view('website.auth.verify-email');
}  
     
}