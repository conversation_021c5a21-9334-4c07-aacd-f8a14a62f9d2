<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasUpdateSeo{{$seoDataList->id}}"
    aria-labelledby="offcanvasUpdateSeo{{$seoDataList->id}}Label">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 id="offcanvasUpdateSeo{{$seoDataList->id}}Label" class="offcanvas-title">Update Category</h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="update_seo_data_{{$seoDataList->id}}" enctype="multipart/form-data">
            @csrf

            <div class="d-grid gap-3 w-100">

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="page">Page</label>
                    <select class="custom_form_field" name="page" id="page">
                        <option selected disabled>Select Page</option>
                        <option {{ $seoDataList->page == 'home' ? 'selected' : '' }} value="home">Home</option>
                        <option {{ $seoDataList->page == 'about' ? 'selected' : '' }} value="about">About</option>
                        <option {{ $seoDataList->page == 'blog' ? 'selected' : '' }} value="blog">Blog</option>
                        <option {{ $seoDataList->page == 'news' ? 'selected' : '' }} value="news">News</option>
                        <option {{ $seoDataList->page == 'destination' ? 'selected' : '' }} value="destination">
                            Destination</option>
                        <option {{ $seoDataList->page == 'destinationCategory' ? 'selected' : '' }}
                            value="destinationCategory">Destination Category</option>
                        <option {{ $seoDataList->page == 'destinationType' ? 'selected' : '' }} value="destinationType">
                            Destination Types</option>
                        <option {{ $seoDataList->page == 'contact' ? 'selected' : '' }} value="contact">Contact</option>
                        <option {{ $seoDataList->page == 'agent' ? 'selected' : '' }} value="agent">Agent</option>
                        <option {{ $seoDataList->page == 'subscribe' ? 'selected' : '' }} value="subscribe">subscribe
                        </option>
                         <option {{ $seoDataList->page == 'termsAndConditions' ? 'selected' : '' }} value="termsAndConditions">Terms And Conditions</option>
                             <option {{ $seoDataList->page == 'privacyPolicy' ? 'selected' : '' }} value="privacyPolicy">Privacy Policy</option>
                              <option {{ $seoDataList->page == 'travelGuide' ? 'selected' : '' }} value="travelGuide">Travel Guide</option>
                         <option {{ $seoDataList->page == 'bookingTermsAndConditions' ? 'selected' : '' }} value="bookingTermsAndConditions">Booking Terms And Conditions</option>
                       
                        <option {{ $seoDataList->page == 'login' ? 'selected' : '' }} value="login">login</option>
                    </select>
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="page_h1_heading">Seo H1 heading (55 to 60 charactor)</label>
                    <input class="custom_form_field" id="page_h1_heading" type="text" name="page_h1_heading"
                        value="{{$seoDataList->page_h1_heading}}" />
                </div>

                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="meta_title">Meta Title (55 to 60 charactor)</label>
                    <input class="custom_form_field" id="meta_title" type="text" name="meta_title"
                        value="{{$seoDataList->meta_title}}" />
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="meta_description">Meta Description (155 to 160 charactor)</label>
                    <input class="custom_form_field" id="meta_description" type="text" name="meta_description"
                        value="{{$seoDataList->meta_description}}" />
                </div>
                <div class="form_field_box d-grid gap-2 w-100">
                    <label for="meta_keywords">Meta keywords (comma seperated)</label>
                    <input class="custom_form_field" id="meta_keywords" type="text" name="meta_keywords"
                        value="{{$seoDataList->meta_keywords}}" />
                </div>




                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button type="submit" class="custom_btn_2">Update
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>
        </form>
    </div>
</div>
