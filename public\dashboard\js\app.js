(function () {
    document.querySelectorAll(".custom_tags_input").forEach(function (input) {
        new Tagify(input);
    });
})();

function initSelect2() {
    $(
        ".include_in_package, .not_include_in_package, .couple_benefits,.custom_multi_select_dropdown"
    ).select2({
        dataPlaceholder: "Select an option",
        allowClear: false,
        multiple: true,
    });
}
initSelect2();

$(".dropify").dropify({
    messages: {
        default: "File",
        replace: "Drag and drop or click to replace",
        remove: "Remove",
        error: "Ooops, something wrong happended.",
    },
});

$(document).ready(function () {
    $(".hamburger-btn").click(function () {
        $(".dashboard-sidebar").toggleClass("active");
        $(".content-section").toggleClass("active");
    });

    $(document).ready(function () {
        $(
            "#package_listing,#destination_listing,#blog_listing,#blog_category_listing,#contact_category_listing,#newsletter_listing,#destination_category_listing,#destination_type_listing,#country_location_listing,#custom_trip_request,#destination_includes_listing,#destination_not_includes_listing,#destination_extras_listing,#custom_trip_payment,#custom_trip_detail"
        ).DataTable({
            dom: "lBfrtip",
            responsive: true,
            scrollX: true, // Enable horizontal scrolling
            fixedColumns: {
                leftColumns: 1, // Number of columns to fix on the left
            },
            buttons: [
                {
                    extend: "collection",
                    text: '<i class="fal fa-cloud-download"></i> Export',
                    buttons: [
                        {
                            extend: "print",
                            text: '<i class="fal fa-print"></i> Print',
                            exportOptions: {
                                columns: ":visible",
                            },
                        },
                        {
                            extend: "csvHtml5",
                            text: '<i class="fal fa-file-csv"></i> CSV',
                            exportOptions: {
                                columns: ":visible",
                            },
                        },
                        {
                            extend: "pdfHtml5",
                            text: '<i class="fal fa-file-pdf"></i> PDF',
                            exportOptions: {
                                columns: ":visible",
                            },
                        },

                        {
                            extend: "copyHtml5",
                            text: '<i class="far fa-copy"></i> Copy',
                            exportOptions: {
                                columns: ":visible",
                            },
                        },
                    ],
                    className: "export-buttons",
                },
            ],

            //  pagingType: "full_numbers",
            language: {
                lengthMenu:
                    '<select name="package_listing_length" aria-controls="example" class="">' +
                    '<option value="10">10</option>' +
                    '<option value="25">25</option>' +
                    '<option value="50">50</option>' +
                    '<option value="200">200</option>' +
                    '<option value="-1">All</option>' +
                    "</select>",
                search: "<label></label>",
                searchPlaceholder: "Search...",
            },
            pageLength: 10,
            select: {
                style: "multi",
            },

            select: {
                style: "os",
                selector: "td:first-child",
            },
            order: [[1, "asc"]],
            columnDefs: [
                {
                    targets: 0, // Target the first column
                    orderable: false,
                    className: "select-checkbox",
                    render: function (data, type, full, meta) {
                        if (type === "display") {
                            return '<input type="checkbox" class="select-checkbox custom_design_checkbox">';
                        }
                        return data;
                    },
                },
                { targets: [0, -1], orderable: false }, // Disable sorting for the first and last columns
            ],
            // Add checkbox in the thead
            initComplete: function () {
                this.api()
                    .columns()
                    .every(function () {
                        var column = this;
                        if (column.index() === 0) {
                            var header = $(column.header());
                            header.html(
                                '<input type="checkbox" class="select-all-checkbox custom_design_checkbox">'
                            );
                        }
                    });
            },
        });
        // Select all checkboxes
        $(document).on("change", ".select-all-checkbox", function () {
            var checked = $(this).prop("checked");
            $("#package_listing .select-checkbox").prop("checked", checked);
        });
    });
});
