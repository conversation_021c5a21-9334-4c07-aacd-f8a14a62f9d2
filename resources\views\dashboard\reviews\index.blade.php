@extends("dashboard.include.layout")


@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a> / reviews / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
       
        <table id="blog_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">Status</th>
                    <th style="min-width: 100px;">Name</th>
                    <th style="min-width: 160px;">Message</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($reviews as $reviews)
                <tr class="review_row">
                    <td></td>
                    <td>
                        <label class="toogle_switch mb-3">
                            <input type="checkbox" class="review_switch_input" {{ $reviews->status==1 ? "checked" : ""}}
                                data-id="{{ $reviews->id }}">
                            <span class="agent_switch switch round" data-id="{{ $reviews->id }}"></span>
                        </label>
                    </td>
                   
                   

                    <td>{{ $reviews->name }}</td>
                    <td>{{ $reviews->message }}</td>
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $reviews->id }}" type="button" class="delete_btn delete_review"><i
                                        class="fas fa-trash-alt"></i>Delete</button>
                            </div>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</div>

@include("dashboard.reviews.delete")

@endsection