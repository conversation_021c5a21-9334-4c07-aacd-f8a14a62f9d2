<?php

namespace App\Http\Controllers;

use App\Models\adminData;
use App\Models\Destinations;
use App\Models\Subscribers;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class subscriberController extends Controller
{
    public function getSubscriber(Request $request)
    {
        $status = $request->query('status');

        if ($status) {
            $subscribers = Subscribers::with('user')->where('status', $status)->get();
        } else {
            $subscribers = Subscribers::with('user')->get();
        }

        $totalSubscribers = count($subscribers);
        $totalApproved = $subscribers->where('aproval', 1)->count();
        $totalRejected = $subscribers->where('rejected', 1)->count();
        $totalVerificationPending = $totalSubscribers - $totalApproved;

        return response()->json([
            'subscribers' => $subscribers,
            'totalSubscribers' => $totalSubscribers,
            'totalApproved' => $totalApproved,
            'totalRejected' => $totalRejected,
            'totalVerificationPending' => $totalVerificationPending,
        ]);
        // return response()->json($subscribers);
    }

    public function postSubscribe(request $request)
    {

        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|string|email|',
            'phone' => 'required',
            'subscription' => 'required',
            'country' => 'required',
            'address' => 'required',
            'company_name' => 'required',
            'company_description' => 'required',
            'licence_file' => 'image|mimes:jpeg,webp,png,jpg,gif|max:2048',
            'company_logo' => 'image|mimes:jpeg,webp,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $existingRequest = Subscribers::where('user_id', $request->user_id)->first();

        if ($existingRequest && $existingRequest->status == 1) {
            return response()->json([
                "message" => "You are already our subscriber.",
                "status" => "error",
            ], 402);
        } elseif ($existingRequest && $existingRequest->status == 0) {
            return response()->json([
                "message" => "You have already submitted a subscription request.",
                "status" => "error",
            ], 403);
        }

        $subscriber = new Subscribers();
        $subscriber->user_id = $request->user_id;
        $subscriber->phone_num = $request->phone;
        $subscriber->subscription = $request->subscription;
        $subscriber->country = $request->country;
        $subscriber->address = $request->address;
        $subscriber->company_name = $request->company_name;
        $subscriber->company_description = $request->company_description;
        $subscriber->registration_number = $request->reg_number;
        $subscriber->tin_number = $request->tin_number;
        $subscriber->company_website_link = $request->company_website_link;

        $subscriber->status = 0;
        $subscriber->aproval = 0;
        // Handle image upload
        if ($request->hasFile('company_logo')) {
            $file = $request->file('company_logo');
            $logoImagePath = media($file, 'storage/companyLogo');
            $logoImagePath = basename($logoImagePath);
            $subscriber->company_logo = $logoImagePath;
        }
        if ($request->hasFile('licence_file')) {
            $file = $request->file('licence_file');
            $licenceImagePath = media($file, 'storage/licenceFiles');
            $licenceImagePath = basename($licenceImagePath);

            $subscriber->licence_file = $licenceImagePath;
        }

        $subscriber->save();

        $userData = User::where('id', $request->user_id)->first();
        $globalData = adminData::first();
        //mail to admin
        $responce = sendMail([
            'view' => 'emailTemplates.subscription_request_mail_to_admin',
            'to' => $globalData->primary_email,
            'subject' => 'Subscription request',
            'data' => [
                'name' => $userData->name,
                'email' => $userData->email,
                'plan' => $request->subscription,

            ],
        ]);
        // mail to user
        $responce = sendMail([
            'view' => 'emailTemplates.subscription_request_mail_to_user',
            'to' => $request->email,
            'subject' => 'Subscription request',
            'data' => [

            ],
        ]);

        return response()->json(["message" => "Subscription request successfull we will get in touch you within 2 days.", "status" => "success", 'route' => route('home')]);

        // $user->sendEmailVerificationNotification();

    }

    public function deleteUserSubscription($id)
    {
        $subscription = Subscribers::findOrFail($id);
        // Delete related records from the 'destination' table
        Destinations::where('user_id', $subscription->user_id)->delete();
        $subscription->delete();
        return response()->json(null, 204);
    }

    public function rejectUserSubscription(Request $request, $id)
    {
        $subscription = Subscribers::findOrFail($id);
        $userdetail = User::where('id', $subscription->user_id)->first();
        // send email to user with reason
        $responce = sendMail([
            'view' => 'emailTemplates.reject_subscription_mail_to_user',
            'to' => $userdetail->email,
            'subject' => 'Subscription Rejected',
            'data' => [
                'message' => $request->message,

            ],
        ]);
        $subscription->aproval = 0;
        $subscription->rejected = 1;
        $subscription->save();
        return response()->json(null, 204);
    }

    public function status($id)
    {
        $subscriber = Subscribers::findOrFail($id);
        $subscriber->status = !$subscriber->status; // Toggle status
        $subscriber->save();
        return response()->json($subscriber, 200);
    }

    public function aprovalStatus($id)
    {
        $subscriber = Subscribers::findOrFail($id);
        $subscriber->rejected = 0;
        $subscriber->aproval = !$subscriber->aproval;

        $subscriber->save();
        $userdetail = User::where('id', $subscriber->user_id)->first();
        $responce = sendMail([
            'view' => 'emailTemplates.approve_subscription_mail_to_user',
            'to' => $userdetail->email,
            'subject' => 'Subscription Approved',
            'data' => [],
        ]);
        return response()->json($subscriber, 200);
    }

    public function profile($userId)
    {
        $user = User::findOrFail($userId);
        $subscribers = Subscribers::where('user_id', $userId)->get();
        return view('dashboard.agents.profile', compact('user', 'subscribers'));
    }
}
