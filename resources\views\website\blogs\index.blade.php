@extends('website.include.layout')
@section('title', 'Blogs')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')

@push('css')

@endpush
@section('content')

<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('website/images/blogbanner.webp');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">

                <h2>Discover the Beauty of Africa with Our Travel Blog</h2>
                <p>Explore Africa's diverse cultures, stunning landscapes, and rich wildlife through our travel blog.
                    Get inspired to plan your African adventure and experience the magic of this extraordinary continent
                    with Travel Africa.</p>
            </div>
        </div>
    </div>
</section>

<div class="single_article_parent d-inline-block w-100">
    <div class="custom_container">
        <div class="single_article_box">
            <div class="single_article_data d-flex flex-column w-100">
                <div class="blog_listing d-flex align-items-center justify-content-center flex-wrap">
                    @foreach($blogs as $blog)
                    @if($blog->type=='blog')
                    <a href="{{route('blog.detail',$blog->slug)}}" class="single_blog_box_main d-flex flex-column">
                        <div class="single_blog_box_figure d-flex w-100">
                            <img class="w-100 h-100 object-fit-cover"
                                src="{{ $blog->cover_image ? asset('storage/blog/'.$blog->cover_image) : asset('website/images/logo.png') }}"
                                alt="{{$blog->alt_text_cover_image}}" loading="lazy" decoding="async" width="250" height="210" />

                        </div>
                        <div class="single_blog_box d-flex flex-column w-100 h-100">
                            <div
                                class="single_blog_box_des_tags d-flex w-100 align-items-center flex-wrap justify-content-start">
                                <span>{{customDate($blog->created_at,'F d, Y')}}</span>
                                <small>{{$blog->comments->count()}} Comments</small>
                            </div>
                            <h3>{{$blog->title}}</h3>
                            <p>{{$blog->short_description}}</p>
                            <strong>Read Post</strong>
                        </div>
                    </a>
                    @endif
                    @endforeach
                </div>
                {{ $blogs->links() }}
            </div>

            <div class="single_article_sidebar">
                <div class="popular_article_box d-flex flex-column w-100">
                    <div class="popular_article_heading d-flex w-100 align-items-center justify-content-between">
                        <strong>Recent Post</strong>
                    </div>
                    <div class="popular_article_list">
                        @if($recent_posts)
                        @foreach($recent_posts as $resent_post)
                        <a class="popular_single_article" href="{{route('blog.detail',$resent_post->slug)}}">
                            <figure class="w-100">
                                <img class="w-100 h-100 object-fit-cover"
                                    src="{{ $resent_post->cover_image ? asset('storage/blog/'.$resent_post->cover_image) : asset('website/images/logo.png') }}"
                                    alt="{{$resent_post->alt_text_cover_image}}" loading="lazy" decoding="async" width="250" height="210" />

                            </figure>
                            <div class="popular_single_article_details d-flex w-100 flex-column">
                                <h3>{{$resent_post->title}}</h3>
                                <div class="popular_single_article_inner_details d-flex align-items-center">
                                    <span>{{customDate($resent_post->created_at,'F d, Y')}}</span>
                                    <small>{{$resent_post->comments->count()}} Comments</small>
                                </div>
                                <strong>Read Post</strong>
                            </div>
                        </a>
                        @endforeach
                        @endif
                    </div>
                </div>
            </div>


        </div>

    </div>

</div>
@endsection

@push('js')
@endpush
