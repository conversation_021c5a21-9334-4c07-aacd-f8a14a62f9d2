$(document).ready(function () {
    $("#custom_trip_direct_payment").validate({
        rules: {
            payment_slip: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/destination/custom/payment";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    showAlert("payment slip submitted successfully", "success");
                    setTimeout(function () {
                        window.location.href = response.route;
                        //  window.location.reload();
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    $(".invalid-feedback").remove();
                    showAlert(
                        "There is some error, contact support team.",
                        "error"
                    );
                },
            });
        },
    });
});

var stripe = Stripe(
    "pk_live_51L8LaaAk3QgNwWy4IbgXTGK4inj7ZcFOcDNE36QKUhLL68OGXrsUVkiEt4A9OxRZbRMNJNHAAVmP9sVeRfmzONjZ00F19EkSDa"
);
var elements = stripe.elements();

var cardElement = elements.create("card");
cardElement.mount("#card-element");

var stripForm = document.getElementById("strip_payment_submit");

stripForm.addEventListener("submit", function (event) {
    event.preventDefault();

    stripe.createToken(cardElement).then(function (result) {
        if (result.error) {
            console.error("error", result.error.message);
        } else {
            $(".form_process_loader").removeClass("d-none");
            var formData = $(stripForm).serialize();
            formData += "&token=" + result.token.id;
            console.log("formData", formData);
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/destination/booking/strip/payment";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;
            $.ajax({
                url: apiUrl,
                method: "POST",
                headers: {
                    "X-CSRF-TOKEN": $('meta[name="csrf-token"]').attr(
                        "content"
                    ),
                },
                data: formData,
                success: function (response) {
                    console.log("success", response);
                    $(".form_process_loader").addClass("d-none");

                    showAlert("payment submitted successfully", "success");
                    setTimeout(function () {
                        window.location.href = response.route;
                        //  window.location.reload();
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error("resonce erorr", error);
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    $(".invalid-feedback").remove();
                    showAlert(
                        "There is some error, contact support team.",
                        "error"
                    );
                },
            });
        }
    });
});
