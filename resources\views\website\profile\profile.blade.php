@extends('website.include.layout')
@section('title', 'Profile')
@push('css')
<link media="print" onload="this.media='all'" rel="stylesheet" href="{{asset('profile/css/profile_page.css')}}">
@endpush
@section('content')

<section class="page_title_section d-block w-100 text-center float-left position-relative">
    <img src="{{asset('website/images/login_banner.png')}}" class="w-100 float-left h-100 object-fit-cover"
        alt="profile banner" decoding="async" fetchpriority="high" />
    <div class="d-flex w-100 h-100 position-absolute top-0 left-0">
        <div class="page_title d-flex align-items-center justify-content-center text-center flex-column">
            <small>Read</small>
            <h2>Profile</h2>
        </div>
    </div>
</section>

<section class="profile_page_section">
    <div class="custom_container">
        <div class="pofile_page_main_box">
            @include('website.profile.sidebar')
            <!-- personel info asset start -->

            <div class="profile_tabs_data_section">
                <div class="profile_tabs_data_header">
                    <h2>Personal Information</h2>
                </div>
                <div class="profile_tabs_content_box">
                    @php($user = auth()->user())
                    <form id="update_user_profile">
                        @csrf
                        <input type="hidden" name="id" value="{{$user->id}}">

                        <div class="edit_personal_info_body">
                            <div class="profile_form_content_box">

                                <div class="profile_form_field_box">

                                    <span for="profile_img">Profile Image</span>
                                    <input id="profile_img" type="file" name="profile_img" class="dropify"
                                        data-max-file-size="2M"
                                        data-allowed-file-extensions="jpg jpeg png gif pdf doc docx xls xlsx webp"
                                        data-default-file="{{$user ? asset('storage/userImages/'.$user->profile_image) : ''}}" />
                                </div>

                                <div class="profile_form_field_box">
                                    <div class="profile_single_field_box">
                                        <span>Full Name</span>
                                        <input type="text" name="name" value="{{$user->name}}">
                                    </div>
                                </div>
                                <div class="profile_form_field_box">
                                    <div class="profile_single_field_box">
                                        <span>Email</span>
                                        <input type="text" name="" readonly value="{{$user->email}}">
                                    </div>
                                </div>

                                <div class="profile_form_submit_btn">
                                    <input type="submit" value="Update">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>



@endsection

@push('js')
<script src="{{ asset('profile/js/custom_script.js') }}" defer></script>
@endpush
