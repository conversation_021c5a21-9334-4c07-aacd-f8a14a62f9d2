<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class destinationSlots extends Model
{
    use HasFactory;

    protected $fillable = [
        'destination_id',
        'date',
        'slots',
        'book_slots',
    ];

    public function destinations()
    {
        return $this->hasMany(Destinations::class, );
    }
}
