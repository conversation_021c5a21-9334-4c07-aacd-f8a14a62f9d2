<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Destinations extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'category_id',
        'type_id',
        'title',
        'slug',
        'short_description',
        'departure_location',
        'departure_date_time',
        'include_in',
        'not_include',
        'price',
        'two_person_price',
        'three_to_four_price',
        'five_to_six_price',
        'six_plus_price',
        
        'single_room_price',
        
        'two_final_price',
        'three_to_four_final_price',
        'five_to_six_final_price',
        'six_plus_final_price',
        'destination_accomodation_detail',
        'cover_img',
        'banner_image',
        'image_gallery',
        'destination_detail',
        'status',
        'created_at',
        'updated_at',
        'destination_long_detail',
        'currency',
        'destination_country',
        'extras',
        'days',
        'nights',
        'admin_commision',
        'final_price',
        'popular',
        'trending',
         'flag',
        'total_slots',
        'city',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'page_h1_heading',
        'alt_text_cover_image',
        'alt_text_banner_image',
        'destination_pdf',

    ];
    public function category()
    {
        return $this->belongsTo(DestinationCategory::class);
    }
    public function type()
    {
        return $this->belongsTo(DestinationType::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function destinationBookings()
    {
        return $this->hasMany(DestinationBooking::class);
    }

    public function destinationDatesSlots()
    {
        return $this->hasMany(destinationSlots::class);
    }

}
