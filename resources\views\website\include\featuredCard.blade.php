@foreach($popularDestination as $popularDestination)

@php
$departureDatesTimesArray = explode(", ", $popularDestination->departure_date_time);
$departureDateTimeObjects = [];
foreach ($departureDatesTimesArray as $dateTimeString) {
$dateTimeObject = \DateTime::createFromFormat('Y-m-d h:i A', $dateTimeString);
if ($dateTimeObject !== false) {
$departureDateTimeObjects[] = $dateTimeObject;
}
}

$currentDateTime = new \DateTime();
$nearestDateTime = null;
$nearestDiff = PHP_INT_MAX;
foreach ($departureDateTimeObjects as $dateTime) {
$diff = $dateTime->getTimestamp() - $currentDateTime->getTimestamp();
if ($diff > 0 && $diff < $nearestDiff) { $nearestDiff=$diff; $nearestDateTime=$dateTime; } }
    $nearestDateString=$nearestDateTime ? $nearestDateTime->format('Y-m-d h:i A') :
    '';
    @endphp
    <li class="swiper-slide">
        <div class="single_product">
            <div class="product_card w-100">
                <a href="{{ route('website.destination.detail', $popularDestination->slug) }}"
                    class="destination_img w-100 position-relative">
                    <img width="300" height="300" src="{{$popularDestination ? asset('storage/destinations/'.$popularDestination->cover_img) : asset('website/images/logo.webp') }}"
                        class="w-100 h-100 object-fit-cover" alt="{{$popularDestination->alt_text_cover_image}}"
                        loading="lazy" decoding="async" />
                    <div class="nearest_departure_date">
                        {{customDate($nearestDateString,'F d, Y')}}
                    </div>
                </a>
                <div class="product_content d-flex flex-column w-100">

                    <div class="shop_single_package_rating d-flex">
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    </div>

                    <div class="shop_package_location d-flex align-items-start flex-column">
                        <span class="d-flex align-items-center"><i
                                class="far fa-calendar-check"></i>{{$popularDestination->days}}
                            Days - {{$popularDestination->nights}} Nights</span>
                        <span class="d-flex align-items-center"><i class="fas fa-map-marker-alt"></i>
                            {{$popularDestination->destination_country}}</span>
                    </div>
                    <a href="{{ route('website.destination.detail', $popularDestination->slug) }}"
                        class="product_name">{{$popularDestination->title}}</a>
                    <div class="shop_short_des">{!! $popularDestination->short_description !!}
                    </div>
                    <div class="price_tags d-flex flex-column w-100">
                        <div class="total_and_discount_price d-flex flex-column">
                            <span class="price_label">Per person</span>
                            <small class="orignal_price">From <span
                                    class="price_currency text-uppercase">{{$popularDestination->currency}}</span>
                               {{$popularDestination->final_price}}</small>

                        </div>
                        <div class="product_cart_btn">
                            <a href="{{ route('website.destination.detail', $popularDestination->slug) }}"
                                class="d-flex align-items-center justify-content-center">View
                                Detail</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </li>
    @endforeach
