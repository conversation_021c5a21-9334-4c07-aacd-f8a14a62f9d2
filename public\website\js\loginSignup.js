$(document).ready(function () {
    $("#signup_form").validate({
        rules: {
            name: {
                required: true,
            },
            email: {
                required: true,
                email: true,
            },
            profile_img: {
                required: true,
            },
            password: {
                required: true,
                minlength: "8",
            },
            password_confirmation: {
                required: true,
                equalTo: "#password",
                minlength: "8",
            },
        },
        messages: {
            password_confirmation: {
                equalTo: "Please enter the same password as above",
            },
        },
        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/register";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert(
                        "Successfully signed up! Please check your email for a verification message. Don't forget to check your spam folder.",
                        "success"
                    );
                    $(".invalid-feedback").remove();
                    setTimeout(function () {
                        window.location.href = response.route;
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");
                    console.error(xhr);
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    }
                },
            });
        },
    });

    $("#login_form").validate({
        rules: {
            login_email: {
                required: true,
                email: true,
            },
           login_password: {
                required: true,
            },
        },
        submitHandler: function (form, e) {
            e.preventDefault(); // Prevent the default form submission
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/login";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form);
            //  console.log(formData);
            // Create FormData object from the form
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    //   console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Successfully login", "success");
                    $(".invalid-feedback").remove();
                    setTimeout(function () {
                        window.location.href = response.route;
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");
                    console.error(xhr);
                    $(".invalid-feedback").remove();
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 404) {
                        $("#login_email")
                            .addClass("is-invalid")
                            .after(
                                '<div class="invalid-feedback">User does not exist</div>'
                            );
                        showAlert("User does not exist", "error");
                    } else if (xhr.status === 402) {
                        $("#login_password")
                            .addClass("is-invalid")
                            .after(
                                '<div class="invalid-feedback">Incorrect password</div>'
                            );
                        showAlert("Incorrect password", "error");
                    } else if (xhr.status === 403) {
                        showAlert("Email not verified", "error");
                        setTimeout(function () {
                            window.location.href = "/verify/email";
                        }, 2000);
                    }
                },
            });
        },
    });

    $("#logout_btn,#profile_logout_btn").click(function (e) {
        e.preventDefault(); // Prevent the default anchor tag behavior
        let baseURL = window.location.origin;
        let apiEndpoint = "/api/logout";
        let apiUrl = baseURL + apiEndpoint;
        let csrfToken = $('meta[name="csrf-token"]').attr("content"); // Get the CSRF token value

        $.ajax({
            url: apiUrl,
            type: "POST",
            headers: {
                "X-CSRF-TOKEN": csrfToken, // Pass the CSRF token in the headers
            },
            success: function (response) {
                // Handle success, such as redirecting to the login page
                window.location.href = "/home";
            },
            error: function (xhr, status, error) {
                // Handle error
                console.error(xhr);
            },
        });
    });

    $("#reset_password_form").submit(function (e) {
        e.preventDefault();
        let baseURL = window.location.origin;
        let apiEndpoint = "/api/password/reset";
        let apiUrl = baseURL + apiEndpoint;
        //  let apiUrl = "/api/password/reset";
        $(".form_process_loader").removeClass("d-none");
        let formData = $(this).serialize();
        $.ajax({
            url: apiUrl,
            type: "POST",
            data: formData,
            dataType: "json",
            success: function (response) {
                $(".form_process_loader").addClass("d-none");
                showAlert("Reset link sent to your email", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                if (xhr.status === 404) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Email not foundr", "error");
                } else {
                    $(".form_process_loader").addClass("d-none");
                    showAlert(
                        "Unable to send reset link.please try later",
                        "error"
                    );
                }
            },
        });
    });
    $("#reset_request").validate({
        rules: {
            password: {
                required: true,
                minlength: 8,
            },
            password_confirmation: {
                required: true,
                equalTo: "#password",
            },
        },
        messages: {
            password_confirmation: {
                equalTo: "Please enter the same password as above",
            },
        },
        submitHandler: function (form, e) {
            e.preventDefault(); // Prevent the default form submission
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/reset-password";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert("Password reset successful", "success");
                    setTimeout(function () {
                        window.location.href = "/login";
                    }, 2000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");
                    console.error(xhr);
                    $(".invalid-feedback").remove();
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 404) {
                        showAlert("User not found", "error");
                    } else if (xhr.status === 403) {
                        $("#password")
                            .addClass("is-invalid")
                            .after(
                                '<div class="invalid-feedback">Password must be different from the previous one</div>'
                            );
                        showAlert(
                            "Password must be different from the previous one",
                            "error"
                        );
                    }
                    // else if (xhr.status === 401) {
                    //     showAlert("Unauthorized access", "error");
                    // }
                },
            });
        },
    });
});
