@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / destinations / includes / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddIncludes"><span>Add
                </span></button>
        </div>
        <table id="destination_includes_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 120px;">Title</th>
                    <th style="min-width: 16px;text-align:right;">Action</th>
                </tr>
            </thead>
            <tbody>
            @if($destinationIncludes && $destinationIncludes->isNotEmpty())
                @foreach($destinationIncludes as $destinationIncludesList)
                <tr class="include_row">
                    <td></td>
                    <td>{{ $destinationIncludesList->title }}</td>
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $destinationIncludesList->id }}" type="button"
                                    class="delete_btn delete_include"><i class="fas fa-trash-alt"></i>Delete</button>

                                <button class="update_btn edit_include_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasUpdateIncludes{{$destinationIncludesList->id}}"
                                    data-form-id="update_includes_{{$destinationIncludesList->id}}"
                                    data-id="{{$destinationIncludesList->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                            </div>
                        </div>
                        @include("dashboard.destinations.destinationIncludes.update")
    </div>
    </td>
    </tr>

    @endforeach
    @endif
    </tbody>
    </table>
</div>

</div>
@include("dashboard.destinations.destinationIncludes.add")
@include("dashboard.destinations.destinationIncludes.delete")

@endsection
