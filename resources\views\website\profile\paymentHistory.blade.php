@extends('website.include.layout')
@section('title', 'profile')
@push('css')
<link rel="stylesheet" href="{{asset('profile/css/profile_page.css')}}">
@endpush
@section('content')

<section class="page_title_section d-block w-100 text-center float-left position-relative">
    <img src="{{asset('website/images/login_banner.png')}}" class="w-100 float-left h-100 object-fit-cover"
        alt="homebanner1" loading="lazy" />
    <div class="d-flex w-100 h-100 position-absolute top-0 left-0">
        <div class="page_title d-flex align-items-center justify-content-center text-center flex-column">
            <small>Read</small>
            <h2>Payment History</h2>
        </div>
    </div>
</section>

<section class="profile_page_section">
    <div class="custom_container">
        <div class="pofile_page_main_box">
            @include('website.profile.sidebar')
            <!-- personel info asset start -->

            <div class="profile_tabs_data_section">
                <div class="profile_tabs_data_header">
                    <h2>Payment History</h2>
                </div>
                <div class="profile_tabs_content_box">
                    <table id="payment_history" class="data_table display" style="width:100%">
                        <thead>
                            <tr>
                                <th style="min-width: 40px;"></th>
                                <th style="min-width: 80px;">Destination Id</th>
                                <th style="min-width: 80px;">Booked Date</th>
                                <th style="min-width: 80px;">Tickets</th>
                                <th style="min-width: 80px;">Price</th>
                                <th style="min-width: 80px;">Payment Type</th>
                                <th style="min-width: 80px;">Charges</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($bookingPaymentRecords as $bookingPaymentRecords)
                            <tr>
                                <td></td>
                                <td>{{$bookingPaymentRecords->bookedDestination->destination_id}}</td>
                                <td>{{$bookingPaymentRecords->bookedDestination->date}}</td>
                                <td>{{$bookingPaymentRecords->bookedDestination->tickets}}</td>
                                <td>{{$bookingPaymentRecords->price}}</td>
                                <td>{{$bookingPaymentRecords->payment_type}}</td>
                                <td>{{$bookingPaymentRecords->charges}}</td>
                            </tr>
                            @endforeach

                        </tbody>
                    </table>

                </div>
            </div>
        </div>
    </div>
</section>



@endsection

@push('js')
<script src="{{ asset('profile/js/custom_script.js') }}"></script>
@endpush
