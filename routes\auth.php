<?php
use App\Http\Controllers\website\Auth\AuthenticatedSessionController;
use App\Http\Controllers\website\Auth\EmailVerificationNotificationController;
use App\Http\Controllers\website\Auth\EmailVerificationPromptController;
use App\Http\Controllers\website\Auth\loginSignupController;
use App\Http\Controllers\website\Auth\VerifyEmailController;
use Illuminate\Support\Facades\Route;

Route::middleware('guest')->group(function () {
    Route::get('login', [loginSignupController::class, 'view'])->name('login');
    Route::post('/api/register', [loginSignupController::class, 'register']);
    Route::post('/api/login', [loginSignupController::class, 'loginRequest']);
    Route::post('/api/password/reset', [loginSignupController::class, 'reset'])->name('password.reset');
});
Route::get('reset-password/{token}', [loginSignupController::class, 'restView'])
    ->name('password.reset');
Route::post('/api/reset-password', [loginSignupController::class, 'resetPassword']);

Route::get('/verify/email', function () {
    return view('website.auth.verify-email');
});
// Email verification route - accessible without authentication
Route::get('/verify-email/{id}/{hash}', [VerifyEmailController::class, 'verify'])->middleware(['signed', 'throttle:6,1'])->name('verification.verify');

// Removed duplicate route - using verification.notice instead
Route::middleware('auth')->group(function () {

    Route::get('/verify-email', [EmailVerificationPromptController::class, 'show'])->name('verification.notice');
    Route::post('/verify-email/send', [EmailVerificationNotificationController::class, 'send'])->middleware('throttle:6,1')->name('verification.send');
    Route::post('/api/logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout');

});
