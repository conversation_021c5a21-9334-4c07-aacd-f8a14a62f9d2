<div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasUpdateBanner{{$bannerList->id}}"
    aria-labelledby="offcanvasUpdateBanner{{$bannerList->id}}Label">
    <!-- Offcanvas Header -->
    <div class="offcanvas-header py-4">
        <h5 id="offcanvasUpdateBanner{{$bannerList->id}}Label" class="offcanvas-title">Update Banner</h5>
        <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
            aria-label="Close"></button>
    </div>
    <!-- Offcanvas Body -->
    <div class="offcanvas-body border-top">
        <form class="pt-0" id="update_banner_{{$bannerList->id}}" enctype="multipart/form-data">
            @csrf

            <div class="d-grid gap-3 w-100">



                   <div class="form_field_box d-grid gap-2 w-100">
                            <label for="desktop_image">Desktop banner</label>
                            <input id="desktop_image" type="file" name="desktop_image" class="dropify" data-max-file-size="2M"
                                data-allowed-file-extensions="jpg jpeg png webp"
                                data-default-file="{{$bannerList->desktop_image ? asset('storage/banner/'.$bannerList->desktop_image) : ''}}" />
                        </div>

                            <div class="form_field_box d-grid gap-2 w-100">
                            <label for="mobile_image">Mobile Banner</label>
                            <input id="mobile_image" type="file" name="mobile_image" class="dropify" data-max-file-size="2M"
                                data-allowed-file-extensions="jpg jpeg png webp"
                                data-default-file="{{$bannerList->mobile_image ? asset('storage/banner/'.$bannerList->mobile_image) : ''}}" />
                        </div>


                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="alt_text">Alt Text</label>
                            <input class="custom_form_field" name="alt_text" id="alt_text"
                                value="{{ $bannerList->alt_text }}" />
                        </div>
                         <div class="form_field_box d-grid gap-2 w-100">
                            <label for="link">Link</label>
                            <input class="custom_form_field" name="link" id="link"
                                value="{{ $bannerList->link }}" />
                        </div>


                <!-- Submit and reset -->
                <div class="d-grid gap-2 w-100">
                    <button type="submit" class="custom_btn_2">Update
                        <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                            style="background-color: var(--secondary-bg-color);">
                            <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                        </div>
                    </button>

                    <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                </div>
            </div>
        </form>
    </div>
</div>
