<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\BlogCategory;
use App\Models\BlogComment;
use App\Models\pagesSeo;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class blogCrudController extends Controller
{
    public function blogCategoryList()
    {
        $blogCatList = BlogCategory::all();

        return view('dashboard.blogs.blogCategoryList', ['blogCatList' => $blogCatList]);
    }

    public function categoryStore(Request $request)
    {

        $blogCategory = new BlogCategory();
        $blogCategory->title = $request->title;
        $blogCategory->page_h1_heading = $request->page_h1_heading;
        $blogCategory->meta_title = $request->meta_title;
        $blogCategory->meta_description = $request->meta_description;
        $blogCategory->meta_keywords = $request->meta_keywords;

        $blogCategory->alt_text_cover_image = $request->alt_text_cover_image;
        $blogCategory->alt_text_banner_image = $request->alt_text_banner_image;

        $blogCategory->slug = Str::slug($request->title);

        if (BlogCategory::where('slug', $blogCategory->slug)->exists()) {
            return response()->json(['message' => 'Category with the same slug already exists'], 422);
        }

        // Handle image upload
        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $blogCategoryPath = media($file, 'storage/blog');
            $blogCategoryPath = basename($blogCategoryPath);
            $blogCategory->cover_image = $blogCategoryPath;
        }
        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $blogCategoryBannerPath = media($file, 'storage/blog');
            $blogCategoryBannerPath = basename($blogCategoryBannerPath);
            $blogCategory->banner_image = $blogCategoryBannerPath;
        }

        $blogCategory->save();
        return response()->json($blogCategory, 200);
    }

    public function categoryDestroy($id)
    {
        $blogCategory = BlogCategory::findOrFail($id);
        $blogCategory->delete();
        return response()->json(null, 204);
    }

    public function categoryUpdate(Request $request, $id)
    {

        $blogCategory = BlogCategory::findOrFail($id);

        $blogCategory->title = $request->title;
        $blogCategory->page_h1_heading = $request->page_h1_heading;
        $blogCategory->meta_title = $request->meta_title;
        $blogCategory->meta_description = $request->meta_description;
        $blogCategory->meta_keywords = $request->meta_keywords;
        $blogCategory->alt_text_cover_image = $request->alt_text_cover_image;
        $blogCategory->alt_text_banner_image = $request->alt_text_banner_image;

        $newSlug = Str::slug($request->title);

        // Check if the slug has changed
        if ($blogCategory->slug !== $newSlug) {
            if (BlogCategory::where('slug', $newSlug)->exists()) {
                return response()->json(['message' => 'Category with the same slug already exists'], 422);
            }
            $blogCategory->slug = $newSlug;
        }

        // Handle image upload
        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $blogCategoryPath = media($file, 'storage/blog');
            $blogCategoryPath = basename($blogCategoryPath);
            $blogCategory->cover_image = $blogCategoryPath;
        }
        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $blogCategoryBannerPath = media($file, 'storage/blog');
            $blogCategoryBannerPath = basename($blogCategoryBannerPath);
            $blogCategory->banner_image = $blogCategoryBannerPath;
        }

        $blogCategory->save();
        return response()->json($blogCategory, 200);

    }

    public function index(request $request)
    {
        $status = $request->query('status');

        if ($status) {
            $blog = Blog::where('status', $status)->get();
        } else {
            $blog = Blog::all();
        }
        return view('dashboard.blogs.index', ['blogList' => $blog]);
    }
    public function addBlog()
    {
        $categories = BlogCategory::all();

        return view('dashboard.blogs.add', ['categories' => $categories]);
    }

    public function store(Request $request)
    {

        $blog = new Blog();
        $blog->user_id = auth()->user()->id;

        $blog->title = $request->title;
        $blog->slug = Str::slug($request->title);
        if (blog::where('slug', $blog->slug)->exists()) {
            return response()->json(['message' => 'Article with the same slug already exists'], 422);
        }
        $blog->short_description = $request->short_description;
        $blog->description = $request->description;
        $blog->tags = $request->tags;

        $blog->category_id = $request->category;

        $blog->author_name = $request->author_name;

        $blog->page_h1_heading = $request->page_h1_heading;
        $blog->meta_title = $request->meta_title;
        $blog->meta_description = $request->meta_description;
        $blog->meta_keywords = $request->meta_keywords;

        $blog->alt_text_cover_image = $request->alt_text_cover_image;
        $blog->alt_text_banner_image = $request->alt_text_banner_image;
        $blog->alt_text_author_image = $request->alt_text_author_image;
        $blog->type = $request->type;
        $blog->status = 0;
        $blog->popular = 0;

        // Handle image upload

        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $blogImagePath = media($file, 'storage/blog');
            $blogImagePath = basename($blogImagePath);
            $blog->cover_image = $blogImagePath;
        }

        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $blogImageBannerPath = media($file, 'storage/blog');
            $blogImageBannerPath = basename($blogImageBannerPath);
            $blog->banner_image = $blogImageBannerPath;
        }

        $blog->save();

        return response()->json($blog, 200);
    }

    public function show($id)
    {
        $blog = Blog::findOrFail($id);
        $categories = BlogCategory::all();

        return view('dashboard.blogs.update', compact('blog', 'categories'));

    }

    public function update(Request $request, $id)
    {
        $blog = Blog::findOrFail($id);

        $blog->title = $request->title;

        $blog->slug = Str::slug($request->title);

        $blog->short_description = $request->short_description;
        $blog->description = $request->description;
        $blog->tags = $request->tags;

        $blog->author_name = $request->author_name;

        $blog->type = $request->type;
        $blog->category_id = $request->category;

        $blog->page_h1_heading = $request->page_h1_heading;
        $blog->meta_title = $request->meta_title;
        $blog->meta_description = $request->meta_description;
        $blog->meta_keywords = $request->meta_keywords;
        $blog->alt_text_cover_image = $request->alt_text_cover_image;
        $blog->alt_text_banner_image = $request->alt_text_banner_image;
        $blog->alt_text_author_image = $request->alt_text_author_image;
        // Handle image upload

        if ($request->hasFile('cover_img')) {
            $file = $request->file('cover_img');
            $blogImagePath = media($file, 'storage/blog');
            $blogImagePath = basename($blogImagePath);
            $blog->cover_image = $blogImagePath;
        }
        if ($request->hasFile('banner_img')) {
            $file = $request->file('banner_img');
            $blogImageBannerPath = media($file, 'storage/blog');
            $blogImageBannerPath = basename($blogImageBannerPath);
            $blog->banner_image = $blogImageBannerPath;
        }

        $blog->save();

        return response()->json($blog, 200);

    }

    public function destroy($id)
    {
        $blog = Blog::findOrFail($id);
        $blogComments = BlogComment::where('blog_id', $id)->get();
        foreach ($blogComments as $comment) {
            $comment->delete();
        }
        $blog->delete();

        return response()->json(null, 204);
    }
    public function status($id)
    {
        $blog = Blog::findOrFail($id);
        $blog->status = !$blog->status; // Toggle status
        $blog->save();
        return response()->json($blog, 200);
    }
    public function popularStatus($id)
    {
        $blog = Blog::findOrFail($id);

        $blog->popular = !$blog->popular; // Toggle status

        $blog->save();

        return response()->json($blog, 200);
    }

    public function commentListing()
    {
        $blogCommentList = BlogComment::with('blog')->get();

        return view('dashboard.blogs.commentListing', ['blogCommentList' => $blogCommentList]);
    }
    public function commentDelete($id)
    {
        $blogComment = BlogComment::findOrFail($id);
        $blogComment->delete();
        return response()->json(null, 204);
    }
    public function commentStatus($id)
    {
        $comment = BlogComment::findOrFail($id);
        $comment->status = !$comment->status; // Toggle status
        $comment->save();
        return response()->json($comment, 200);
    }

// websites functions

    public function getBlogList(Request $request)
    {
        $seoData = pagesSeo::where('page', 'blog')->first();
        $blogs = Blog::with('comments')->where('status', 1)->paginate(10);
        $recent_posts = Blog::with('comments')->where('status', 1)->latest('created_at')->take(6)->get();
        return view('website.blogs.index', get_defined_vars());
    }
    public function getBlogDetail($slug)
    {
        $blog = Blog::with('comments')->where('slug', $slug)->firstOrFail();
        $popular_posts = Blog::where('status', 1)->where('popular', 1)->orderBy('created_at', 'desc')->get();
        if ($blog) {
            $related = Blog::where('status', 1)->where('id', '!=', $blog->id)->where(function ($q) use ($blog) {
                $q->where('category_id', $blog->category_id);
            })->take(10)->get();
        } else {
            $related = [];
        }
        return view('website.blogs.detail', get_defined_vars());
    }

    public function submitComment(Request $request)
    {
        if (auth()->user()) {
            $request->merge(['user_id' => auth()->user()->id]);
        }
        $request->merge(['status' => 0]);
        $comment = BlogComment::create($request->all());
        // $view = view('front.blogs.appendComment', get_defined_vars())->render();

        //return response()->json(['html' => $view, 'target' => 'append_blog_list', 'success' => 'Successfully Submitted']);

        return response()->json($comment, 200);
    }

    public function getNewsList(Request $request)
    {
        $seoData = pagesSeo::where('page', 'news')->first();
        $news = Blog::where('type', 'news')->paginate(10);
        $recent_posts = Blog::where('type', 'news')->where('status', 1)->latest('created_at')->take(6)->get();
        return view('website.news.index', get_defined_vars());
    }
    public function getNewsDetail($slug)
    {
        $news = Blog::where('slug', $slug)->firstOrFail();
        $popular_posts = Blog::where('status', 1)->where('popular', 1)->orderBy('created_at', 'desc')->get();
        $related = Blog::where('status', 1)->where('id', '!=', $news->id)->where(function ($q) use ($news) {
            $q->where('category_id', $news->category_id);
        })->take(10)->get();
        return view('website.news.detail', get_defined_vars());
    }

}
