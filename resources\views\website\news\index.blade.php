@extends('website.include.layout')
@section('title', 'news')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')

@push('css')

@endpush
@section('content')


<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('website/images/travelnews.jpg');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">

                <h2>Explore the Wonders of Africa with Travel Africa News</h2>
                <p>Stay updated with the latest news and stories about African destinations, wildlife, culture, and
                    more. Discover the beauty and diversity of Africa through our informative articles and updates. Plan
                    your next adventure with Travel Africa News</p>
            </div>
        </div>
    </div>
</section>

<div class="single_article_parent d-inline-block w-100">
    <div class="custom_container">
        <div class="single_article_box">
            <div class="single_article_data d-flex flex-column w-100">
                <div class="blog_listing d-flex align-items-center justify-content-center flex-wrap">
                    @foreach($news as $items)
                    @if($items->type=='news')
                    <a href="{{route('news.detail',$items->slug)}}" class="single_blog_box_main d-flex flex-column">
                        <div class="single_blog_box_figure d-flex w-100">
                            <img class="w-100 h-100 object-fit-cover" src="{{ $items->cover_image ? asset('storage/blog/'.$items->cover_image) : asset('website/images/logo.png') }}"
                                alt="{{$items->alt_text_cover_image}}" loading="lazy" decoding="async" width="250" height="210" />
                        </div>
                        <div class="single_blog_box d-flex flex-column w-100 h-100">
                            <div
                                class="single_blog_box_des_tags d-flex w-100 align-items-center flex-wrap justify-content-start">
                                <span>{{customDate($items->created_at,'F d, Y')}}</span>

                            </div>
                            <h3>{{$items->title}}</h3>
                            <p>{{$items->short_description}}</p>
                            <strong>Read News</strong>
                        </div>
                    </a>
                    @endif
                    @endforeach
                </div>
                {{ $news->links() }}
            </div>

            <div class="single_article_sidebar">
                <div class="popular_article_box d-flex flex-column w-100">
                    <div class="popular_article_heading d-flex w-100 align-items-center justify-content-between">
                        <strong>Recent News</strong>
                    </div>
                    <div class="popular_article_list">
                        @if($recent_posts)
                        @foreach($recent_posts as $resent_post)
                        <a class="popular_single_article" href="{{route('news.detail',$resent_post->slug)}}">
                            <figure class="w-100">

                                <img class="w-100 h-100 object-fit-cover"
                                    src="{{ $resent_post->cover_image ? asset('storage/blog/'.$resent_post->cover_image) : asset('website/images/logo.png') }}"
                                    alt="{{$resent_post->alt_text_cover_image}}" loading="lazy" decoding="async" width="250" height="210" />
                            </figure>
                            <div class="popular_single_article_details d-flex w-100 flex-column">
                                <h3>{{$resent_post->title}}</h3>
                                <div class="popular_single_article_inner_details d-flex align-items-center">
                                    <span>{{customDate($resent_post->created_at,'F d, Y')}}</span>

                                </div>
                                <strong>Read News</strong>
                            </div>
                        </a>
                        @endforeach
                        @endif
                    </div>
                </div>
            </div>


        </div>

    </div>

</div>
@endsection

@push('js')
@endpush