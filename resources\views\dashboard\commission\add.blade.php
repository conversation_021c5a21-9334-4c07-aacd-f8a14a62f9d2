   <!-- Offcanvas to add new package -->
   <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddCommission"
       aria-labelledby="offcanvasAddCategoryLabel">
       <!-- Offcanvas Header -->
       <div class="offcanvas-header py-2 flex-column">
           <div class="d-flex align-content-center justify-content-between w-100">
               <h5 id="offcanvasAddCommissionLabel" class="offcanvas-title">Add Commission</h5>
               <button type="button" class="btn-close bg-label-secondary text-reset" data-bs-dismiss="offcanvas"
                   aria-label="Close"></button>
           </div>
         
       </div>
       <!-- Offcanvas Body -->
       <div class="offcanvas-body border-top">
           <form class="pt-0" id="commission_form">
               @csrf


               <div class="d-grid gap-3 w-100">

                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="title">Country</label>
                       <input type="text" class="custom_form_field" name="country" id="country" />
                   </div>
                   <div class="form_field_box d-grid gap-2 w-100">
                       <label for="title">Commission</label>
                       <input type="text" class="custom_form_field" name="commission" id="commission" />
                   </div>


                  

                 
                   <!-- Submit and reset -->
                   <div class="d-grid gap-2 w-100">
                       <button type="submit" class="custom_btn_2">Create
                           <div class="form_loader position-absolute top-0 w-100 h-100 d-none align-items-center justify-content-center"
                               style="background-color: var(--secondary-bg-color);">
                               <img class="loader" src="{{asset('dashboard/img/loader.gif')}}" style="width:30px">
                           </div>
                       </button>

                       <button type="reset" class="custom_btn_3 w-100" data-bs-dismiss="offcanvas">Discard</button>
                   </div>
               </div>


           </form>
       </div>
   </div>