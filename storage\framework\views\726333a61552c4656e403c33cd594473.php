

<?php $__env->startSection("wrapper"); ?>
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / banners / banners list</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddWebBanner"><span>Add
                    Banner</span></button>
        </div>
        <table id="newsletter_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 120px;">Desktop Banner</th>
                    <th style="min-width: 200px;">Mobile Banner</th>
                   <th style="min-width: 200px;">Link</th>
                   <th style="min-width: 200px;">Alt Text</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $webBanner; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $bannerList): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="banner_row">
                    <td></td>
                    <td><img src="<?php echo e($bannerList->desktop_image ? asset('storage/banner/'.$bannerList->desktop_image) : ''); ?>"
                            class=" object-fit-cover" alt="<?php echo e($bannerList->alt_text); ?>" loading="lazy" decoding="async" width="270" height="300" />
                    </td>
                    <td><img src="<?php echo e($bannerList->mobile_image ? asset('storage/banner/'.$bannerList->mobile_image) : ''); ?>"
                            class=" object-fit-cover" alt="<?php echo e($bannerList->alt_text); ?>" loading="lazy" decoding="async" width="270" height="300" />
                    </td>
                    <td><?php echo e($bannerList->link); ?></td>
                    <td><?php echo e($bannerList->alt_text); ?></td>
                   
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                            

                                <button class="update_btn edit_banner" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasUpdateBanner<?php echo e($bannerList->id); ?>"
                                    data-form-id="update_banner_<?php echo e($bannerList->id); ?>" data-id="<?php echo e($bannerList->id); ?>">
                                    <i class="far fa-edit"></i> Update
                                </button>
                                    <button data-id="<?php echo e($bannerList->id); ?>" type="button"
                                    class="delete_btn delete_banner">Delete</button>
                            </div>
                        </div>
                        <?php echo $__env->make("dashboard.banner.updateBanner", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    </td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>

</div>
<?php echo $__env->make("dashboard.banner.delete", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make("dashboard.banner.addWebBanner", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<?php $__env->stopSection(); ?>
<?php echo $__env->make("dashboard.include.layout", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\travelafrica\resources\views/dashboard/banner/webBanners.blade.php ENDPATH**/ ?>