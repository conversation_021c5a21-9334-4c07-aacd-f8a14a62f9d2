

$(document).ready(function () {
    $(".dropify").dropify({
    messages: {
        default: "File",
        replace: "Drag and drop or click to replace",
        remove: "Remove",
        error: "Ooops, something wrong happended.",
    },
});
    $(".res_sidebar_toggle_btn").click(function () {
        $(".pofile_sidebar_tab_section").toggleClass("active");
    });

    $("#update_user_profile").validate({
        rules: {
            name: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);

            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let id = $(form).find('[name="id"]').val();
            let apiEndpoint = "/user/profile/update/" + id;
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("updated successfully!", "success");
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");

                    showAlert("An error occurred while updating.", "error");
                },
            });
        },
    });

    $("#update_user_password").validate({
        rules: {
            current_password: {
                required: true,
            },
            new_password: {
                required: true,
                minlength: 8,
            },
            new_password_confirmation: {
                required: true,
                equalTo: "#new_password",
                minlength: 8,
            },
        },
        messages: {
            current_password: {
                required: "Current password is required",
                minlength: "Please enter your current password",
            },
            new_password: {
                required: "New password is required",
                minlength: "Password must be at least 8 characters long",
            },
            new_password_confirmation: {
                required: "Please confirm your new password",
                equalTo: "Please enter the same password as above",
                minlength: "Password must be at least 8 characters long",
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);

            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let id = $(form).find('[name="id"]').val();
            let apiEndpoint = "/user/profile/password/update/" + id;
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("updated successfully!", "success");
                    $(".invalid-feedback").remove();
                    setTimeout(function () {
                        window.location.href = response.route;
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");
                    console.error(xhr);
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 403) {
                        $(".invalid-feedback").remove();
                        $("#current_password")
                            .addClass("is-invalid")
                            .after(
                                '<div class="invalid-feedback">Current password is incorrect</div>'
                            );
                    }
                    showAlert(
                        "An error occurred contact your supprt team.",
                        "error"
                    );
                },
            });
        },
    });
});
