@extends("dashboard.include.layout")


@section("wrapper")


<div class="pagination-list d-flex w-100">
    <a> / Destination / Add Destination</a>
</div>


<form id="add_blog_form" method="post" enctype="multipart/form-data">
    <!-- Add Product -->
    <div class="d-flex mb-3 justify-content-end">

        <button type="submit" class="custom_btn_1">Add</button>
    </div>
    <div class="image_guide_list">
        <ul>
            <li>Upload Image must be less then 1 MB (10kb or 50 kb)</li>
            <li>Upload image of same dimension for all category and banners</li>
        </ul>
    </div>

    @csrf
    <div class="add_destination_main">


        <div class="add_destination_box_one d-flex flex-column">
            <div class="destination_single_box">

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Blog information</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="title">Title</label>
                            <input type="text" id="title" class="custom_form_field" placeholder="Enter title"
                                name="title">
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="cover_img">Cover Image (use dimension 400px by 400px)</label>
                            <input id="cover_img" type="file" name="cover_img" class="dropify" data-max-file-size="2M"
                                data-allowed-file-extensions="jpg jpeg png webp" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="alt_text_cover_image">Alt Text of Cover Image</label>
                            <input class="custom_form_field" name="alt_text_cover_image" id="alt_text_cover_image" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="banner_img">Banner Image (use dimension 2500px by 400px)</label>

                            <input id="banner_img" type="file" name="banner_img" class="dropify" data-max-file-size="2M"
                                data-allowed-file-extensions="jpg jpeg png pdf webp" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="alt_text_banner_image">Alt Text of Banner Image</label>
                            <input class="custom_form_field" name="alt_text_banner_image" id="alt_text_banner_image" />
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="short_description">Short Description</label>
                            <textarea id="short_description" class="custom_form_field w-100" rows="4"
                                name="short_description"></textarea>

                        </div>


                    </div>

                </div>



                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Description</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="description">Long Description</label>

                            <textarea id="description" class="blog_long_description w-100" rows="25"
                                name="description"></textarea>
                        </div>


                    </div>

                </div>





            </div>
        </div>


        <div class="add_destination_box_two d-flex flex-column">
            <div class="destination_single_box">

                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">
                        @auth
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="author_name">Author Name</label>
                            <input type="text" id="author_name" class="custom_form_field " placeholder="Enter name"
                                name="author_name" value="{{Auth::user()->name}}">
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="author_image">Author Image</label>
                            <input id="author_image" type="file" name="author_image" class="dropify"
                                data-max-file-size="2M" data-allowed-file-extensions="jpg jpeg png gif webp"
                                data-default-file="{{Auth::user()->profile_image ? asset('storage/userImages/'.Auth::user()->profile_image) : ''}}" />
                        </div>
                        @endauth
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="type">Type</label>
                            <select id="type" class="custom_form_field" name="type">
                                <option value="blog" selected>
                                    Blog</option>
                                <option value="news">News
                                </option>
                            </select>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="category">Category</label>

                            <select id="category" class="custom_form_field" name="category">
                                @foreach($categories as $category)

                                <option value="{{ $category->id }}"
                                    {{ $category->id == $category->id ? 'selected' : '' }}>
                                    {{ $category->title }}
                                </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="d-grid gap-3 w-100">

                            <div class="form_field_box d-grid gap-2 w-100">
                                <label for="tags">Tags List</label>
                                <input class="custom_tags_input custom_form_field" name="tags" id="tags"
                                    value="tag1,tag2,tag3" />
                            </div>

                        </div>



                    </div>
                </div>


                <div class="d-grid gap-3 w-100 bg_box">
                    <div class="card_heading">
                        <h5>SEO Details</h5>
                    </div>

                    <div class="d-grid gap-3 w-100">

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="page_h1_heading">Seo H1 heading (55 to 60 charactor) </label>
                            <label>Total character (<span class="h1_headingCharCount"></span>) </label>
                            <textarea class="custom_form_field page_h1_heading" id="page_h1_heading" type="text"
                                name="page_h1_heading" rows="4"></textarea>
                        </div>

                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_title">Meta Title (55 to 60 charactor)</label>
                            <label>Total character (<span class="meta_titleCharCount"></span>) </label>
                            <textarea class="custom_form_field meta_title" id="meta_title" type="text" name="meta_title"
                                rows="4"></textarea>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_description">Meta Description (155 to 160 charactor)</label>
                            <label>Total character (<span class="meta_descriptionCharCount"></span>) </label>
                            <textarea class="custom_form_field meta_description" id="meta_description" type="text"
                                name="meta_description" rows="4"></textarea>
                        </div>
                        <div class="form_field_box d-grid gap-2 w-100">
                            <label for="meta_keywords">Meta keywords (comma seperated)</label>
                            <textarea class="custom_form_field" id="meta_keywords" type="text" name="meta_keywords"
                                rows="4"></textarea>
                        </div>





                    </div>

                </div>



            </div>
        </div>

    </div>
</form>

@endsection
