function showAlert(message, type) {
    var alertElement = $("#alertMessage");
    var alertContent = $("#alertContent");
    alertContent.text(message);

    if (type === "success") {
        alertElement.addClass("alert-success show");
        setTimeout(function () {
            alertElement.removeClass("alert-success show");
        }, 3000);
    } else if (type === "error") {
        alertElement.addClass("alert-danger show");
        setTimeout(function () {
            alertElement.removeClass("alert-danger show");
        }, 3000);
    }
}

document.addEventListener("DOMContentLoaded", function () {
    const carousel = document.querySelector("#bannerSlider");
    if (!carousel) return;

    let startX = 0;
    let endX = 0;
    let isDragging = false;

    // --- Touch Support (Mobile) ---
    carousel.addEventListener("touchstart", function (e) {
        startX = e.changedTouches[0].screenX;
    });

    carousel.addEventListener("touchend", function (e) {
        endX = e.changedTouches[0].screenX;
        handleSwipe();
    });

    // --- Mouse Support (Desktop) ---
    carousel.addEventListener("mousedown", function (e) {
        isDragging = true;
        startX = e.clientX;
    });

    carousel.addEventListener("mouseup", function (e) {
        if (!isDragging) return;
        endX = e.clientX;
        isDragging = false;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50; // min px to trigger swipe
        const carouselInstance = bootstrap.Carousel.getInstance(carousel);

        if (endX < startX - swipeThreshold) {
            carouselInstance.next();
        }
        if (endX > startX + swipeThreshold) {
            carouselInstance.prev();
        }
    }
});
// featured tour slider js
var TrandingSlider = new Swiper(".featured_package_slider", {
    effect: "coverflow",
    grabCursor: true,
    centeredSlides: true,
    loop: true,
    slidesPerView: "auto",
    coverflowEffect: {
        rotate: 50,
        stretch: 0,
        depth: 200,
        modifier: 2.5,
    },
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
    },
    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },
});

// reviews slider js

const reviewSlider = new Swiper(".review_slider", {
    effect: "slide",
    loop: true,
    speed: 1000,
    parallax: true,
    // autoplay: {
    //     delay: 6500,
    //     disableOnInteraction: false,
    // },
    watchSlidesProgress: true,
    pagination: {
        el: ".swiper-pagination",
        clickable: true,
    },

    navigation: {
        nextEl: ".swiper-button-next",
        prevEl: ".swiper-button-prev",
    },

    // Responsive breakpoints
    breakpoints: {
        // 640: {
        //   slidesPerView: 1.25,
        //   spaceBetween: 20
        // },
        // 1024: {
        //   slidesPerView: 2,
        //   spaceBetween: 20
        // }
    },
});

$(document).ready(function () {
    $(document).on("click", ".fa-eye-slash, .fa-eye", function () {
        let passwordInput = $(this).closest("div").find(".password");
        let isPasswordVisible = passwordInput.attr("type") === "password";
        passwordInput.attr("type", isPasswordVisible ? "text" : "password");
        if (isPasswordVisible) {
            $(this).removeClass("fa-eye-slash").addClass("fa-eye");
        } else {
            $(this).removeClass("fa-eye").addClass("fa-eye-slash");
        }
    });

    $(".package_detail_tabs a").on("click", function (e) {
        $(".package_detail_tabs a").removeClass("active");
        $(this).addClass("active");
    });

    $(".header_toggle_btn,.res_toggle_button").click(function () {
        $(".header_links").toggleClass("active");
    });

    $(".brand_slider").slick({
        slidesToShow: 5,
        slidesToScroll: 1,
        autoplay: false,
        autoplaySpeed: 4000,
        dots: false,
        arrows: false,
        lazyLoad: true,
        cssEase: "ease-in-out",
        touchThreshold: 100,
        responsive: [
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 3,
                },
            },
            {
                breakpoint: 540,
                settings: {
                    slidesToShow: 2,
                },
            },
        ],
    });

    $(".tour_package_slider").slick({
        slidesToShow: 3,
        slidesToScroll: 1,
        autoplay: false,
        autoplaySpeed: 4000,
        dots: true,
        arrows: false,
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 2,
                },
            },
            {
                breakpoint: 767,
                settings: {
                    slidesToShow: 2,
                },
            },
            {
                breakpoint: 540,
                settings: {
                    slidesToShow: 1,
                },
            },
        ],
    });

    $(window).on("load", function () {
        $(".product_category_slider").slick({
            slidesToShow: 4,
            slidesToScroll: 1,
            autoplay: false,
            autoplaySpeed: 2000,
            dots: false,
            arrows: true,
            appendArrows: $(".category_arrow_box"),
            nextArrow:
                '<div class="arrow_box category_right_arrow"><a class="d-flex align-items-center  justify-content-center"><i class="fa fa-angle-right" aria-hidden="true"></i></a></div>',
            prevArrow:
                '<div class="arrow_box category_left_arrow"><a class="d-flex align-items-center  justify-content-center"><i class="fa fa-angle-left" aria-hidden="true"></i></a></div>',
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        autoplay: false,
                        autoplaySpeed: 1500,
                        dots: false,
                        arrows: true,
                    },
                },
                {
                    breakpoint: 767,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        autoplay: false,
                        autoplaySpeed: 1500,
                        dots: false,
                        arrows: true,
                        centerMode: true,
                        centerPadding: "20px",
                    },
                },
                {
                    breakpoint: 540,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        autoplay: false,
                        autoplaySpeed: 1500,
                        dots: false,
                        arrows: true,
                        centerMode: true,
                        centerPadding: "20px",
                    },
                },
            ],
        });
        // Hide Skeleton
        $("#category-skeleton").hide();

        // Show Real Content
        $(".product_category_list_show").fadeIn(600);
    });

    $(window).on("load", function () {
        $(".product_type_slider").slick({
            slidesToShow: 4,
            slidesToScroll: 1,
            autoplay: false,
            autoplaySpeed: 2000,
            dots: false,
            arrows: true,
            appendArrows: $(".type_arrow_box"),
            nextArrow:
                '<div class="arrow_box category_right_arrow"><a class="d-flex align-items-center  justify-content-center"><i class="fa fa-angle-right" aria-hidden="true"></i></a></div>',
            prevArrow:
                '<div class="arrow_box category_left_arrow"><a class="d-flex align-items-center  justify-content-center"><i class="fa fa-angle-left" aria-hidden="true"></i></a></div>',
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 3,
                        slidesToScroll: 1,
                        autoplay: false,
                        autoplaySpeed: 1500,
                        dots: false,
                        arrows: true,
                    },
                },
                {
                    breakpoint: 767,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        autoplay: false,
                        autoplaySpeed: 1500,
                        dots: false,
                        arrows: true,
                        centerMode: true,
                        centerPadding: "20px",
                    },
                },
                {
                    breakpoint: 540,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        autoplay: false,
                        autoplaySpeed: 1500,
                        dots: false,
                        arrows: true,
                        centerMode: true,
                        centerPadding: "20px",
                    },
                },
            ],
        });

        // Hide Skeleton
        $("#type-skeleton").hide();

        // Show Real Content
        $(".product_type_list_show").fadeIn(600);
    });

    $(".related_blog_slider").slick({
        slidesToShow: 4,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 4000,
        dots: false,
        arrows: false,
        appendArrows: $(".brand_slider_arrow_box"),
        nextArrow:
            '<a class="slider_arrow"><i class="fa fa-angle-right" aria-hidden="true"></i></a>',
        prevArrow:
            '<a class="slider_arrow"><i class="fa fa-angle-left" aria-hidden="true"></i></a>',
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 4000,
                    dots: false,
                    arrows: false,
                },
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 4000,
                    dots: false,
                    arrows: false,
                },
            },
            {
                breakpoint: 540,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 4000,
                    dots: false,
                    arrows: false,
                },
            },
        ],
    });

    $(".related_destination_slider").slick({
        slidesToShow: 4,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 4000,
        dots: false,
        arrows: false,
        appendArrows: $(".brand_slider_arrow_box"),
        nextArrow:
            '<a class="slider_arrow"><i class="fa fa-angle-right" aria-hidden="true"></i></a>',
        prevArrow:
            '<a class="slider_arrow"><i class="fa fa-angle-left" aria-hidden="true"></i></a>',
        responsive: [
            {
                breakpoint: 1024,
                settings: {
                    slidesToShow: 3,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 4000,
                    dots: false,
                    arrows: false,
                },
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 4000,
                    dots: false,
                    arrows: false,
                },
            },
            {
                breakpoint: 540,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    autoplay: true,
                    autoplaySpeed: 4000,
                    dots: false,
                    arrows: false,
                },
            },
        ],
    });

    // $(".blog_slider").slick({
    //   slidesToShow: 3,
    //   slidesToScroll: 1,
    //   autoplay: true,
    //   autoplaySpeed: 4000,
    //   dots: false,
    //   arrows: true,
    //   appendArrows: $(".blog_slider_arrow_box"),
    //   nextArrow:
    //     '<a class="blog_pre_arrow"><i class="fa fa-angle-right" aria-hidden="true"></i></a>',
    //   prevArrow:
    //     '<a class="blog_pre_arrow"><i class="fa fa-angle-left" aria-hidden="true"></i></a>',
    //   responsive: [
    //     {
    //       breakpoint: 1024,
    //       settings: {
    //         slidesToShow: 3,
    //         slidesToScroll: 1,
    //         autoplay: true,
    //         dots: false,
    //         arrows: true,
    //       },
    //     },
    //     {
    //       breakpoint: 767,
    //       settings: {
    //         slidesToShow: 2,
    //         slidesToScroll: 1,
    //         autoplay: true,
    //         dots: false,
    //         arrows: true,
    //       },
    //     },
    //     {
    //       breakpoint: 540,
    //       settings: {
    //         slidesToShow: 2,
    //         slidesToScroll: 1,
    //         autoplay: true,
    //         dots: false,
    //         arrows: true,
    //       },
    //     },
    //     {
    //       breakpoint: 400,
    //       settings: {
    //         slidesToShow: 1,
    //         slidesToScroll: 1,
    //         autoplay: true,
    //         dots: false,
    //         arrows: true,
    //       },
    //     },
    //   ],
    // });

    // $(".reviews_slider").slick({
    //   slidesToShow: 2,
    //   slidesToScroll: 1,
    //   autoplay: false,
    //   autoplaySpeed: 4000,
    //   dots: false,
    //   arrows: true,
    //   appendArrows: $(".reviews_slider_arrow_box"),
    //   nextArrow:
    //     '<a class="reviews_pre_arrow"><i class="fa fa-angle-right" aria-hidden="true"></i></a>',
    //   prevArrow:
    //     '<a class="reviews_pre_arrow"><i class="fa fa-angle-left" aria-hidden="true"></i></a>',
    //   responsive: [
    //     {
    //       breakpoint: 1024,
    //       settings: {
    //         slidesToShow: 3,
    //         slidesToScroll: 1,
    //         autoplay: false,
    //         dots: false,
    //         arrows: true,
    //       },
    //     },
    //     {
    //       breakpoint: 767,
    //       settings: {
    //         slidesToShow: 2,
    //         slidesToScroll: 1,
    //         autoplay: false,
    //         dots: false,
    //         arrows: true,
    //       },
    //     },
    //     {
    //       breakpoint: 540,
    //       settings: {
    //         slidesToShow: 2,
    //         slidesToScroll: 1,
    //         autoplay: false,
    //         dots: false,
    //         arrows: true,
    //       },
    //     },
    //     {
    //       breakpoint: 400,
    //       settings: {
    //         slidesToShow: 1,
    //         slidesToScroll: 1,
    //         autoplay: false,
    //         dots: false,
    //         arrows: true,
    //       },
    //     },
    //   ],
    // });

    var $review_slider = $(".reviews_slider");
    var $progressBar = $(".slider_progress");
    var $progressBarLabel = $(".slider__label");

    $review_slider.on(
        "beforeChange",
        function (event, slick, currentSlide, nextSlide) {
            var calc = (nextSlide / (slick.slideCount - 1)) * 100;

            $progressBar
                .css("background-size", calc + "% 100%")
                .attr("aria-valuenow", calc);
        }
    );

    $review_slider.slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        speed: 400,
    });

    // image drag and drop js

    $(".drag_and_drop_box").on("dragover", function (e) {
        e.preventDefault();
    });

    $(".drag_and_drop_box").on("dragenter", function () {
        $(this).addClass("drag-active");
    });

    $(".drag_and_drop_box").on("dragleave", function () {
        $(this).removeClass("drag-active");
    });

    $(".drag_and_drop_box").on("drop", function (e) {
        e.preventDefault();
        $(this).removeClass("drag-active");
        const input = $(this).find(".drag_and_drop_input");
        input.prop("files", e.originalEvent.dataTransfer.files);
    });

    $(".dropify").dropify({
        messages: {
            default: "File",
            replace: "Drag and drop or click to replace",
            remove: "Remove",
            error: "Ooops, something wrong happended.",
        },
    });
});

// footer responsive toggle script

var acc = document.getElementsByClassName("accordion");
var i;

for (i = 0; i < acc.length; i++) {
    acc[i].addEventListener("click", function () {
        this.classList.toggle("active");
        var panel = this.nextElementSibling;
        if (panel.style.display === "block") {
            panel.style.display = "none";
        } else {
            panel.style.display = "block";
        }
    });
}

$(document).ready(function () {
    $("#contact_submit").validate({
        rules: {
            name: {
                required: true,
            },
            email: {
                required: true,
                email: true,
            },
            phone: {
                required: true,
            },
            message: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/contact/submit";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    showAlert(
                        "Thank you for contacting us! Your message has been successfully submitted. We will get back to you shortly.",
                        "success"
                    );
                    setTimeout(function () {
                        window.location.href = response.route;
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    $(".invalid-feedback").remove();
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        showAlert("Recaptcha field is missing.", "error");
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="contact-error invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else {
                        showAlert(
                            "An error occurred while submitting.",
                            "error"
                        );
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    $("#newsletter_submit").validate({
        rules: {
            email: {
                required: true,
                email: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/newsletter/submit";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    showAlert(
                        "Thank you for subscribing our newsletter.",
                        "success"
                    );
                    setTimeout(function () {
                        window.location.href = response.route;
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");

                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        showAlert("Recaptcha field is missing.", "error");
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="newsletter-error invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 403) {
                        showAlert(
                            "Email already subscribed to our newsletter",
                            "error"
                        );
                        form.reset();
                    } else {
                        showAlert(
                            "An error occurred please contact your support team",
                            "error"
                        );
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    $("#submit_comment").validate({
        rules: {
            name: {
                required: true,
            },
            email: {
                required: true,
                email: true,
            },
            message: {
                required: true,
            },
            // "g-recaptcha-response": {
            //     required: true,
            // },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/blog/comment/submit";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    showAlert(
                        "Your comment has been submitted and is currently under review.",
                        "success"
                    );
                    setTimeout(function () {
                        // window.location.href = response.route;
                        window.location.reload();
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");

                    showAlert("An error occurred while updating.", "error");
                },
            });
        },
    });
});

$(".filter_toggle_btn,.filter_close_btn").click(function () {
    $(".filters_list").toggleClass("active");
});
$(document).mouseup(function (e) {
    var container_two = $(".filters_list.active");
    if (
        !container_two.is(e.target) &&
        container_two.has(e.target).length === 0
    ) {
        container_two.removeClass("active");
    }
});

//-----JS for Price Range slider-----

$(function () {
    $("#filter_slider_range").slider({
        range: true,
        min: 0,
        max: 5000,
        values: [50, 5000],
        slide: function (event, ui) {
            $("#amount").val(ui.values[0] + " - " + ui.values[1]);
        },
    });
    $("#apply_price_filter_btn").click(function () {
        $("#price_range_form").submit();
    });
    $("#amount").val(
        $("#filter_slider_range").slider("values", 0) +
            " - " +
            $("#filter_slider_range").slider("values", 1)
    );
});

$(document).ready(function () {
    // Function to filter destinations based on selected categories, types, and price range
    function filterDestinations() {
        // Get selected category IDs
        var categoryIds = [];
        $('input[name="category[]"]:checked').each(function () {
            categoryIds.push($(this).val());
        });

        // Get selected type IDs
        var typeIds = [];
        $('input[name="type[]"]:checked').each(function () {
            typeIds.push($(this).val());
        });

        // Filter destinations
        $(".single_product").each(function () {
            var categoryId = $(this).data("category-id");
            var typeId = $(this).data("type-id");

            var categoryMatch =
                categoryIds.length === 0 ||
                categoryIds.includes(categoryId.toString());
            var typeMatch =
                typeIds.length === 0 || typeIds.includes(typeId.toString());

            if (categoryMatch && typeMatch) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    // Listen for changes in category and type checkboxes
    $('input[name="category[]"], input[name="type[]"]').change(function () {
        filterDestinations();
    });
});

$(document).ready(function () {
    $(".form_dates_listing li").click(function () {
        // Remove all "selected" classes from previous selections
        $(".form_dates_listing li").removeClass("selected");

        // Add "selected" class to the clicked list item
        $(this).addClass("selected");

        var selectedDate = $(this).data("date");
        var selectedSlots = $(this).data("slots");

        // Set the value of the hidden input to the selected date
        $("#date").val(selectedDate);
        $("#slots").val(selectedSlots);
    });
});

$(document).ready(function () {
    $(".booking_toggle_btn").click(function () {
        $(".destination_booking_form").toggleClass("active");
    });
});

$(document).ready(function () {
    var mobile_num = $("#mobile_code");

    $("#destination_booking_form").validate({
        rules: {
            phone: {
                required: true,
            },
            date: {
                required: true,
            },
            email: {
                required: true,
            },
            name: {
                required: true,
            },
            tickets: {
                required: true,
            },
            adultes: {
                required: true,
            },
            children: {
                required: true,
            },
            terms: {
                required: true,
            },
            messages: {
                // Add a custom message for the date field
                date: "Please select a departure date.",
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/destination/booking/submit";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            var countryCode = mobile_num.intlTelInput(
                "getSelectedCountryData"
            ).dialCode;
            var phoneNumber = mobile_num.val();
            // Combine country code and phone number
            var fullPhoneNumber = "+" + countryCode + phoneNumber;

            // Append full phone number to form data
            formData.append("full_phone_number", fullPhoneNumber);

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    // Use the message from the server response
                    if (response.success) {
                        showAlert(response.success, "success");
                    } else {
                        showAlert(
                            "Your request has been submitted. Our team will contact you within 24 hours.",
                            "success"
                        );
                    }
                    setTimeout(function () {
                        // window.location.href = response.route;
                        window.location.reload();
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    $(".invalid-feedback").remove();
                    if (xhr.status === 422) {
                        showAlert("some required fields are missing.", "error");
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 401) {
                        showAlert(
                            "login with your account to submit booking request.",
                            "error"
                        );
                    } else if (xhr.status === 402) {
                        console.log("error test");
                        showAlert(
                            "Number of participants must be less than or equal to available slots",
                            "error"
                        );
                        $("#tickets")
                            .addClass("is-invalid")
                            .after(
                                '<div class="invalid-feedback">Number of participants must be less than or equal to available slots</div>'
                            );
                    } else if (xhr.status === 403) {
                        showAlert(
                            "Adultes + Children must be equal to number of participants.",
                            "error"
                        );
                        $("#total_value")
                            .addClass("is-invalid")
                            .after(
                                '<div class="invalid-feedback">Adultes + Children must be equal to number of participants.</div>'
                            );
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    var phone_num = $("#phone_code");

    $("#private_destination_booking_form").validate({
        rules: {
            phone: {
                required: true,
            },
            date: {
                required: true,
            },
            tickets: {
                required: true,
            },
            adultes: {
                required: true,
            },
            children: {
                required: true,
            },
            terms: {
                required: true,
            },
            messages: {
                // Add a custom message for the date field
                date: "Please select a departure date.",
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/destination/booking/submit";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            var countryCode = phone_num.intlTelInput(
                "getSelectedCountryData"
            ).dialCode;
            var phoneNumber = phone_num.val();
            // Combine country code and phone number
            var fullPhoneNumber = "+" + countryCode + phoneNumber;

            // Append full phone number to form data
            formData.append("full_phone_number", fullPhoneNumber);

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    showAlert(
                        "Your request has been submitted . Our team contact you with in 24 hours.",
                        "success"
                    );
                    setTimeout(function () {
                        // window.location.href = response.route;
                        window.location.reload();
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    $(".invalid-feedback").remove();
                    if (xhr.status === 422) {
                        showAlert("some required fields are missing.", "error");
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 401) {
                        showAlert(
                            "login with your account to submit booking request.",
                            "error"
                        );
                    } else if (xhr.status === 402) {
                        console.log("error test");
                        showAlert(
                            "Number of participants must be less than or equal to available slots",
                            "error"
                        );
                        $("#tickets")
                            .addClass("is-invalid")
                            .after(
                                '<div class="invalid-feedback">Number of participants must be less than or equal to available slots</div>'
                            );
                    } else if (xhr.status === 403) {
                        showAlert(
                            "Adultes + Children must be equal to number of participants.",
                            "error"
                        );
                        $("#total_value")
                            .addClass("is-invalid")
                            .after(
                                '<div class="invalid-feedback">Adultes + Children must be equal to number of participants.</div>'
                            );
                    }
                },
            });
        },
    });
});

$("#custom_package_date_range").flatpickr({
    altInput: true,
    altFormat: "F j, Y",
    dateFormat: "Y-m-d",
    mode: "range",
});

function initSelect2() {
    $(".custom_multi_select_dropdown").select2({
        dataPlaceholder: "Select an option",
        allowClear: false,
        multiple: true,
    });
}
initSelect2();

$(document).ready(function () {
    // Function to fetch and set the country code based on user's IP
    function setCountryBasedOnIP() {
        $.get("/get-country-code", function (response) {
            console.log("Country Code Response:", response);

            var countryCode = response.countryCode
                ? response.countryCode
                : "US"; // Default to 'US' if country code not found

            // Initialize intlTelInput with the country code
            $("#number_code,#phone_code,#mobile_code").intlTelInput({
                initialCountry: countryCode.toLowerCase(), // Convert to lowercase as required
                separateDialCode: true,
                // Uncomment and use the utilsScript if needed for formatting
                // utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/utils.js"
            });
        });
    }

    // Call the function to set the country code
    setCountryBasedOnIP();
});

$(document).ready(function () {
    var number_code = $("#number_code");

    $("#custom_package_form").validate({
        rules: {
            number: {
                required: true,
            },
            country: {
                required: true,
            },
            message: {
                required: true,
            },

            total_participant: {
                required: true,
            },
            total_adult: {
                required: true,
            },
            total_children: {
                required: true,
            },
            days: {
                required: true,
            },
            from_to_date: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/destination/custom/request";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            var countryCode = number_code.intlTelInput(
                "getSelectedCountryData"
            ).dialCode;
            var phoneNumber = number_code.val();
            // Combine country code and phone number
            var fullPhoneNumber = "+" + countryCode + phoneNumber;

            // Append full phone number to form data
            formData.append("full_phone_number", fullPhoneNumber);

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    showAlert(
                        "Your request has been submitted our team contact you with in 24 hours.",
                        "success"
                    );
                    setTimeout(function () {
                        // window.location.href = response.route;
                        window.location.reload();
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    $(".invalid-feedback").remove();
                    if (xhr.status === 422) {
                        showAlert(
                            "some required fields are missing !",
                            "error"
                        );
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 401) {
                        showAlert(
                            "login with your account to submit booking request.",
                            "error"
                        );
                    }
                },
            });
        },
    });
});

$(document).ready(function () {
    $("#payment_history,#booking_history").DataTable({
        dom: "lBfrtip",
        responsive: true,
        scrollX: true, // Enable horizontal scrolling
        fixedColumns: {
            leftColumns: 1, // Number of columns to fix on the left
        },
        buttons: [
            {
                extend: "collection",
                text: '<i class="fal fa-cloud-download"></i> Export',
                buttons: [
                    {
                        extend: "print",
                        text: '<i class="fal fa-print"></i> Print',
                        exportOptions: {
                            columns: ":visible",
                        },
                    },
                    {
                        extend: "csvHtml5",
                        text: '<i class="fal fa-file-csv"></i> CSV',
                        exportOptions: {
                            columns: ":visible",
                        },
                    },
                    {
                        extend: "pdfHtml5",
                        text: '<i class="fal fa-file-pdf"></i> PDF',
                        exportOptions: {
                            columns: ":visible",
                        },
                    },

                    {
                        extend: "copyHtml5",
                        text: '<i class="far fa-copy"></i> Copy',
                        exportOptions: {
                            columns: ":visible",
                        },
                    },
                ],
                className: "export-buttons",
            },
        ],

        //  pagingType: "full_numbers",
        language: {
            lengthMenu:
                '<select name="package_listing_length" aria-controls="example" class="">' +
                '<option value="10">10</option>' +
                '<option value="25">25</option>' +
                '<option value="50">50</option>' +
                '<option value="200">200</option>' +
                '<option value="-1">All</option>' +
                "</select>",
            search: "<label></label>",
            searchPlaceholder: "Search...",
        },
        pageLength: 10,
        select: {
            style: "multi",
        },

        select: {
            style: "os",
            selector: "td:first-child",
        },
        order: [[1, "asc"]],
        columnDefs: [
            {
                targets: 0, // Target the first column
                orderable: false,
                className: "select-checkbox",
                render: function (data, type, full, meta) {
                    if (type === "display") {
                        return '<input type="checkbox" class="select-checkbox custom_design_checkbox">';
                    }
                    return data;
                },
            },
            { targets: [0, -1], orderable: false }, // Disable sorting for the first and last columns
        ],
        // Add checkbox in the thead
        initComplete: function () {
            this.api()
                .columns()
                .every(function () {
                    var column = this;
                    if (column.index() === 0) {
                        var header = $(column.header());
                        header.html(
                            '<input type="checkbox" class="select-all-checkbox custom_design_checkbox">'
                        );
                    }
                });
        },
    });
    // Select all checkboxes
    $(document).on("change", ".select-all-checkbox", function () {
        var checked = $(this).prop("checked");
        $("#package_listing .select-checkbox").prop("checked", checked);
    });
});

document.addEventListener("DOMContentLoaded", function () {
    var galleryImages = document.querySelectorAll(".destination_gallery img");
    var imageModal = new bootstrap.Modal(document.getElementById("imageModal"));
    var modalImage = document.getElementById("modalImage");
    var currentImageIndex = -1;

    // Function to update the modal image
    function updateModalImage(index) {
        if (index >= 0 && index < galleryImages.length) {
            modalImage.src = galleryImages[index].src;
            currentImageIndex = index;
        }
    }

    // Add click event listeners to gallery images
    galleryImages.forEach(function (img, index) {
        img.addEventListener("click", function () {
            updateModalImage(index);
            imageModal.show();
        });
    });

    // Handle previous image button
    document.getElementById("prevImage").addEventListener("click", function () {
        var prevIndex = currentImageIndex - 1;
        if (prevIndex >= 0) {
            updateModalImage(prevIndex);
        }
    });

    // Handle next image button
    document.getElementById("nextImage").addEventListener("click", function () {
        var nextIndex = currentImageIndex + 1;
        if (nextIndex < galleryImages.length) {
            updateModalImage(nextIndex);
        }
    });
});

$(document).ready(function () {
    $("#search").on("keyup", function () {
        var query = $(this).val();
        // Dynamically retrieve base URL
        let baseURL = window.location.origin;
        // API endpoint path
        let apiEndpoint = "/destination/search";
        // Concatenate base URL with API endpoint path
        let apiUrl = baseURL + apiEndpoint;

        $.ajax({
            url: apiUrl,
            type: "GET",
            data: { query: query },
            success: function (data) {
                $("#product-list").html("");

                if (data.length > 0) {
                    console.log("data", data);
                    $.each(data, function (index, product) {
                        $("#product-list").append(`
                                    <a href="/destination/detail/${product.slug}" class="search_listing">
                                        <h2>${product.title}</h2>
                                         <h2>country: ${product.destination_country}</h2>
                                    </a>
                            `);
                    });
                } else {
                    $("#product-list").append("<p>No products found.</p>");
                }
            },
        });
    });
});

$(document).ready(function () {
    $("#review_submit").validate({
        rules: {
            name: {
                required: true,
            },
            message: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();

            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let apiEndpoint = "/api/review/submit";
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");

                    showAlert(
                        "Your review has been successfully submitted.",
                        "success"
                    );
                    setTimeout(function () {
                        window.location.href = "/home";
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");
                    $(".invalid-feedback").remove();
                    if (xhr.status === 422) {
                        $(".form_process_loader").addClass("d-none");
                        showAlert("System error please try later.", "error");
                    } else {
                        $(".form_process_loader").addClass("d-none");
                        showAlert("An error occurred.", "error");
                    }
                },
            });
        },
    });
});

document.addEventListener('DOMContentLoaded', function () {
    const sections = document.querySelectorAll('.package_detail_box');
    const navLinks = document.querySelectorAll('.package_detail_tabs a');
    const tabWrapper = document.querySelector('.package_detail_tabs_wrapper');
    const OFFSET = 68; // Adjust based on header height

    // Smooth scroll with offset
    navLinks.forEach(link => {
        link.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            const scrollTo = targetSection.offsetTop - OFFSET;
            window.scrollTo({ top: scrollTo, behavior: 'smooth' });
        });
    });

    // Scroll active tab into view horizontally
    function scrollTabIntoView(activeLink) {
        if (tabWrapper && activeLink) {
            const wrapperRect = tabWrapper.getBoundingClientRect();
            const linkRect = activeLink.getBoundingClientRect();

            if (linkRect.left < wrapperRect.left || linkRect.right > wrapperRect.right) {
                tabWrapper.scrollBy({
                    left: linkRect.left - wrapperRect.left - 20,
                    behavior: 'smooth'
                });
            }
        }
    }

    // Manual scroll tracking for scrollspy
    window.addEventListener('scroll', () => {
        let currentSectionId = '';

        sections.forEach(section => {
            const sectionTop = section.offsetTop - OFFSET;
            const sectionHeight = section.offsetHeight;

            if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                currentSectionId = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            const isActive = link.getAttribute('data-target') === currentSectionId;
            link.classList.toggle('active', isActive);
            if (isActive) scrollTabIntoView(link);
        });
    });
});

