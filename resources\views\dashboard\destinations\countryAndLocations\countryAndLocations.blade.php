@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / destinations / country and locations / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddDestinationCountryLocation"><span>Add
                    Country & Locations</span></button>
        </div>
        <table id="country_location_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 120px;">Country</th>
                    <th style="min-width: 120px;">City</th>
                    <th style="min-width: 330px;">Locations</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($countryAndLocations as $destinationCountryAndLocations)
                <tr class="country_location_row">
                    <td></td>
                    <td>{{ $destinationCountryAndLocations->country }}</td>
                    <td>{{ $destinationCountryAndLocations->city }}</td>
                    <td>
                        <div class="d-flex align-items-center flex-wrap">
                            @php
                            $countryArray = json_decode($destinationCountryAndLocations->locations, true);
                            @endphp

                            @foreach ($countryArray as $data)
                            <p class=" m-2 p-2" style="background-color: var(--ternary-bg-color);">{{ $data['value'] }}
                            </p>
                            @endforeach
                        </div>
                    </td>

                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $destinationCountryAndLocations->id }}" type="button"
                                    class="delete_btn delete_country_location"><i class="fas fa-trash-alt"></i>
                                    Delete</button>

                                <button class="update_btn edit_country_location_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasUpdateCountryAndLocations{{$destinationCountryAndLocations->id}}"
                                    data-form-id="update_country_locations_{{$destinationCountryAndLocations->id}}"
                                    data-id="{{$destinationCountryAndLocations->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                            </div>
                        </div>
                        @include("dashboard.destinations.countryAndLocations.updateCountryLocations")

                    </td>
                </tr>

                @endforeach
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.destinations.countryAndLocations.addCountryLocations")
@include("dashboard.destinations.countryAndLocations.deleteCountryAndLocations")

@endsection
