@extends("dashboard.include.layout")


@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a> / Custom Trip / Requests</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">

        </div>
        <table id="custom_trip_request" class="data_table display" style="width:100%;">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 250px;">Status</th>
                    <th style="min-width: 60px;">Trip id</th>
                    <th style="min-width: 100px;">Name</th>
                    <th style="min-width: 100px;">Email</th>
                    <th style="min-width: 100px;">Phone</th>
                    <th style="min-width: 80px;">Participent</th>
                    <th style="min-width: 80px;">Adults</th>
                    <th style="min-width: 80px;">children</th>
                    <th style="min-width: 80px;">days</th>
                    <th style="min-width: 300px;">Message</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($customTrip as $tripList)
                <tr class="trip_row">
                    <td></td>
                    <td>
                        @if ($tripList->status === 'cancel')
                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #dc3545;">Cancelled
                            </p>
                        </div>
                        @elseif ($tripList->status === 'advancePaymentPending')
                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style=" background-color: #17a2b8;">Advance Payment
                                Pending
                            </p>
                        </div>
                        @elseif ($tripList->status === 'advancePaymentSuccess')
                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #28a745;">Advance Payment
                                Success
                            </p>

                            <button style=" background-color: #17a2b8;"
                                class="action_btn custom_trip_remaining_payment_link p-2" tabindex="0" type="button"
                                data-bs-toggle="offcanvas" data-bs-target="#offcanvasRemainingPayment{{$tripList->id}}"
                                data-form-id="remaining_payment_{{$tripList->id}}" data-id="{{$tripList->id}}">
                                Send Remaining Payment Link</button>
                        </div>

                        @elseif ($tripList->status === 'remainingPaymentPending')

                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #28a745;">Advance Payment
                                Success
                            </p>
                            <button style=" background-color: #17a2b8;"
                                class="action_btn custom_trip_remaining_payment_link p-2" tabindex="0" type="button"
                                data-bs-toggle="offcanvas" data-bs-target="#offcanvasRemainingPayment{{$tripList->id}}"
                                data-form-id="remaining_payment_{{$tripList->id}}" data-id="{{$tripList->id}}">
                                Send Remaining Payment Link</button>
                            <p class=" p-2" style="background-color:#ffc107; color:#000;">Remaining Payment
                                Pending
                            </p>
                        </div>

                        @elseif ($tripList->status === 'remainingPaymentSuccess')

                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #28a745;">All Payment Completed
                            </p>
                        </div>

                        @elseif ($tripList->status === 'completePaymentPending')

                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #17a2b8;">complete payment
                                pending
                            </p>
                        </div>

                        @elseif ($tripList->status === 'completePaymentSuccess')

                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color: #28a745;">complete payment
                                Done
                            </p>
                        </div>

                        @else
                        <div class="payment_status_parent d-grid gap-3">
                            <p class=" p-2" style="background-color:#ffc107; color:#000;">No Payment
                            </p>
                        </div>

                        @endif
                    </td>

                    <td>{{ $tripList->id }}</td>
                    <td>{{ $tripList->user->name }}</td>
                    <td>{{ $tripList->user->email }}</td>
                    <td>{{ $tripList->phone }}</td>
                    <td>{{ $tripList->total_participant}}</td>
                    <td>{{ $tripList->total_adults}}</td>
                    <td>{{ $tripList->total_children }}</td>
                    <td>{{ $tripList->total_days }}</td>

                    <td>{{ $tripList->message }}</td>


                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>


                            <div class="action_btn_group actions gap-3" style="display: none;">
                                
                                        
                                <a href="{{ route('dashboard.customTripRequest.updateCustomTrip', $tripList->id) }}"
                                    class="update_btn update_booking_detail"><i class="far fa-edit"></i>Update</a>
                                    
                                <a href="{{ url('/dashboard/custom/trip/details/'.$tripList->id) }}"
                                    class="detail_btn"><i class="fas fa-info"></i>Details</a>
                                <button class="cancle_btn custom_trip_cancle_reply_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasCancleReply{{$tripList->id}}"
                                    data-form-id="cancle_reply_{{$tripList->id}}" data-id="{{$tripList->id}}">
                                    <i class="fas fa-comment-dots"></i> Cancel Reply
                                </button>
                                <button class="payment_btn custom_trip_accept_reply_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasAcceptReply{{$tripList->id}}"
                                    data-form-id="accept_reply_{{$tripList->id}}" data-id="{{$tripList->id}}">
                                    <i class="fas fa-money-check"></i> Advance payment link </button>

                                <button class="payment_btn custom_trip_complete_payment_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasCompletePayment{{$tripList->id}}"
                                    data-form-id="complete_payment_{{$tripList->id}}" data-id="{{$tripList->id}}">
                                    <i class="fas fa-money-check"></i> Complete payment link </button>
                                    <button data-id="{{ $tripList->id }}" type="button" class="delete_btn delete_trip"><i
                                        class="fas fa-trash-alt"></i>Delete</button>

                            </div>
                        </div>
                        @include("dashboard.customTripRequest.completePayment")
                        @include("dashboard.customTripRequest.remainingPayment")
                        @include("dashboard.customTripRequest.cancleReply")
                        @include("dashboard.customTripRequest.acceptReply")
                    </td>
                </tr>

                @endforeach
            </tbody>
        </table>
    </div>

</div>

@include("dashboard.customTripRequest.delete")

@endsection
