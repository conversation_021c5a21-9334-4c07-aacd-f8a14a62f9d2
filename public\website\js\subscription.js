$(document).ready(function () {
    $("#subscription_form").validate({
        rules: {
            name: {
                required: true,
            },
            email: {
                required: true,
                email: true,
            },
            phone: {
                required: true,
            },
            subscription: {
                required: true,
            },
            country: {
                required: true,
            },
            address: {
                required: true,
            },
            company_name: {
                required: true,
            },
            company_description: {
                required: true,
            },
            licence_file: {
                required: true,
            },
        },

        submitHandler: function (form, e) {
            e.preventDefault();
            let baseURL = window.location.origin;
            let apiEndpoint = "/api/subscribe";
            let apiUrl = baseURL + apiEndpoint;
            var formData = new FormData(form); // Create FormData object from the form
            console.log(formData);
            $(".form_process_loader").removeClass("d-none");
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    // Handle success response
                    console.log(response);
                    form.reset();
                    $(".form_process_loader").addClass("d-none");
                    showAlert(
                        "Subscription request successfull we will get in touch you within 2 days.",
                        "success"
                    );
                    $(".invalid-feedback").remove();
                    setTimeout(function () {
                        window.location.href = response.route;
                    }, 3000);
                },
                error: function (xhr, status, error, responce) {
                    $(".form_process_loader").addClass("d-none");
                    console.error(xhr);
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 403) {
                        $(".form_process_loader").addClass("d-none");
                        showAlert(
                            "You have already submitted a subscription request.",
                            "error"
                        );
                        $(".invalid-feedback").remove();
                        setTimeout(function () {
                            window.location.href = "/home";
                        }, 2000);
                    } else if (xhr.status === 402) {
                        $(".form_process_loader").addClass("d-none");
                        showAlert("You are already our subscriber.", "error");
                        $(".invalid-feedback").remove();
                        setTimeout(function () {
                            window.location.href = "/home";
                        }, 2000);
                    }
                },
            });
        },
    });
});
