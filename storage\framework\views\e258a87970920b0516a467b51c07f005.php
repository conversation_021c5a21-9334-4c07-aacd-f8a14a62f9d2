<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="ahrefs-site-verification" content="d8bdbb7cd5a68d0e28a4f41fa5308f58b4818679ee034f9990c1ee99f95ea329">
    
    <meta name="google-site-verification" content="8JSGS50jGU_KsQERP_sFGkrnFqlR1J_au4CKrLmz_rw" />
    
    <title><?php echo $__env->yieldContent('meta_title'); ?></title>
    <meta name="title" content="<?php echo $__env->yieldContent('meta_title'); ?>">
    <meta name="keywords" content="<?php echo $__env->yieldContent('meta_keywords'); ?>">
    <meta name="description" content="<?php echo $__env->yieldContent('meta_description'); ?>">
    <link rel="canonical" href="<?php echo e(url()->current()); ?>">
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('website/images/favicon.png')); ?>">

  <!-- Open Graph tags for social media sharing -->
    <meta property="og:title" content="<?php echo $__env->yieldContent('meta_title'); ?>">
    <meta property="og:description" content="<?php echo $__env->yieldContent('meta_description'); ?>">
    <meta property="og:image" content="<?php echo e(asset('website/images/logo.webp')); ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Travel Africas">

    <!-- Preload Fonts -->
    <link  media="print" onload="this.media='all'" rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link  media="print" onload="this.media='all'" rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link  media="print" onload="this.media='all'" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&family=Yesteryear&display=swap" rel="stylesheet">
    <link  media="print" onload="this.media='all'" rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link  media="print" onload="this.media='all'" rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link  media="print" onload="this.media='all'" href="https://fonts.googleapis.com/css2?family=Ancizar+Serif:ital,wght@0,300..900;1,300..900&display=swap" rel="stylesheet">

    <!-- External CSS -->
     <link media="print" onload="this.media='all'" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha384-..." crossorigin="anonymous"> 
    <link href="<?php echo e(asset('library/bootstrap/bootstrap.min.css')); ?>" rel="stylesheet">
    <link media="print" onload="this.media='all'" href="<?php echo e(asset('library/slick/slick.css')); ?>" rel="stylesheet">
    <link  media="print" onload="this.media='all'" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper/swiper-bundle.min.css" />
    <link  media="print" onload="this.media='all'" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link  media="print" onload="this.media='all'" rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link  media="print" onload="this.media='all'" rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link  media="print" onload="this.media='all'" rel="stylesheet" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.dataTables.min.css">
    <link  media="print" onload="this.media='all'" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/css/intlTelInput.css">
    <link  media="print" onload="this.media='all'" href="https://cdn.jsdelivr.net/npm/dropify@0.2.2/dist/css/dropify.min.css" rel="stylesheet">
    <!-- jQuery ui -->
    <link media="print" onload="this.media='all'" href=" https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet" />

    <!-- Custom Styles -->
    <link  href="<?php echo e(asset('website/css/style.css')); ?>" rel="stylesheet">

    <!-- Render pushed css -->
    <?php echo $__env->yieldPushContent('css'); ?>



<style>
    /* theme color */
:root {
  --main-bg-color: #ffffff;
  --main-bg-shadow: rgba(100, 100, 111, 0.8);
  --main-text-color: #000;
  --main-border-color: rgba(0, 0, 0, 0.09);

  --header-bg-color: #ffffff;
  --header-bg-shadow: 0px 4px 16px 0px #1122110D;
  --header-text-color: #000;
  --header-border-color: #112211;
  --header-border-color-two: #e2e2e2;

  --footer-bg-color: #ffffff;
  --footer-bg-shadow: rgb(0 0 0 / 8%);
  --footer-text-color: #181433;
  --footer-border-color: #E5E5EA;


  --home-banner-text-color: #fff;

  --primary-box-shadow: rgba(100, 100, 111, 0.2);
  --custom-text-color-one: #8c8c8c;
  --active-color: #823602;
  --custom-color-one: #fff;

  --active-bg-color: #823602;
  --active-bg-text-color: #fff;

  --primary-bg-color: #823602;
  --primary-text-color: #ffffff;
  --primary-bg-shadow: #DF695126;

  --secondary-bg-color: #f7f7f7;
  --secondary-text-color: #000000;

  --ternary-bg-color: #000000;
  --ternary-text-color: #ffffff;

  --success-bg-color: green;
  --success-text-color: #fff;

  --failed-bg-color: red;
  --failed-text-color: #fff;
}

.loading-skeleton {
  color: transparent;
  appearance: none;
  -webkit-appearance: none;
  background-color: #eee !important;
  border-color: #eee !important;
  pointer-events: none;
  animation: loading-skeleton 1s infinite alternate;
  box-shadow: unset !important;
}

.loading-skeleton * {
  color: transparent !important;
  appearance: none !important;
  border-color: transparent !important;
}

.loading-skeleton {
  color: transparent !important;
  appearance: none !important;
  border-color: transparent !important;
}

.loading-skeleton::placeholder {
  color: transparent !important;
}

@keyframes loading-skeleton {
  from {
    opacity: .4;
  }

  to {
    opacity: 1;
  }
}

.loading-skeleton img {
  filter: grayscale(100) contrast(0%) brightness(1.8);
}

/* .loading-skeleton h1,
.loading-skeleton h2,
.loading-skeleton h3,
.loading-skeleton h4,
.loading-skeleton h5,
.loading-skeleton h6,
.loading-skeleton p,
.loading-skeleton li,
.loading-skeleton .btn,
.loading-skeleton label,
.loading-skeleton .form-control {
  color: transparent;
  appearance: none;
  -webkit-appearance: none;
  background-color: #eee;
  border-color: #eee;
} */


.skeleton-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: 0px -10px;
  justify-content: center;

}

.skeleton-box {
  height: 220px;
  border-radius: 10px;
  flex: 0 0 25%;
  padding: 10px 10px;
  max-width: 25%;
  display: flex;
}

.skeleton-item {
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, #eee 25%, #ddd 50%, #eee 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;

}
@media(max-width:1024px){
  .skeleton-box {
    flex: 0 0 33.333%;
    max-width: 33.333%;
  }
}
@media(max-width:768px){
  .skeleton-box {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@keyframes shimmer {
  0% {
      background-position: -200% 0;
  }

  100% {
      background-position: 200% 0;
  }
}


* {
  -web-kit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0px;
  margin: 0px;
  font-family: 'Montserrat', sans-serif;
  /* font-family: 'Yesteryear', cursive; */
  /* font-family: 'Poppins', sans-serif; */
}

html {
  scroll-behavior: smooth;
}

body {
  float: left;
  width: 100%;
  margin: 0px;
  font-family: 'Montserrat', sans-serif;
}

a {
  color: var(--main-text-color);
  text-decoration: none !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  /* font-family: 'Montserrat', sans-serif; */
  font-family: "Ancizar Serif", serif !important;
}

figure,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
label {
  margin: 0px;
  padding: 0px;
}
</style>
</head>

<body>
    <h1 class="d-none"><?php echo $__env->yieldContent('h1'); ?></h1>


    <div class="form_process_loader d-none" id="form_loader">
        <img src="<?php echo e(asset('files/loader.gif')); ?>">
    </div>

    <!-- Flash messages for email verification -->
    <?php if(session('status')): ?>
        <div id="flashMessage" class="alert alert-success alert-dismissible fade show" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
            <?php echo e(session('status')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <script>
            // Auto-hide flash message after 5 seconds
            setTimeout(function() {
                var flashMessage = document.getElementById('flashMessage');
                if (flashMessage) {
                    flashMessage.style.opacity = '0';
                    setTimeout(function() {
                        flashMessage.remove();
                    }, 300);
                }
            }, 5000);
        </script>
    <?php endif; ?>

    <!-- Generic alert message for all responce -->
    <div id="alertMessage" class="alert" role="alert">
        <div class="alertContentBox">
            <span id="alertContent"></span>
            <i class="fas fa-times"></i>
        </div>
    </div>


    <?php echo $__env->make('website/include/header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php $__env->startSection('content'); ?>
    <?php echo $__env->yieldSection(); ?>

    <?php echo $__env->make('website/include/footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


    <!-- JavaScript (defer as much as possible) -->

        <!-- Google tag (gtag.js) -->
    <script defer src="https://www.googletagmanager.com/gtag/js?id=G-8CC8G3FKEB"></script>
    <script defer>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-8CC8G3FKEB');
    </script>


    <!-- jQuery -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" defer></script>
    <!-- jQuery Validation Plugin -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js" defer></script>
    <!-- Bootstrap popper JS -->
    <script src="<?php echo e(asset('library/bootstrap/popper.js')); ?>" defer></script>
    <!-- Bootstrap 5 JS -->
    <script src="<?php echo e(asset('library/bootstrap/bootstrap.min.js')); ?>" defer></script>
    <!-- slick slider JS -->
    <script src="<?php echo e(asset('library/slick/slick.min.js')); ?>" defer></script>
    <!-- swiper Slider js -->
    <script src="https://cdn.jsdelivr.net/npm/swiper/swiper-bundle.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr" defer></script>


    <script src="https://cdn.jsdelivr.net/npm/dropify@0.2.2/dist/js/dropify.min.js" defer></script>
 
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.13/js/intlTelInput-jquery.min.js" defer></script>
  
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.0/jquery-ui.min.js" defer></script>


    <script src="<?php echo e(asset('website/js/select2/select2.js')); ?>" defer></script>
    <script src="<?php echo e(asset('website/js/custom_script.js')); ?>" defer></script>
    <script src="<?php echo e(asset('website/js/loginSignup.js')); ?>" defer></script>
    <script src="<?php echo e(asset('website/js/subscription.js')); ?>" defer></script>
    <script src="<?php echo e(asset('website/js/payment.js')); ?>" defer></script>



    <script src="https://js.stripe.com/v3/" async></script>
  <!-- recaptcha js for website -->
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>

        <!-- Render pushed JavaScript -->
    <?php echo $__env->yieldPushContent('js'); ?>
  
</body>

</html><?php /**PATH D:\clients project\yared\travelafrica\resources\views/website/include/layout.blade.php ENDPATH**/ ?>