/* header style start*/
.dashboard-header {
  padding: 15px 15px 0px 15px;
}


.dashboard_header_main {
  background-color: var(--main-bg-color);
  padding: 20px 15px;
  border-radius: 8px;
  box-shadow: 0 0 0.375rem 0.25rem var(--main-bg-shadow);
}

.header-hamburger-btn {

  cursor: pointer;
  display: flex;
}

.header-hamburger-btn i {
  font-size: 18px;
  color: var(--secondary-text-color);
  background: var(--secondary-bg-color);
  border-radius: 8px;
  padding: 12px 10px;
}

/* header style end*/

/* side bar style */
.dashboard-sidebar {
  left: 0px;
  padding-top: 40px;
  width: 280px;
  height: 100vh;
  background-color: var(--main-bg-color);
  display: flex;
  flex-direction: column;
  padding-bottom: 30px;
  position: fixed;
  transform: translateX(0%);
  transition: 0.5s ease-in-out;
  z-index: 99;
  box-shadow: 0 0.125rem 0.375rem 0 var(--main-bg-shadow);
}

.dashboard-sidebar-data {
  overflow-y: auto;
  scrollbar-width: thin;
}

.dashboard-sidebar.active {
  transform: translateX(-100%);
  transition: 0.5s ease-in-out;
}

.sidebar_cross_btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: var(--secondary-text-color);
  cursor: pointer;
  position: absolute;
  right: -20px;
  background-color: var(--body-color) !important;
}

.sidebar_cross_btn i {
  width: 38px;
  height: 38px;
  background: var(--main-bg-color);
  border-radius: 50%;
  color: var(--secondary-text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-logo {
  padding: 0px 15px 40px 15px;
}

.dashboard-logo img {
  width: 150px;
}

.menu-list-box {
  padding: 0px 15px 0px 10px;
}

.menu-list-header {
  background: var(--secondary-bg-color);
  border-radius: 8px;
  transition: 0.5s ease-in-out;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: 0.5s ease-in-out;

}

.menu-list-header span {
  color: var(--secondary-text-color);
  transition: 0.5s ease-in-out;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 0.3px;
}

.menu-list-header i {
  transition: all 0.5s ease;
  color: var(--secondary-text-color);
  font-weight: 500;
}

.menu-list-header.open i {
  transform: rotate(180deg);
  transition: 0.5s ease-in-out;

}

.menu-list {
  margin-bottom: 15px;
  margin-top: 20px;
  padding: 10px 0px;
  margin-left: 10px;
  display: grid;
  gap: 20px;
}

.menu-list li {
  display: flex;
  padding: 0px 10px 0px 30px;
}

.menu-list li a {
  color: var(--main-text-color);
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 0.3px;
  position: relative;
}

.menu-list li a::before {
  position: absolute;
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--main-text-color);
  top: 8px;
  left: -20px;
}

.menu-list li a::after {
  position: absolute;
  content: "";
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid var(--main-text-color);
  top: 5px;
  left: -23px;
}

.menu-list li.active a {
  color: var(--active-text-color);
  font-weight: 600;
}

.menu-list li.active a::before {
  background: var(--primary-bg-color);
}

.menu-list li.active a::after {
  border-color: var(--primary-bg-color);
}

/* body style */
.content-section {
  padding-left: 280px;
  transition: 0.5s ease-in-out;
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content-section-main {
  padding: 15px 15px 30px 15px;

}


.content-section.active {
  padding-left: 0px;
  transition: 0.5s ease-in-out;
}


.pagination-list {
  padding-bottom: 30px;
  padding-top: 20px;
}

.pagination-list a {
  color: var(--text-color-one) !important;
  font-size: 18px;
  font-weight: 500;
}


@media (max-width: 1200px) {
  .dashboard-sidebar {
    transform: translateX(-100%);
    transition: 0.5s ease-in-out;
  }

  .dashboard-sidebar.active {
    transform: translateX(0%);
    transition: 0.5s ease-in-out;
  }

  .content-section {
    padding-left: 0px;
  }
}

/* profile css */

.user-profile-header-banner img {
  height: 250px;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%
}

.user-profile-header .user-profile-img {
  width: 120px
}

.user-profile-header h4 {
  font-style: normal;
  font-weight: 600;
  font-size: 22px;
  line-height: 21px;
  color: var(--main-text-color);
  text-transform: capitalize;
}

.user-profile-info ul li {
  font-style: normal;
  font-weight: 400 !important;
  font-size: 15px;
  line-height: 21px;
  color: var(--text-color-two);
  text-transform: capitalize;
}

.profile_subscription_details small {
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 21px;
  color: var(--main-text-color) !important;
  text-transform: capitalize !important;
}


@media(max-width: 768px) {
  .user-profile-header-banner img {
    height: 150px
  }

  .user-profile-header .user-profile-img {
    width: 100px
  }
}

/*  */
.add_destination_main {
  display: flex;
  flex-wrap: wrap;
  margin: 0px -10px;
}

.add_destination_box_one {
  flex: 0 0 60%;
  max-width: 100%;
  padding: 0px 10px;
  margin: 10px 0px;
}

.add_destination_box_two {
  flex: 0 0 40%;
  max-width: 100%;
  padding: 0px 10px;
  margin: 10px 0px;
}

.destination_single_box {
  display: grid;
  gap: 20px 20px;
  width: 100%;
}

.card_heading h5 {
  color: var(--main-text-color) !important;
  font-size: 18px;
  font-weight: 600;
}

@media(max-width: 1024px) {
  .add_destination_box_one {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0px 10px;
    margin: 10px 0px;
  }

  .add_destination_box_two {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0px 10px;
    margin: 10px 0px;
  }
}

/* form action toggle css */
.form_action_box {
  position: relative;
  text-align: right;
}

button.toggle-button {
  border: unset;
  background: unset;
}

button.toggle-button i {
  font-size: 20px;
}

.action_btn_group {
  display: grid;
  position: absolute;
  top: 0px;
  right: 20px;
  content: "";
  width: 245px;
  background-color: var(--ternary-bg-color);
  padding: 15px 13px;
  border-radius: 4px;
}

.action_btn_group>button {
  border: unset;
  text-align: left;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  color: #000;
  font-weight: 600;
  padding: 8px 10px;
  border-radius: 4px;
}

.action_btn_group>a {
  border: unset;
  text-align: left;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  color: #000;
  font-weight: 600;
  padding: 8px 10px;
  border-radius: 4px;
}

.detail_btn {
  color: #fff !important;
  background-color: #17a2b8;
}

.delete_btn {
  color: #fff !important;
  background-color: #dc3545;

}

.cancle_btn {
  color: #fff !important;
  background-color: #dc3545;
}

.payment_btn {
  color: #fff !important;
  background-color: #28a745;
}

.update_btn {
  color: #fff !important;
  background-color: #28a745;
}

.action_btn_group>button i {
  font-size: 14px;
  margin-right: 10px;
  padding-top: 4px;
  font-weight: normal;
}

.action_btn_group>a i {
  font-size: 14px;
  margin-right: 10px;
  padding-top: 4px;
  font-weight: normal;
}

/* payment status  */
.payment_status_parent p {
  font-size: 14px;
  font-weight: 500;
  border-radius: 4px;
  color: #fff;
}

.payment_status_parent button {
  font-size: 14px;
  font-weight: 500;
  border-radius: 4px;
  color: #fff;
  border: unset;
}