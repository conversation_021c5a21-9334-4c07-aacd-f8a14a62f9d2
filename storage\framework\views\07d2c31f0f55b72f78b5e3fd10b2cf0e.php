
<?php $__env->startSection('title', 'change password'); ?>
<?php $__env->startPush('css'); ?>
<link rel="stylesheet" href="<?php echo e(asset('profile/css/profile_page.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

<section class="page_title_section d-block w-100 text-center float-left position-relative">
    <img src="<?php echo e(asset('website/images/login_banner.png')); ?>" class="w-100 float-left h-100 object-fit-cover"
        alt="homebanner1" loading="lazy" />
    <div class="d-flex w-100 h-100 position-absolute top-0 left-0">
        <div class="page_title d-flex align-items-center justify-content-center text-center flex-column">
            <small>Read</small>
            <h2>Update Password</h2>
        </div>
    </div>
</section>

<section class="profile_page_section">
    <div class="custom_container">
        <?php if(session()->has('success_message')): ?>
        <div class="alert alert-success">
            <?php echo e(session()->get('success_message')); ?>

        </div>
        <?php endif; ?>
        <?php if(session()->has('error_message')): ?>
        <div class="alert alert-danger">
            <?php echo e(session()->get('error_message')); ?>

        </div>
        <?php endif; ?>
        <div class="pofile_page_main_box">
            <?php echo $__env->make('website.profile.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- personel info asset start -->

            <div class="profile_tabs_data_section">
                <div class="profile_tabs_data_header">
                    <h2>User Password</h2>
                </div>
                <div class="profile_tabs_content_box">
                    <?php ($user = auth()->user()); ?>
                    <form id="update_user_password">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="id" value="<?php echo e($user->id); ?>">
                        <div class="edit_personal_info_body">
                            <div class="profile_form_content_box">
                                <div class="profile_form_field_box">
                                    <div class="profile_single_field_box position-relative">
                                        <span>Current Password</span>
                                        <input type="password" id="current_password" name="current_password">
                                    </div>
                                </div>
                                <div class="profile_form_field_box">
                                    <div class="profile_single_field_box position-relative">
                                        <span>New Password</span>
                                        <input type="password" id="new_password" name="new_password">
                                    </div>
                                </div>
                                <div class="profile_form_field_box">
                                    <div class="profile_single_field_box position-relative">
                                        <span>Confirm Password</span>
                                        <input type="password" id="new_password_confirmation"
                                            name="new_password_confirmation">
                                    </div>
                                </div>
                                <div class="profile_form_submit_btn">
                                    <input type="submit" value="Update">
                                </div>
                            </div>
                        </div>
                        <!-- Loading indicator -->
                        <div class="form_process_loader d-none">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>



<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script src="<?php echo e(asset('profile/js/custom_script.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.include.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\clients project\yared\travelafrica\resources\views/website/profile/changePassword.blade.php ENDPATH**/ ?>