@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / commission / List</a>
</div>
<div class="content-section-box">


    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddCommission"><span>Add
                    Commission</span></button>
        </div>
        <table id="blog_category_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">country</th>
                    <th style="min-width: 100px;">Commission in percentage</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($commissionData as $commissionData)
                <tr class="commission_row">
                    <td></td>

                 
                    <td>{{ $commissionData->country }}</td>
                    <td>{{ $commissionData->commission }}</td>

                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $commissionData->id }}" type="button"
                                    class="delete_btn delete_commission"><i
                                        class="fas fa-trash-alt"></i>Delete</button>

                                <button class="update_btn edit_commission_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasUpdateCommision{{$commissionData->id}}"
                                    data-form-id="update_commission_{{$commissionData->id}}"
                                    data-id="{{$commissionData->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                            </div>
                        </div>
                        @include("dashboard.commission.update")

                    </td>
                </tr>

                @endforeach
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.commission.add")
@include("dashboard.commission.delete")

@endsection
