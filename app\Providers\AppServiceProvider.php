<?php

namespace App\Providers;

use App\Models\adminData;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Validator;
class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //

        $adminData = adminData::first();
        $globalData = [
            'facebook' => $adminData->facebook,
            'instagram' => $adminData->instagram,
            'twitter' => $adminData->twitter,
            'tiktok' => $adminData->tiktok,
            'youtube' => $adminData->youtube,

            'primary_no' => $adminData->primary_no,
            'secondary_no' => $adminData->secondary_no,
            'address' => $adminData->address,

            'newsletter_email' => $adminData->newsletter_email,
            'contact_email' => $adminData->contact_email,
            'primary_email' => $adminData->primary_email,

            'commission_percentage' => $adminData->commission_percentage,
            'featured_fee' => $adminData->featured_fee,
            'featured_duration' => $adminData->featured_duration,

            'visible_recaptcha_sitekey' => $adminData->visible_recaptcha_sitekey,
            'invisible_recaptcha_sitekey' => $adminData->invisible_recaptcha_sitekey,

        ];

        View::share('globalData', $globalData);
        Paginator::useBootstrap();
        
      Validator::extend('recaptcha', 'App\\Validators\\ReCaptcha@validate');
    }
}