@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / banners / banners list</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddWebBanner"><span>Add
                    Banner</span></button>
        </div>
        <table id="newsletter_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 120px;">Desktop Banner</th>
                    <th style="min-width: 200px;">Mobile Banner</th>
                   <th style="min-width: 200px;">Link</th>
                   <th style="min-width: 200px;">Alt Text</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($webBanner as $bannerList)
                <tr class="banner_row">
                    <td></td>
                    <td><img src="{{ $bannerList->desktop_image ? asset('storage/banner/'.$bannerList->desktop_image) : '' }}"
                            class=" object-fit-cover" alt="{{$bannerList->alt_text}}" loading="lazy" decoding="async" width="270" height="300" />
                    </td>
                    <td><img src="{{ $bannerList->mobile_image ? asset('storage/banner/'.$bannerList->mobile_image) : '' }}"
                            class=" object-fit-cover" alt="{{$bannerList->alt_text}}" loading="lazy" decoding="async" width="270" height="300" />
                    </td>
                    <td>{{ $bannerList->link }}</td>
                    <td>{{ $bannerList->alt_text }}</td>
                   
                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                            

                                <button class="update_btn edit_banner" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasUpdateBanner{{$bannerList->id}}"
                                    data-form-id="update_banner_{{$bannerList->id}}" data-id="{{$bannerList->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                                    <button data-id="{{ $bannerList->id }}" type="button"
                                    class="delete_btn delete_banner">Delete</button>
                            </div>
                        </div>
                        @include("dashboard.banner.updateBanner")

                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.banner.delete")
@include("dashboard.banner.addWebBanner")


@endsection