$(document).ready(function () {
    $("#update_admin_profile").validate({
        rules: {
            name: {
                required: true,
            },
            email: {
                required: true,
            },
           
        },

        submitHandler: function (form, event) {
            console.log('submit');
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);

            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let id = $(form).find('[name="id"]').val();
            let apiEndpoint = "/admin/profile/update/" + id;
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("updated successfully!", "success");
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_process_loader").addClass("d-none");

                    showAlert("An error occurred while updating.", "error");
                },
            });
        },
    });
});

$(document).ready(function () {
    $("#update_admin_password").validate({
        rules: {
            new_password: {
                required: true,
            },
            new_password_confirmation: {
                required: true,
                equalTo: "#new_password",
            },
        },
        messages: {
            new_password_confirmation: {
                equalTo: "Please enter the same password as above",
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_process_loader").removeClass("d-none");
            //  Serialize form data
            var formData = new FormData(form);

            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let id = $(form).find('[name="id"]').val();
            let apiEndpoint = "/admin/profile/password/update/" + id;
            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                processData: false, // Important! Don't process the data
                contentType: false, // Important! Set content type to false
                dataType: "json",
                success: function (response) {
                    $(".form_process_loader").addClass("d-none");
                    showAlert("updated successfully!", "success");
                    $(".invalid-feedback").remove();
                    setTimeout(function () {
                        window.location.href = response.route;
                    }, 1000);
                },
                error: function (xhr, status, error) {
                    $(".form_process_loader").addClass("d-none");
                    console.error(xhr);
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    }
                    showAlert(
                        "An error occurred contact your supprt team.",
                        "error"
                    );
                },
            });
        },
    });
});

$("#logout_btn").click(function (e) {
    e.preventDefault(); // Prevent the default anchor tag behavior
    let baseURL = window.location.origin;
    let apiEndpoint = "/api/logout";
    let apiUrl = baseURL + apiEndpoint;
    let csrfToken = $('meta[name="csrf-token"]').attr("content"); // Get the CSRF token value

    $.ajax({
        url: apiUrl,
        type: "POST",
        headers: {
            "X-CSRF-TOKEN": csrfToken, // Pass the CSRF token in the headers
        },
        success: function (response) {
            // Handle success, such as redirecting to the login page
            window.location.href = "/home";
        },
        error: function (xhr, status, error) {
            // Handle error
            console.error(xhr);
        },
    });
});
