<?php

namespace App\Http\Controllers\profile;

use App\Http\Controllers\Controller;
use App\Models\bookingPayments;
use App\Models\DestinationBooking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class profileController extends Controller
{

    public function profile()
    {
        return view('website.profile.profile');
    }
    public function changePassword()
    {
        return view('website.profile.changePassword');
    }
    public function updateProfile(Request $request, $id)
    {
        $user = User::findOrFail($id);
        $user->name = $request->name;

        if ($request->has('email')) {
            $user->email = $request->email;
        }



        // Handle image upload
        if ($request->hasFile('profile_img')) {
            $file = $request->file('profile_img');
            $userPath = media($file, 'storage/userImages');
            $userPath = basename($userPath);
            $user->profile_image = $userPath;
        }
        $user->save();

        // Update related subscriber info
        if ($user->subscriber) {
            $subscriber = $user->subscriber;
            if ($request->has('lang')) {
                $subscriber->languages_spoken = $request->lang; // if it's an array
            }
            if ($request->has('certificate')) {
                $subscriber->certificates = $request->certificate; // if it's an array
            }
            if ($request->has('agent_points')) {
                $subscriber->agent_points = $request->agent_points; // if it's an array
            }



            if ($request->has('bio')) {
                $subscriber->company_description = $request->bio;
            }

            if ($request->has('c_name')) {
                $subscriber->company_name = $request->c_name;
            }
            if ($request->has('about')) {
                $subscriber->guide_detail = $request->about;
            }

            $subscriber->save();
        }


        return response()->json(['message' => 'Profile updated successfully'], 200);
    }

    public function updatePassword(Request $request, $id)
    {

        $request->validate([
            'current_password' => 'required|string',
            'new_password' => ['required', 'string', 'confirmed', Password::defaults()],
        ]);
        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json(['message' => 'Current password is not correct'], 403);
        }
        User::where('id', $user->id)->update(['password' => Hash::make($request->new_password)]);

        Auth::logout();
        return response()->json(['message' => 'Password updated', 'route' => '/login'], 200);
    }
    public function updateAdminPassword(Request $request)
    {
        $request->validate([
            'new_password' => ['required', 'string', 'confirmed', Password::defaults()],
        ]);
        $user = Auth::user();
        User::where('id', $user->id)->update(['password' => Hash::make($request->new_password)]);

        Auth::logout();
        return response()->json(['message' => 'Password updated', 'route' => '/login'], 200);
    }
    public function bookingDetail()
    {
        $user = Auth::user();
        $bookingRecords = DestinationBooking::where('user_id', $user->id)->with('destination')->get();
        return view('website.profile.bookingDetail', ['bookingRecords' => $bookingRecords]);
    }
    public function paymentHistory()
    {
        $user = Auth::user();
        $bookings = DestinationBooking::with('destination')->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();
        $bookingPaymentRecords = collect(); // Initialize an empty collection for payments
        foreach ($bookings as $booking) {
            $bookingPayments = bookingPayments::where('booking_id', $booking->id)->with('bookedDestination')
                ->orderBy('created_at', 'desc')
                ->get();

            $bookingPaymentRecords = $bookingPaymentRecords->merge($bookingPayments); // Merge the payments into the collection
        }

        return view('website.profile.paymentHistory', ['bookingPaymentRecords' => $bookingPaymentRecords]);
    }
}
