@extends('website.include.layout')
@section('title', 'Destinations')

@section('meta_title', $seoData->meta_title ?? 'Default Meta Title')
@section('meta_keywords', $seoData->meta_keywords ?? 'Default Meta Keywords')
@section('meta_description', $seoData->meta_description ?? 'Default Meta Description')
@section('h1', $seoData->page_h1_heading ?? 'Default H1 Heading')


@section('content')



<section class="inner_banner_section d-block w-100">
    <div class="inner_banner_text_section w-100 h-100 position-relative"
        style="background-image: url('/website/images/destinationBanner.webp');">
        <div class="inner_banner_container">
            <div class="inner_banner_slider_text d-flex flex-column w-100">

                <h2>Discover Your Dream Destination with Travel Africa's Online Booking</h2>
                <p>Explore our extensive collection of destination packages in Africa, featuring breathtaking
                    landscapes, vibrant cities, and rich cultural experiences. Book your next international adventure
                    with ease through Travel Africa's online booking platform.</p>
            </div>
        </div>
    </div>
</section>

<div class="sop_page_parent w-100 d-block">
    <div class="custom_container">
        <div class="shop_content_body d-flex">
            <div class="filter_parent d-flex flex-column">
                <div class="filters_heading d-lg-flex d-none align-items-center w-100">
                    <span class="d-flex align-items-center w-100 justify-content-center">Filter <i class="fa fa-filter"></i></span>
                </div>

                <div class="filters_heading filter_toggle_btn d-lg-none d-flex align-items-center w-100">
                    <span class="d-flex align-items-center w-100 justify-content-center">Filter <i class="fa fa-filter"></i></span>
                </div>

                <div class="filters_list">
                    <div class="filter_responsive_btn d-lg-none d-flex justify-content-between align-items-center">
                        <span>Filter</span>
                        <a class="filter_close_btn"><i class="fas fa-times"></i></a>
                    </div>
                    <div class="accordion filters_accordion_parent d-grid w-100" id="filtersAccordion">
                        <div class="single_filter_section d-grid w-100">
                            <h2 class="filter_header" id="filterheadingOne">
                                <button
                                    class="filter_header_btn accordion-button d-flex align-items-center justify-content-between"
                                    type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapseOne"
                                    aria-expanded="false" aria-controls="filterCollapseOne">
                                    <h5>Categories</h5>
                                    <i class="fas fa-chevron-down ms-auto"></i>
                                </button>
                            </h2>
                            <div id="filterCollapseOne" class="accordion-collapse collapse show"
                                aria-labelledby="filterheadingOne" data-bs-parent="#filtersAccordion">

                                <ul class="filters_listing d-grid w-100">

                                    @foreach($destinationCategory as $destinationCat)
                                    @php
                                    $count = $destinationCat->destinations()->where('status', 1)->count();
                                    @endphp
                                    <li class="d-flex align-items-center">
                                        <div class="filter_checkbox d-flex">
                                            <input type="checkbox" name="category[]" id="cat{{$destinationCat->id}}"
                                                value="{{$destinationCat->id}}" />
                                            <label for="cat{{$destinationCat->id}}"> </label>
                                        </div>
                                        <div class="filter_list_detail d-flex align-items-center">
                                            <span>{{$destinationCat->title}}</span>
                                            <small>({{$count}})</small>
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>

                            </div>
                        </div>

                        <div class="single_filter_section d-grid w-100">
                            <h2 class="filter_header" id="filterheadingTwo">
                                <button
                                    class="filter_header_btn accordion-button d-flex align-items-center justify-content-between"
                                    type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapseTwo"
                                    aria-expanded="false" aria-controls="filterCollapseTwo">
                                    <h5>Types</h5>
                                    <i class="fas fa-chevron-down ms-auto"></i>
                                </button>
                            </h2>
                            <div id="filterCollapseTwo" class="accordion-collapse collapse show"
                                aria-labelledby="filterheadingTwo" data-bs-parent="#filtersAccordion">
                                <ul class="filters_listing d-grid w-100">
                                    @foreach($destinationType as $destinationType)
                                    @php
                                    $count = $destinationType->destinations()->where('status', 1)->count();

                                    @endphp
                                    <li class="d-flex align-items-center">
                                        <div class="filter_checkbox d-flex">
                                            <input type="checkbox" name="type[]" value="{{$destinationType->id}}"
                                                id="type{{$destinationType->id}}" />
                                            <label for="type{{$destinationType->id}}"> </label>
                                        </div>
                                        <div class="filter_list_detail d-flex align-items-center">
                                            <span>{{$destinationType->title}}</span>
                                            <small>({{$count}})</small>
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>



                        <div class="single_filter_section d-grid w-100">
                            <h2 class="filter_header" id="filterheadingThree">
                                <button
                                    class="filter_header_btn accordion-button d-flex align-items-center justify-content-between"
                                    type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapseThree"
                                    aria-expanded="false" aria-controls="filterCollapseThree">
                                    <h5>Price</h5>
                                    <i class="fas fa-chevron-down ms-auto"></i>
                                </button>
                            </h2>
                            <div id="filterCollapseThree" class="accordion-collapse collapse show"
                                aria-labelledby="filterheadingThree" data-bs-parent="#filtersAccordion">

                                <form id="price_range_form" method="get" action="{{ route('destinations') }}">
                                    <div class="price_range_slider w-100 d-flex flex-column">
                                        <div id="filter_slider_range" class="price_filter_range_bar"></div>
                                        <p class="filter_range_value d-flex align-items-center">
                                            <span class="price_curreny">USD</span>
                                            <input type="text" id="amount" name="price_range" readonly>
                                        </p>
                                    </div>
                                    <button class="price_range_filter" type="button" id="apply_price_filter_btn">Apply
                                        Filter</button>
                                </form>

                            </div>
                        </div>





                    </div>
                </div>
            </div>
            <div class="product_list_parent d-flex flex-column">
                <div class="shop_sort_filter_parent d-flex align-items-center justify-content-between w-100 flex-wrap">

                    <h5>Search Results ({{ $destinations->count() }})</h5>
                    <div class="shop_sort_filter d-flex align-items-center">
                      
                        <form method="get" action="{{ route('destinations') }}" class="w-100">
                            <label for="sort_by">Sort by:</label>
                            <select name="sort_by" id="sort_by" onchange="this.form.submit()">
                                <option value="latest" {{ Request::get('sort_by') == 'latest' ? 'selected' : '' }}>
                                    Latest</option>
                                <option value="price_high_to_low"
                                    {{ Request::get('sort_by') == 'price_high_to_low' ? 'selected' : '' }}>Price High to
                                    Low</option>
                                <option value="price_low_to_high"
                                    {{ Request::get('sort_by') == 'price_low_to_high' ? 'selected' : '' }}>Price Low to
                                    High</option>
                            </select>
                            <noscript><input type="submit" value="Submit"></noscript>
                        </form>
                    </div>
                </div>
                <div class="product_listing_box d-flex flex-column w-100">
                    <div class="d-flex flex-column">

                        <div class="product_listing_section row">

                            @foreach($destinations as $destination)

                            @php
                            $departureDatesTimesArray = explode(", ", $destination->departure_date_time);
                            $departureDateTimeObjects = [];
                            foreach ($departureDatesTimesArray as $dateTimeString) {
                            $dateTimeObject = \DateTime::createFromFormat('Y-m-d h:i A', $dateTimeString);
                            if ($dateTimeObject !== false) {
                            $departureDateTimeObjects[] = $dateTimeObject;
                            }
                            }

                            $currentDateTime = new \DateTime();
                            $nearestDateTime = null;
                            $nearestDiff = PHP_INT_MAX;
                            foreach ($departureDateTimeObjects as $dateTime) {
                            $diff = $dateTime->getTimestamp() - $currentDateTime->getTimestamp();
                            if ($diff > 0 && $diff < $nearestDiff) { $nearestDiff=$diff; $nearestDateTime=$dateTime; } }
                                $nearestDateString=$nearestDateTime ? $nearestDateTime->format('Y-m-d h:i A') :
                                '';
                                @endphp

                                <div class="single_product col-12 col-md-4 col-lg-4"
                                    data-category-id="{{ $destination->category_id }}"
                                    data-type-id="{{ $destination->type_id }}">
                                    <div class="product_card w-100">
                                        <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                            class="destination_img w-100 position-relative">
                                            <img src="{{$destination ? asset('storage/destinations/'.$destination->cover_img) : asset('website/images/logo.png') }}"
                                                class="w-100 h-100 object-fit-cover"
                                                alt="{{$destination->alt_text_cover_image}}" loading="lazy" />
                                            <div class="nearest_departure_date">
                                                {{customDate($nearestDateString,'F d, Y')}}
                                            </div>
                                        </a>
                                        <div class="product_content d-flex flex-column w-100">

                                            <div class="shop_single_package_rating d-flex">
                                                <i class="far fa-star"></i>
                                                <i class="far fa-star"></i>
                                                <i class="far fa-star"></i>
                                                <i class="far fa-star"></i>
                                                <i class="far fa-star"></i>
                                            </div>

                                            <div class="shop_package_location d-flex align-items-start flex-column">
                                                <span class="d-flex align-items-center"><i
                                                        class="far fa-calendar-check"></i>{{$destination->days}}
                                                    Days - {{$destination->nights}} Nights</span>
                                                <span class="d-flex align-items-center"><i
                                                        class="fas fa-map-marker-alt"></i>
                                                    {{$destination->destination_country}}</span>
                                            </div>
                                            <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                                class="product_name">{{$destination->title}}</a>
                                            <div class="shop_short_des">{!! $destination->short_description !!}
                                            </div>
                                            <div class="price_tags d-flex flex-column w-100">
                                                <div class="total_and_discount_price d-flex flex-column">
                                                    <span class="price_label">Per person</span>
                                                    <small class="orignal_price">From <span
                                                            class="price_currency text-uppercase">{{$destination->currency}}</span>
                                                        {{$destination->final_price}}</small>

                                                </div>
                                                <div class="product_cart_btn">
                                                    <a href="{{ route('website.destination.detail', $destination->slug) }}"
                                                        class="d-flex align-items-center justify-content-center">View
                                                        Detail</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                @endforeach

                                <div class="pagination">
                                    {{ $destinations->links() }}
                                </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>




@endsection
