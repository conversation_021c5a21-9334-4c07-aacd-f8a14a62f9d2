:root {

  --profile_page_bg_color: #ffffff;
  --primary-bg-color: #823602;
  --primary-text-color: #ffffff;
  /* profile box bg heading and text */
  --profile_process_border_color: rgba(0, 0, 0, 0.08);
  --profile_process_bg_color: #ffffff;
  --profile_process_shadow_color: 0px 4px 14px rgba(0, 0, 0, 0.08);
  --profile_process_heading_color: #000000;

  /* profile page table color */

  --profile_table_thead_bg_color: #F6F6F6;
  --profile_table_thead_border_color: rgba(15, 153, 75, 0.16);
  --profile_table_tbody_bg_color: #fff;
  --profile_table_text_color: #000000;
  --profile_table_order_detail_text_one: #e32528;

  /*profile page input colors  */
  --profile_input_border_color: rgba(0, 0, 0, 0.16);
  --profile_input_text_color: #212121;
  --profile_input_bg_color: #f5f5f5;
  --profile_input_lable_color: rgba(0, 0, 0, 0.5);
  --profile_input_placeholder_color: #bdbdbd;

  --profile_btn_bg_color: #C93927;
  --profile_btn_text_color: #fff;
}

.invalid-feedback {
  color: red;
  font-size: 12px;
  margin: 0px;
  position: absolute;
  bottom: -17px;
  line-height: 14px;
  left: 3px;
}

.profile_action_btn {
  cursor: pointer;
}

.delete_job_application_btn {
  border: unset;
  background: var(--profile_btn_bg_color);
  color: #fff !important;
  padding: 0px 4px;
  border-radius: 4px;
}

.delete_job_application_btn i {
  color: #fff !important;
}

.profile_page_section {
  display: inline-block;
  width: 100%;
  padding: 0px 10px 0px 10px;
  background-color: var(--profile_page_bg_color);
}

.custom_container {
  max-width: 1140px;
  margin: 0 auto;
}

.pofile_page_main_box {
  display: grid;
  grid-template-columns: 212px 1fr;
  gap: 30px 35px;
  width: 100%;
  grid-template-rows: auto;
  grid-template-areas: "..";
}

.pofile_page_sidebar {
  display: flex;
  width: 100%;
  float: left;
  flex-direction: column;
}

.pofile_sidebar_header {
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
}

.pofile_sidebar_header span {
  font-style: normal;
  font-weight: 400;
  font-size: 28px;
  line-height: 34px;
  text-transform: capitalize;
  color: var(--profile_process_heading_color);
}

.pofile_sidebar_header h5 {
  font-style: normal;
  font-weight: 700;
  font-size: 28px;
  line-height: 36px;
  text-transform: capitalize;
  color: var(--profile_process_heading_color);
  margin: 0px;
}

.pofile_sidebar_tab_section {
  mix-blend-mode: normal;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: block;
}

.pofile_sidebar_tab_list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.single_pofile_tab {
  background: var(--profile_process_bg_color);
  border-bottom: 1px solid var(--profile_process_border_color);
  border-radius: 4px 4px 0px 0px;
  padding: 21px 0px 21px 11px;
  display: flex;
  align-items: center;
  position: relative;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  text-transform: capitalize;
  color: var(--profile_process_heading_color);
  text-decoration: none;
}

.single_pofile_tab:hover {
  color: var(--profile_process_heading_color);
  text-decoration: none;
}

.single_pofile_tab:last-child {
  border-bottom: 0px;
}

.single_pofile_tab.active {
  background: var(--profile_process_bg_color);
  border: 1px solid var(--profile_process_border_color);
  box-shadow: var(--profile_process_shadow_color);
  border-radius: 4px;
  position: relative;
  z-index: 99;
}

.res_sidebar_toggle_btn {
  display: none;
  align-items: center;
}

.res_sidebar_toggle_btn.res_cross_btn {
  padding: 0px 15px 10px 0px;
  justify-content: flex-end;
}


.res_sidebar_toggle_btn span {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 26px;
  text-transform: capitalize;
  color: var(--profile_process_heading_color);
}

.res_sidebar_toggle_btn i {
  font-size: 20px;
  line-height: 26px;
  color: var(--profile_process_heading_color);
  margin-left: 10px;
}

.profile_tabs_data_section {
  display: flex;
  width: 100%;
  flex-direction: column;
  overflow: hidden;
}

.profile_tabs_data_header {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
  padding-bottom: 29px;
  border-bottom: 1px solid var(--profile_process_border_color);
  margin-bottom: 30px;
}

.profile_tabs_data_header h2 {
  font-style: normal;
  font-weight: 500;
  font-size: 32px;
  line-height: 36px;
  text-transform: capitalize;
  color: var(--profile_process_heading_color);
  margin: 0px;
}

.profile_tabs_content_box .table thead {
  background: var(--profile_table_thead_bg_color);
  border-radius: 4px;
  border: 1px solid var(--profile_table_thead_border_color);
  box-sizing: border-box;
}

.profile_tabs_content_box .table thead tr th {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  text-transform: capitalize;
  color: #000000;
  border: unset !important;
  padding: 15px 10px !important;
  vertical-align: middle;
  text-align: center;
}

.profile_tabs_content_box .table tbody tr {
  background-color: var(--profile_table_tbody_bg_color) !important;
}

.profile_tabs_content_box .table tbody tr td {
  font-style: normal;
  font-weight: 500;
  font-size: 13px;
  line-height: 18px;
  text-transform: capitalize;
  color: var(--profile_table_text_color);
  border: unset !important;
  padding: 15px 10px !important;
  vertical-align: middle;
  text-align: center;
}

.profile_tabs_content_box .table tbody tr td i {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 18px;
  text-transform: capitalize;
  color: var(--profile_table_text_color);
  border: unset !important;
  padding: 15px 10px !important;
  vertical-align: middle;
  text-align: center;
}

.profile_order_detail_btn {
  margin: 0px;
  display: grid;
  gap: 4px;
}

.profile_order_detail_btn a {
  color: var(--profile_table_order_detail_text_one) !important;
}

/* profile page input css start */
.profile_form_content_box {
  width: 100%;
  display: grid;
  gap: 22px 0px;
  max-width: 540px;
}

.profile_form_field_box {
  display: grid;
  position: relative;
}

.profile_form_field_box span {
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 16px;
  letter-spacing: 0.03em;
  color: var(--profile_input_lable_color);
  padding-bottom: 15px;
}

.profile_single_field_box {
  display: grid;
  width: 100%;
  position: relative;
  height: 60px;
  background-color: var(--profile_input_bg_color);
  border: none;
  border-radius: 6px;
}

.profile_single_field_box.textarea_box {
  height: unset;
}

.profile_single_field_box span {
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.03em;
  color: var(--profile_input_lable_color);
  position: absolute;
  top: 10px;
  left: 16px;
}

.profile_single_field_box input {

  height: 100%;
  padding: 32px 38px 14px 16px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: var(--profile_input_text_color);
  width: 100%;
  background-color: unset;
  border: unset;
}

.profile_single_field_box textarea {

  height: 100%;
  padding: 32px 38px 14px 16px;
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: var(--profile_input_text_color);
  width: 100%;
  background-color: unset;
  border: unset;
}

.profile_single_field_box input::placeholder {
  color: var(--profile_input_placeholder_color);
}

.profile_form_field_box label.error {
  color: red;
  font-size: 12px;
  margin: 0px;
  position: absolute;
  bottom: -17px;
  line-height: 14px;
  left: 3px;
}

.profile_single_field_box i {
  position: absolute;
  bottom: 19px;
  align-items: center;
  right: 16px;
  color: var(--profile_input_text_color);
  cursor: pointer;
}

.profile_single_field_box input:focus-visible {
  border: unset !important;
  outline: unset;
}

.profile_single_field_box input::placeholder {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--profile_input_placeholder_color);
}

.profile_form_submit_btn {
  max-width: 300px;
  display: flex;
}

.profile_form_submit_btn input {
  height: 48px;
  background: var(--profile_btn_bg_color);
  border-radius: 4px;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--profile_btn_text_color) !important;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  border: unset;
}

.profile_form_submit_btn button {
  height: 48px;
  background: var(--profile_btn_bg_color);
  border-radius: 4px;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--profile_btn_text_color) !important;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  border: unset;
}

.profile_form_submit_btn a {
  height: 48px;
  background: var(--profile_btn_bg_color);
  border-radius: 4px;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  text-transform: capitalize;
  color: var(--profile_btn_text_color) !important;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
  border: unset;
  padding: 0px 10px;
}


.edit_personal_info_header {
  padding: 10px 10px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.edit_personal_info_header h3 {
  color: rgb(36, 40, 43);
  font-size: 18px;
  font-weight: bold;
  line-height: 20px;
}

.edit_personal_info_cross_btn i {
  background-color: rgba(0, 0, 0, 0.5);
  color: rgb(255, 255, 255);
  font-size: 16px;
  border-radius: 4px;
  cursor: pointer;
  padding: 7px 7px;
}

.edit_personal_info_container {
  max-width: 540px;
}

.edit_personal_info_content {
  padding: 15px;
  border: unset !important;
  border-radius: 15px;
}

.modal.fade .modal-dialog.edit_personal_info_zoom {
  -webkit-transform: translate(0, 0) scale(0.5);
  transform: translate(0, 0) scale(0.5);
}

.modal.show .modal-dialog.edit_personal_info_zoom {
  -webkit-transform: translate(0, 0) scale(1);
  transform: translate(0, 0) scale(1);
}

.edit_personal_info_body {
  padding: 10px 10px;
  display: flex;
  width: 100%;
}

.edit_personal_info_body form {
  width: 100%;
}

/*  profile page input css end */

@media(max-width:768px) {
  .profile_page_section {
    padding: 0px 10px 0px 10px;
  }

  .pofile_page_main_box {
    grid-template-columns: 1fr;
    grid-template-areas: ".";
  }

  .pofile_sidebar_header {
    padding-bottom: 0px;
  }

  .pofile_page_sidebar {
    flex-direction: column;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;
    border-bottom: 1px solid var(--profile_process_border_color);
    padding-bottom: 15px;
  }

  .res_sidebar_toggle_btn {
    display: flex;
  }

  .pofile_sidebar_tab_list {
    margin-top: 30px;
  }

  .pofile_sidebar_tab_section {
    position: fixed;
    left: 0px;
    max-width: 200px;
    height: 100vh;
    top: 0px;
    background: var(--profile_process_bg_color);
    box-shadow: var(--profile_process_shadow_color);
    transform: translate(-100%);
    transition: 0.2s ease-in-out;
    z-index: 9999;
  }

  .pofile_sidebar_tab_section.active {
    transform: translate(0%);
    transition: 0.2s ease-in-out;
  }

  .profile_tabs_data_header {
    padding-bottom: 0px;
    border-bottom: unset;
    margin-bottom: 20px;
  }

  .profile_tabs_data_header h2 {
    font-size: 24px;
    line-height: 25px;
  }

}

@media(max-width:540px) {

  .pofile_sidebar_header span {
    font-size: 22px;
    line-height: 29px;
  }

  .pofile_sidebar_header h5 {
    font-size: 24px;
    line-height: 33px;
  }

  .res_sidebar_toggle_btn span {
    font-size: 16px;
    line-height: 21px;
  }

  .res_sidebar_toggle_btn i {
    font-size: 16px;
  }
}

/* datatable style start */

.datatable_parent {
  background-color: var(--primary-bg-color);
  border-radius: 8px;
  box-shadow: 0 0 0.375rem 0.25rem var(--primary-bg-color);
  padding: 20px 15px 20px 15px;

}

.dataTables_scroll {
  padding-top: 20px !important;
  padding-bottom: 20px !important;

}

.dataTables_scrollBody {
  border: unset !important;
}


button.dt-button.export-buttons {
  background: var(--primary-bg-color);
  color: var(--primary-text-color);
  border: unset;
  padding: 6px 21px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  line-height: 29px;
  margin: 0px !important;
}

button.dt-button.export-buttons:hover {
  border: unset !important;
}

button.dt-button.export-buttons i {
  padding-right: 8px;
  font-size: 16px;
}

.dt-button-background {
  background: transparent !important;
}

.dt-button-collection {
  border: 0 !important;
  border-radius: 0.375rem !important;
  padding: 0.3125rem 0px !important;
  width: auto !important;
  background: var(--primary-bg-color);
  box-shadow: 0 0 0.375rem 0.25rem var(--primary-bg-color) !important;
}

.dt-button-collection div button.dt-button {
  background: transparent !important;
  border: 0 !important;
  padding: 0.632rem 2rem 0.632rem 0rem !important;
  min-width: 8rem !important;
  text-align: left !important;
  margin: 0px !important;
}

.dt-button-collection div button.dt-button span {
  background: transparent !important;
  border: 0 !important;
  padding: 0.532rem 1.25rem !important;
  min-width: 8rem !important;
  text-align: left !important;
  font-size: 15px;
  font-weight: 500;
  color: #000;
}


.dt-button-collection div button.dt-button span i {
  padding-right: 5px;
}

.dataTables_length label select {
  padding: 9px 12px !important;
  border: 1px solid #000 !important;
  border-radius: 4px !important;
  color: #000;
  font-size: 16px !important;
  font-weight: 400;
  margin-right: 20px;
}

.dataTables_filter label input {
  padding: 9px 12px !important;
  border: 1px solid #000 !important;
  border-radius: 4px !important;
  color: #000;
  font-size: 16px !important;
  font-weight: 400;
  margin-left: 20px;
}

.dataTables_info {
  color: #000 !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  line-height: 29px !important;
}

.paginate_button.previous,
.paginate_button.next,
.paginate_button.first,
.paginate_button.last {
  background: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
  border: unset;
  padding: 3px 21px !important;
  border-radius: 4px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  line-height: 29px !important;
  margin: 0px !important;
}

.paginate_button.previous,
.paginate_button.next,
.paginate_button.first,
.paginate_button.last:hover {
  border: unset !important;
  color: #000 !important;
}

.dataTables_paginate span {
  margin: 0px 10px;
}

.dataTables_paginate span a {
  padding: 3px 15px !important;
  border-radius: 4px !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  line-height: 29px !important;
  margin: 0px !important;
  margin: 0px 5px !important;
}

#payment_history_previous {
  background: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
}

a#payment_history_next {
  background: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
}

div#payment_history_paginate span a {
  background: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
  box-shadow: 0 0.125rem 0.25rem 0 var(--primary-bg-color) !important;
}


#booking_history_previous {
  background: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
}

a#booking_history_next {
  background: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
}

div#booking_history_paginate span a {
  background: var(--primary-bg-color) !important;
  color: var(--primary-text-color) !important;
  box-shadow: 0 0.125rem 0.25rem 0 var(--primary-bg-color) !important;
}

/* Style the DataTable header */


table.dataTable thead tr th {
  border-color: #000 !important;
  font-size: 14px;
  color: var(--text-color-two);
  font-weight: 500;
  border-top: 1px solid var(--profile_process_border_color) !important;
  padding: 12px 15px;
  border-bottom: unset !important;
}

table.dataTable tbody tr {
  background: transparent !important;
  border: unset !important;
}

table.dataTable tbody tr td {
  background: transparent !important;
  padding: 15px 15px;
  border-color: var(--profile_process_border_color) !important;
}


/* datatable style end  */