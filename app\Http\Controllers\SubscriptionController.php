<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SubscriptionController extends Controller
{
    public function index(request $request)
    {
        $status = $request->query('status');

        if ($status) {
            $subscriptions = Subscription::where('status', $status)->get();
        } else {
            $subscriptions = Subscription::all();
        }
        return response()->json($subscriptions);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'package_type' => 'required',
            'duration' => 'required',
            'duration_count' => 'required',
            'price' => 'required|numeric',
            'currency' => 'required',
            'features' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $existingSubscription = Subscription::where('package_type', $request->input('package_type'))->first();

        if ($existingSubscription) {
            return response()->json(['error' => 'A subscription with the same package type already exists.'], 409);
        }

        $subscription = Subscription::create($request->all());
        return response()->json($subscription, 201);
    }

    public function show($id)
    {
        $subscription = Subscription::findOrFail($id);
       
        return response()->json($subscription);
    }

    public function update(Request $request, $id)
    {

        $subscription = Subscription::findOrFail($id);
        $subscription->update($request->all());
        return response()->json($subscription, 200);
    }

    public function destroy($id)
    {
        $subscription = Subscription::findOrFail($id);
        $subscription->delete();
        return response()->json(null, 204);
    }
    public function status($id)
    {
        $subscription = Subscription::findOrFail($id);
       
        $subscription->status = !$subscription->status; // Toggle status
        $subscription->save();
        return response()->json($subscription, 200);
    }
}
