@extends("dashboard.include.layout")

@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a>/ dashboard / destinations / category / List</a>
</div>
<div class="content-section-box">





    <div class="datatable_parent">
        <div class="d-flex w-100 mb-4 justify-content-end">
            <button class="custom_btn_2 d-flex align-items-center " tabindex="0" type="button"
                data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddDestinationCategory"><span>Add
                    Category</span></button>
        </div>
        <table id="destination_category_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">Image</th>
                    <th style="min-width: 330px;">Title</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($destinationCatList as $destinationCategory)
                <tr class="category_row">
                    <td></td>

                    <td>
                        <div style="width: 2.375rem;
                      height: 2.375rem;">
                            <img src="{{$destinationCategory ? asset('storage/destinations/'.$destinationCategory->cover_image) : ''}}"
                                class="w-100 h-100">
                        </div>
                    </td>

                    <td>{{ $destinationCategory->title }}</td>

                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $destinationCategory->id }}" type="button"
                                    class="delete_btn delete_destination_category"><i
                                        class="fas fa-trash-alt"></i>Delete</button>

                                <button class="update_btn edit_destination_btn" tabindex="0" type="button"
                                    data-bs-toggle="offcanvas"
                                    data-bs-target="#offcanvasUpdateCategory{{$destinationCategory->id}}"
                                    data-form-id="update_destination_category_{{$destinationCategory->id}}"
                                    data-id="{{$destinationCategory->id}}">
                                    <i class="far fa-edit"></i> Update
                                </button>
                            </div>
                        </div>
                        @include("dashboard.destinations.updateCategory")

                    </td>
                </tr>

                @endforeach
            </tbody>
        </table>
    </div>

</div>
@include("dashboard.destinations.addCategory")
@include("dashboard.destinations.deleteCategory")

@endsection
