<?php

namespace App\Models;

use App\Models\Destinations;
use App\Models\Subscribers;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'role',
        'email',
        'password',
        'is_verified',
        'profile_image',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function subscriber()
    {
        return $this->hasOne(Subscribers::class);
    }
    public function destinations()
    {
        return $this->hasMany(Destinations::class);
    }

    public function destinationIncludes()
    {
        return $this->hasMany(destination_includes::class, 'user_id');
    }
    public function destinationNotIncludes()
    {
        return $this->hasMany(destination_not_includes::class, 'user_id');
    }
    public function destinationExtras()
    {
        return $this->hasMany(destination_extras::class, 'user_id');
    }
}
