<?php

namespace App\Http\Controllers;

use App\Models\contact;
use Illuminate\Http\Request;

class contact<PERSON><PERSON>roller extends Controller
{

    public function getContact()
    {
        $contactRecords = contact::all();

        return view('dashboard.contact.contactRecords', ['contactRecords' => $contactRecords]);
    }
    public function destroy($id)
    {
        $contact = contact::findOrFail($id);
        $contact->delete();
        return response()->json(null, 200);
    }

    public function reply(request $request, $id)
    {
        $contactdata = contact::where('id', $request->id)->first();

        $responce = sendMail([
            'view' => 'emailTemplates.contact_reply_template',
            'to' => $contactdata->email,
            'subject' => 'Contact Reply',
            'data' => [
                'reply' => $request->message,

            ],
        ]);

        return response()->json($responce, 200);
    }

}
