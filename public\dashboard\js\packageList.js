//get subscrition list
document.addEventListener("DOMContentLoaded", function () {
    const pakagesList = document.querySelector(".agent_plan_list_box");
    if (!pakagesList) {
        return; // Exit if .agent_list is not found
    }
    let baseURL = window.location.origin;
    let apiEndpoint = "/api/subscriptions";
    let apiUrl = baseURL + apiEndpoint;

    fetch(apiUrl)
        .then((response) => {
            if (!response.ok) {
                throw new Error("Network response was not ok");
            }
            return response.json();
        })
        .then((subscriptionsData) => {
           

            // Clear existing list
            pakagesList.innerHTML = "";
            subscriptionsData.forEach((subscription) => {
               
                const featuresArray = JSON.parse(subscription.features);
                // console.log(featuresArray);
                // const featuresArray = subscription.features.split(",");
                const subscriptionBox = document.createElement("div");
                subscriptionBox.classList.add(
                    "single_plan_box",
                    "d-flex",
                    "w-100",
                    "flex-column"
                );
                
                subscriptionBox.innerHTML = `
                <div class="bg_box d-flex w-100 flex-column h-100 justify-content-between">
                    <div class="single_plan_box_data_header d-flex w-100 align-items-center justify-content-center flex-column text-center">
                        <label class="toogle_switch">
                            <input type="checkbox" class="package_switch_input" ${
                                subscription.status == 1 ? "checked" : ""
                            } data-id="${subscription.id}">
                            <span class="package_switch switch round" data-id="${
                                subscription.id
                            }"></span>
                        </label>
                        <h3>${subscription.package_type}</h3>
                        <span><strong>${subscription.currency} ${
                    subscription.price
                }</strong>/${subscription.duration}</span>
                    </div>
                    <ul class="single_plan_features_list">
                        ${featuresArray
                            .map(
                                (feature) => `
                            <li>
                                <small>${feature.value}</small>
                                <i class="fas fa-check"></i>
                            </li>
                        `
                            )
                            .join("")}
                    </ul>
                    <div class="single_plan_btn d-flex w-100 align-items-center justify-content-between">
                        <button data-id="${
                            subscription.id
                        }" type="button" class="custom_btn_2 update_package w-100" >update</button>
                        <button data-id="${
                            subscription.id
                        }" type="button" class="custom_btn_2 delete_package w-100" >Delete</button>
                    </div>
                </div>
            `;
                pakagesList.appendChild(subscriptionBox);
            });
            newPackageAdded = false;
        })
        .catch((error) => {
            console.error(
                "There was a problem with the fetch operation:",
                error.message
            );
        });
});

$(document).ready(function () {
    //add subscrition package
    $("#subscription-form").validate({
        rules: {
            package_type: {
                required: true,
            },
            duration: {
                required: true,
            },
            duration_count: {
                required: true,
            },
            price: {
                required: true,
            },
            currency: {
                required: true,
            },
            features: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_loader").addClass("d-flex").removeClass("d-none");
            //  Serialize form data
            var formData = $(form).serialize();

            let baseURL = window.location.origin;
            //   API endpoint path
            let apiEndpoint = "/api/subscriptions";
            //    Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;
            //   Send POST request using AJAX
            $.ajax({
                url: apiUrl,
                type: "POST",
                data: formData,
                dataType: "json",
                success: function (response) {
                    form.reset();
                    $(".form_loader").removeClass("d-flex").addClass("d-none");
                    // Close the offcanvas
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("offcanvasPackageList")
                    );
                    offcanvas.hide();
                    showAlert("Subscription created successfully!", "success");
                    $(".invalid-feedback").remove();
                    window.location.reload();
                },
                error: function (xhr, status, error) {
                    $(".form_loader").removeClass("d-flex").addClass("d-none");
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.error;
                        $(".invalid-feedback").remove();
                        $.each(errors, function (key, value) {
                            $("#" + key)
                                .addClass("is-invalid")
                                .after(
                                    '<div class="invalid-feedback">' +
                                        value[0] +
                                        "</div>"
                                );
                        });
                    } else if (xhr.status === 409) {
                        form.reset();

                        // Close the offcanvas
                        var offcanvas = bootstrap.Offcanvas.getInstance(
                            document.getElementById("offcanvasPackageList")
                        );
                        offcanvas.hide();
                        showAlert(
                            "A subscription with the same package type already exists.",
                            "error"
                        );
                    } else {
                        showAlert(
                            "An error occurred while processing the request.",
                            "error"
                        );
                    }
                },
            });
        },
    });
});

document.addEventListener("click", function (event) {
    if (event.target.classList.contains("delete_package")) {
        const subscriptionId = event.target.getAttribute("data-id");
        // console.log(subscriptionId);
        document
            .querySelector("#confirmDeleteBtn")
            .setAttribute("data-id", subscriptionId);
        var myModal = new bootstrap.Modal(
            document.getElementById("deleteConfirmationModal"),
            {}
        );
        myModal.show();
    }
});

// Add event listener to confirm delete button in the modal
document.addEventListener("DOMContentLoaded", function () {
    const confirmDeleteBtn = document.querySelector("#confirmDeleteBtn");
    if (!confirmDeleteBtn) {
        return; // Exit if .agent_list is not found
    }
    confirmDeleteBtn.addEventListener("click", function () {
        // Get the ID from the data-id attribute of the confirm delete button
        const getId = this.getAttribute("data-id");
        // console.log("Deleting subscription with ID:", getId);
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/subscriptions/${getId}`;
        let apiUrl = baseURL + apiEndpoint;

        $(".form_loader").addClass("d-flex").removeClass("d-none");
        // Send DELETE request using AJAX
        $.ajax({
            url: apiUrl,
            type: "DELETE",
            dataType: "json",
            success: function (response) {
                // Handle success response
                // console.log(response);
                showAlert("Subscription deleted successfully!", "success");
                $(".form_loader").removeClass("d-flex").addClass("d-none");
                // Reload the page or update the UI as needed
                document
                    .querySelectorAll(".delete_package")
                    .forEach((deleteBtn) => {
                        if (deleteBtn.getAttribute("data-id") === getId) {
                            deleteBtn.closest(".single_plan_box").remove();
                        }
                    });
                // Close the modal
                var myModal = bootstrap.Modal.getInstance(
                    document.getElementById("deleteConfirmationModal")
                );
                myModal.hide();
            },
            error: function (xhr, status, error) {
                // Handle error response
                console.error(xhr);
                showAlert(
                    "An error occurred while deleting subscription.",
                    "error"
                );
            },
        });
    });
});
document.addEventListener("click", function (event) {
    if (event.target.classList.contains("update_package")) {
        const subscriptionId = event.target.getAttribute("data-id");

        let baseURL = window.location.origin;
        let apiEndpoint = `/api/subscriptions/${subscriptionId}`;
        let apiUrl = baseURL + apiEndpoint;
        // Fetch subscription data using AJAX
        $.ajax({
            url: apiUrl,
            type: "GET",
            success: function (response) {
                const featuresArray = JSON.parse(response.features);
                const featuresString = featuresArray
                    .map((feature) => feature.value)
                    .join(",");

                console.log(response.id);
                // Populate the modal fields with subscription data
                document.getElementById("update_subscription_id").value =
                    response.id;
                document.getElementById("update_package_type").value =
                    response.package_type;
                document.getElementById("update_duration").value =
                    response.duration;
                document.getElementById("update_duration_count").value =
                    response.duration_count;
                document.getElementById("update_price").value = response.price;
                document.getElementById("update_currency").value =
                    response.currency;
                document.getElementById("update_features").value =
                    featuresString;

                // Show the offcanvas
                var offcanvas = new bootstrap.Offcanvas(
                    document.getElementById("updateSubscriptionModal")
                );
                offcanvas.show();
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                alert("An error occurred while fetching subscription data.");
            },
        });
    }
});

$(document).ready(function () {
    //update subscrition package
    $("#subscription-form-update").validate({
        rules: {
            package_type: {
                required: true,
            },
            duration: {
                required: true,
            },
            duration_count: {
                required: true,
            },
            price: {
                required: true,
            },
            currency: {
                required: true,
            },
            features: {
                required: true,
            },
        },

        submitHandler: function (form, event) {
            //   Prevent the default form submission behavior
            event.preventDefault();
            $(".form_loader").addClass("d-flex").removeClass("d-none");
            //  Serialize form data
            var formData = $(form).serialize();
            console.log(formData);
            // Dynamically retrieve base URL
            let baseURL = window.location.origin;
            // API endpoint path
            let id = $(form).find('[name="id"]').val();
            let apiEndpoint = "/api/subscriptions/" + id;

            // Concatenate base URL with API endpoint path
            let apiUrl = baseURL + apiEndpoint;

            $(".form_loader").addClass("d-flex").removeClass("d-none");
            // Send PUT request using AJAX

            $.ajax({
                url: apiUrl,
                type: "PUT",
                data: formData,
                success: function (response) {
                    // $("#subscription-form-update").reset();
                    $(".form_loader").removeClass("d-flex").addClass("d-none");

                    // Close the offcanvas
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("updateSubscriptionModal")
                    );
                    offcanvas.hide();
                    showAlert("updated successfully!", "success");
                    window.location.reload();
                },
                error: function (xhr, status, error) {
                    console.error(xhr);
                    $(".form_loader").removeClass("d-flex").addClass("d-none");
                    var offcanvas = bootstrap.Offcanvas.getInstance(
                        document.getElementById("updateSubscriptionModal")
                    );
                    offcanvas.hide();
                    showAlert(
                        "An error occurred while updating subscription.",
                        "error"
                    );
                },
            });
        },
    });
});

// Add event listener to toggle switches
document.addEventListener("change", function (event) {
    if (event.target.classList.contains("package_switch_input")) {
        const subscriptionId = event.target.getAttribute("data-id");
        const checked = event.target.checked;
        let baseURL = window.location.origin;
        let apiEndpoint = `/api/subscriptions/${subscriptionId}/status`;
        let apiUrl = baseURL + apiEndpoint;

        // Send PUT request to update status using AJAX
        $.ajax({
            url: apiUrl,
            type: "PUT",
            data: { status: checked },
            success: function (response) {
                // Show success message or update UI as needed
                showAlert("updated successfully !", "success");
            },
            error: function (xhr, status, error) {
                console.error(xhr);
                showAlert("There is a problem contact supprt !", "error");
            },
        });
    }
});
