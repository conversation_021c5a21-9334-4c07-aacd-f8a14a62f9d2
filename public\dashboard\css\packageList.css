    /* agent plan section css start */

    .agent_plan_list_box {
        margin: 15px -10px 0px -10px;
    }

    .single_plan_box {
        flex: 0 0 25%;
        max-width: 25%;
        padding: 15px 10px;
    }

    .single_plan_box_data_header {
        margin-bottom: 25px;
    }

    .single_plan_box_data_header h3 {
        font-style: normal;
        font-weight: 600;
        font-size: 22px;
        line-height: 40px;
        color: var(--main-text-color);
        padding-bottom: 8px;
        text-transform: capitalize;
        font-family: 'Poppins', sans-serif;
    }

    .single_plan_box_data_header span {
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 18px;
        letter-spacing: 0.08em;
        color: var(--main-text-color);
    }

    .single_plan_features_list {
        padding: 0px 0px 30px 0px;
        display: grid;
        gap: 17px;
    }

    .single_plan_features_list li {
        display: flex;
        padding-bottom: 17px;
        list-style: none;
        width: 100%;
        justify-content: space-between;
        align-items: center;
    }

    .single_plan_features_list li:last-child {
        padding-bottom: 0px;
        border-bottom: unset;
    }

    .single_plan_features_list li i {
        padding-left: 7px;
        color: var(--main-text-color);
        font-size: 13px;
    }

    .single_plan_features_list li small {
        font-style: normal;
        font-weight: 500;
        font-size: 14px;
        line-height: 16px;
        color: var(--main-text-color);
    }

    .single_plan_btn {
        margin-top: auto;
    }

    .single_plan_btn button:first-child {
        margin-right: 10px;
    }

    @media (max-width: 1200px) {
        .single_plan_box {
            flex: 0 0 33.33%;
            max-width: 33.33%;
        }
    }

    @media (max-width: 1024px) {
        .agent_plan_list_box {
            margin: 0 -8px;
        }

        .single_plan_box {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 10px 8px;
        }
    }

    @media (max-width: 768px) {
        .single_plan_box {
            flex: 0 0 100%;
            max-width: 100%;
        }
    }

    /* agent_plan section css end */