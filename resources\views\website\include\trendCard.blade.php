<!-- <li>
    <div class="packages_single_list d-flex flex-column w-100 h-100">
        <figure class="w-100 h-100"><img src="{{asset('website/images/tour1.png')}}"
                class="w-100 float-left h-100 object-fit-cover" alt="homebanner1" loading="lazy" /></figure>
        <div class="packages_single_list_content d-flex flex-column w-100">
            <div class="package_location d-flex align-items-center justify-content-between">
                <span class="d-flex align-items-center"><i class="far fa-calendar-check"></i>3 days - 3 nights</span>
                <span class="d-flex align-items-center"><i class="fas fa-map-marker-alt"></i> Europe</span>
            </div>
            <h3>Switzerland</h3>
            <div class="single_package_rating d-flex">
                <i class="far fa-star"></i>
                <i class="far fa-star"></i>
                <i class="far fa-star"></i>
                <i class="far fa-star"></i>
                <i class="far fa-star"></i>
            </div>
            <div class="packages_price d-flex align-items-center">
                <h6>850 $</h6>
                <h5 class="discount_on_price">333.50 $</h5>
            </div>

            <p>Nam exercitationem commodi et ducimus quia in dolore animi sit mollitia amet id quod eligendi.
                Et labore harum non nobis ipsum eum molestias mollitia et corporis praesentium a laudantium internos.
            </p>
            <div class="single_package_btn d-flex w-100">
                <a class="d-flex align-items-center justify-content-center">View Detail</a>
            </div>
        </div>
    </div>
</li> -->



@foreach($trendingDestination as $trendingDestination)

@php
$departureDatesTimesArray = explode(", ", $trendingDestination->departure_date_time);
$departureDateTimeObjects = [];
foreach ($departureDatesTimesArray as $dateTimeString) {
$dateTimeObject = \DateTime::createFromFormat('Y-m-d h:i A', $dateTimeString);
if ($dateTimeObject !== false) {
$departureDateTimeObjects[] = $dateTimeObject;
}
}

$currentDateTime = new \DateTime();
$nearestDateTime = null;
$nearestDiff = PHP_INT_MAX;
foreach ($departureDateTimeObjects as $dateTime) {
$diff = $dateTime->getTimestamp() - $currentDateTime->getTimestamp();
if ($diff > 0 && $diff < $nearestDiff) { $nearestDiff=$diff; $nearestDateTime=$dateTime; } }
    $nearestDateString=$nearestDateTime ? $nearestDateTime->format('Y-m-d h:i A') :
    '';
    @endphp
    <li>
        <div class="single_product">
            <div class="product_card w-100">
                <a href="{{ route('website.destination.detail', $trendingDestination->slug) }}"
                    class="destination_img w-100 position-relative">
                    <img width="300" height="300" src="{{$trendingDestination ? asset('storage/destinations/'.$trendingDestination->cover_img) : asset('website/images/logo.webp') }}"
                        class="w-100 h-100 object-fit-cover" alt="{{$trendingDestination->alt_text_cover_image}}"
                        loading="lazy" decoding="async" />
                    <div class="nearest_departure_date">
                        {{customDate($nearestDateString,'F d, Y')}}
                    </div>
                </a>
                <div class="product_content d-flex flex-column w-100">

                    <div class="shop_single_package_rating d-flex">
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                        <i class="far fa-star"></i>
                    </div>

                    <div class="shop_package_location d-flex align-items-start flex-column">
                        <span class="d-flex align-items-center"><i
                                class="far fa-calendar-check"></i>{{$trendingDestination->days}}
                            Days - {{$trendingDestination->nights}} Nights</span>
                        <span class="d-flex align-items-center"><i class="fas fa-map-marker-alt"></i>
                            {{$trendingDestination->destination_country}}</span>
                    </div>
                    <a href="{{ route('website.destination.detail', $trendingDestination->slug) }}"
                        class="product_name">{{$trendingDestination->title}}</a>
                    <div class="shop_short_des">{!! $trendingDestination->short_description !!}
                    </div>
                    <div class="price_tags d-flex flex-column w-100">
                        <div class="total_and_discount_price d-flex flex-column">
                            <span class="price_label">Per person</span>
                            <small class="orignal_price">From <span
                                    class="price_currency text-uppercase">{{$trendingDestination->currency}}</span>
                                {{$trendingDestination->final_price}}</small>

                        </div>
                        <div class="product_cart_btn">
                            <a href="{{ route('website.destination.detail', $trendingDestination->slug) }}"
                                class="d-flex align-items-center justify-content-center">View
                                Detail</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </li>
    @endforeach
