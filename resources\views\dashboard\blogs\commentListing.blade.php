@extends("dashboard.include.layout")


@section("wrapper")
<div class="pagination-list d-flex w-100">
    <a> / blog / comments</a>
</div>
<div class="content-section-box">

    <div class="datatable_parent">

        <table id="blog_listing" class="data_table display" style="width:100%">
            <thead>
                <tr>
                    <th style="min-width: 40px;"></th>
                    <th style="min-width: 100px;">Status</th>
                    <th style="min-width: 100px;">Name</th>
                    <th style="min-width: 100px;">Email</th>
                    <th style="min-width: 250px;">Message</th>
                    <th style="min-width: 250px;">Blog</th>
                    <th style="min-width: 16px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($blogCommentList as $blogCommentList)
                <tr class="comment_row">
                    <td></td>
                    <td>
                        <label class="toogle_switch mb-3">
                            <input type="checkbox" class="blog_comment_switch_input"
                                {{ $blogCommentList->status==1 ? "checked" : ""}} data-id="{{ $blogCommentList->id }}">
                            <span class="agent_switch switch round" data-id="{{ $blogCommentList->id }}"></span>
                        </label>
                    </td>
                    <td>{{ $blogCommentList->name }}</td>
                    <td>{{ $blogCommentList->email }}</td>
                    <td>{{ $blogCommentList->message }}</td>
                    <td>{{ $blogCommentList->blog->title }}</td>

                    <td>
                        <div class="form_action_box">
                            <button class="toggle-button">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <div class="action_btn_group actions gap-3" style="display: none;">
                                <button data-id="{{ $blogCommentList->id }}" type="button"
                                    class="delete_btn delete_blog_comment"><i
                                        class="fas fa-trash-alt"></i>Delete</button>

                            </div>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

</div>

@include("dashboard.blogs.deleteComment")

@endsection
